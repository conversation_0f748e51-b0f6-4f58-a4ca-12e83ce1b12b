/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.dto.pubc;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


/**
 * 管理台app版本删除dto
 * <AUTHOR>
 * @version $Id: XcmcAppVersionDeleteDTO.java, v 0.1 2019年11月18日 16时58分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class XcmcAppVersionDeleteDTO  implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** id */
    @NotEmpty(message = "id列表不能为空")
    private List<@NotBlank String> ids;

}
