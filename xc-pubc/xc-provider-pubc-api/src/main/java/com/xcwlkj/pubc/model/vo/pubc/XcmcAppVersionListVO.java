/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.vo.pubc;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 管理台app版本列表vo
 * <AUTHOR>
 * @version $Id: XcmcAppVersionListVO.java, v 0.1 2019年11月18日 16时14分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class XcmcAppVersionListVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** app版本列表 */
    private List<AppVersionItemVO> appVersionList;
    /** 总条数 */
    private Integer totalRows;

}
