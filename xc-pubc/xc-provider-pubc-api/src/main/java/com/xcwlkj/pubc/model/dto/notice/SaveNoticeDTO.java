/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2019 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.dto.notice;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.xcwlkj.base.dto.BaseVo;

/**
 * 通知公告
 * <AUTHOR>
 * @version $Id: SaveNoticeDTO.java, v 0.1 2019年06月01日 下午09:23:30 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaveNoticeDTO  extends BaseVo {
	/** 序列id */
	private static final long serialVersionUID = 1L;
	
					/** 标题 */
				private String title;
				/** 公告类型(1通知 2公告) */
				private Integer type;
				/** 摘要 */
				private String summary;
				/** 公告内容 */
				private String content;
				/** 公告状态(1正常 0关闭) */
				private Integer status;
    /** 部门id */
    private String            departmentId;
				/** 备注 */
				private String remark;
							
}
