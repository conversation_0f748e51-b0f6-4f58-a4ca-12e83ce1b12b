/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.hystrix;

import com.xcwlkj.pubc.model.dto.pubc.WhiteIpDTO;
import com.xcwlkj.pubc.service.IpLimiterFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-06-19
 */
@Component
public class IpLimiterFeignHystrix implements IpLimiterFeignApi {

    @Override
    public Wrapper<Integer> hitCheckWhiteIp(WhiteIpDTO whiteIpDTO) {
        return WrapMapper.error();
    }
}
