/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.hystrix;

import org.springframework.stereotype.Component;

import com.xcwlkj.pubc.model.dto.robot.ChatRobotMsgDTO;
import com.xcwlkj.pubc.service.DingtalkFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @version $Id: DingtalkFeignHystrix.java, v 0.1 2018年8月27日 上午11:09:59 danfeng.zhou Exp $
 */
@Slf4j
@Component
public class DingtalkFeignHystrix implements DingtalkFeignApi {

    /**
     * @see com.xcwlkj.service.DingtalkFeignApi#sendChatRobotMsg(com.xcwlkj.model.dto.robot.ChatRobotMsgDTO)
     */
    @Override
    public Wrapper<Boolean> sendChatRobotMsg(final ChatRobotMsgDTO uacUserReqDto) {
        log.error("sendChatRobotMsg - 服务降级. uacUserReqDto={}", uacUserReqDto);
        return WrapMapper.error();
    }
}
