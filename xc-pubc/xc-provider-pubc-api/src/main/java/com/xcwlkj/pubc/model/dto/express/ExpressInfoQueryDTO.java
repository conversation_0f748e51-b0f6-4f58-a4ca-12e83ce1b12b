/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.dto.express;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.Data;


/**
 * 快递信息请求
 * <AUTHOR>
 *
 */
@Data
public class ExpressInfoQueryDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	/** 快递单号 */
	@NotBlank(message = " 快递单号不能为空")
	private String no;
	/** 快递公司编码 */
	private String type;
}
