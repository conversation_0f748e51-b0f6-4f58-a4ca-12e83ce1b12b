/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.dto.attachment;

import com.xcwlkj.base.dto.BaseVo;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 附件定义表
 * <AUTHOR>
 * @version $Id: SaveAttachmentDefineDTO.java, v 0.1 2019年09月04日 12时04分 XcDev Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SaveAttachmentDefineDTO extends BaseVo {
	/** 序列id */
	private static final long serialVersionUID = 1L;
    /** 顺序 */
    private Integer            orderSeq;
    /** 业务类型 */
    private String            busiType;
    /** 附件种类代码 */
    private String            attachmentCode;
    /** 附件种类名称 */
    private String            attachmentName;
    /** 必传数量 */
    private Integer            requireNum;
    /** 是否必须上传 */
    private String            isRequire;
    /** 附件支持类型 */
    private String            attachementApplyType;

}
