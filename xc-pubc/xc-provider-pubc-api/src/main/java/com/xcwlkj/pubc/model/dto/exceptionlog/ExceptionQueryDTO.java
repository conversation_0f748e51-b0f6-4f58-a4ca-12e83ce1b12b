/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.dto.exceptionlog;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xcwlkj.base.dto.BaseQuery;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * <AUTHOR>
 * @version $Id: ExceptionQueryDto.java, v 0.1 2018年8月27日 上午10:47:29 danfeng.zhou Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExceptionQueryDTO extends BaseQuery {

    /**  */
    private static final long serialVersionUID = 5503362075905496246L;
    /**
     * 操作用户名称
     */
    private String            creator;
    /**
     * 异常原因
     */
    private String            exceptionCause;

    /**
     * 异常栈信息
     */
    private String            exceptionStack;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date              startQueryTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date              endQueryTime;
}
