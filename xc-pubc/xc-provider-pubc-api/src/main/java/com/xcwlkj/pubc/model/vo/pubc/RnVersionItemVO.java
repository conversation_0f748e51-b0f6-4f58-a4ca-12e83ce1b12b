/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.vo.pubc;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 管理台rn版本列表vo
 * <AUTHOR>
 * @version $Id: RnVersionItemVO.java, v 0.1 2019年11月18日 17时17分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class RnVersionItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** rn版本号 */
    private String rnVersion;
    /** 生效类型 */
    private String effectType;
    /** 是否强制更新 */
    private String isMandatory;
    /** 是否提示 */
    private String isPrompt;
    /** 指定更新人员姓名 */
    private String designatedUserName;
    /** id */
    private String id;

}
