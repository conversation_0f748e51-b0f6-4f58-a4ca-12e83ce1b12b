/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.hystrix;

import org.springframework.stereotype.Component;

import com.xcwlkj.pubc.model.dto.express.ExpressInfoQueryDTO;
import com.xcwlkj.pubc.model.vo.ExpressInfoQueryVO;
import com.xcwlkj.pubc.service.ExpressInfoSysFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @version $Id: ExpressInfoSysFeignHystrix.java, v 0.1 2019年10月03日 11时23分 xcwlkj.com Exp $
 */
@Slf4j
@Component
public class ExpressInfoSysFeignHystrix implements ExpressInfoSysFeignApi{


	@Override
	public Wrapper<ExpressInfoQueryVO> expressInfoQuery(ExpressInfoQueryDTO dto) {
		 log.error("expressInfoQuery - 服务降级. expressInfoQueryDTO={}", dto);
	     return WrapMapper.error();
	}
}
