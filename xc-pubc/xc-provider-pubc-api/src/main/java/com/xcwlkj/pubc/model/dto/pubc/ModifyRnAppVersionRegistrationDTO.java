/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.dto.pubc;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.base.dto.BaseVo;


/**
 * rn_app_version_registration
 * <AUTHOR>
 * @version $Id: ModifyRnAppVersionRegistrationDTO.java, v 0.1 2019年07月03日 13时36分 XcDev Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ModifyRnAppVersionRegistrationDTO extends BaseVo {
	/** 序列id */
	private static final long serialVersionUID = 1L;
    /** app类型 */
    private String            appType;
    /** app版本号 */
    private String            appVersion;
    /** 版本号 */
    private String            rnVersion;
    /** 生效类型 */
    private String            effectType;
    /** 是否强制更新 */
    private String            isMandatory;
    /** 是否提示 */
    private String            isPrompt;
    /** 更新地址 */
    private String            url;
    /** 更新内容 */
    private String            updateContent;
								
}
