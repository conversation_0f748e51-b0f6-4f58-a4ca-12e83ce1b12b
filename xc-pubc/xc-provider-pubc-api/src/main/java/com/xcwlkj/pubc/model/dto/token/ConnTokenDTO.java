/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.dto.token;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 校验连接tokendto
 * <AUTHOR>
 * @version $Id: ConnTokenDTO.java, v 0.1 2020年05月05日 16时14分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ConnTokenDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 连接token */
    @NotBlank(message = "连接token不能为空")
    private String connToken;
    /** 渠道 */
    @NotBlank(message = "渠道不能为空")
    private String channel;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
