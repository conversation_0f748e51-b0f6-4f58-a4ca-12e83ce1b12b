/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.xcwlkj.core.annotation.mock.YapiMock;
import com.xcwlkj.pubc.model.dto.token.ConnTokenDTO;
import com.xcwlkj.pubc.model.dto.token.GetLoginTokenDTO;
import com.xcwlkj.pubc.model.dto.token.LoginTokenDTO;
import com.xcwlkj.pubc.model.vo.token.ConnTokenVO;
import com.xcwlkj.pubc.model.vo.token.GetLoginTokenVO;
import com.xcwlkj.pubc.model.vo.token.LoginTokenVO;
import com.xcwlkj.pubc.service.hystrix.TokenFeignHystrix;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * 
 * <AUTHOR>
 * @version $Id: TokenFeignApi.java, v 0.1 2020年05月05日 14时54分 xcwlkj.com Exp $
 */
@FeignClient(value = "eeip-pubc-service" , contextId = "TokenFeignApi", fallback = TokenFeignHystrix.class)
public interface TokenFeignApi {

   
	/**
	 * 获取登录token
	 * @param getLoginTokenDto
	 * @return
	 */
	@YapiMock(projectId="133", returnClass = GetLoginTokenVO.class)
    @PostMapping(value = "/sys/pubc/token/getLoginToken")
    Wrapper<GetLoginTokenVO> getLoginToken(@RequestBody GetLoginTokenDTO getLoginTokenDto);
	/**
	 * 校验连接token
	 * @param connTokenDto
	 * @return
	 */
	@YapiMock(projectId="133", returnClass = ConnTokenVO.class)
    @PostMapping(value = "/sys/pubc/token/checkConnToken")
    Wrapper<ConnTokenVO> checkConnToken(@RequestBody ConnTokenDTO connTokenDto);
	/**
	 * 校验登录token
	 * @param loginTokenDto
	 * @return
	 */
	@YapiMock(projectId="133", returnClass = LoginTokenVO.class)
    @PostMapping(value = "/sys/pubc/token/checkLoginToken")
    Wrapper<LoginTokenVO> checkLoginToken(@RequestBody LoginTokenDTO loginTokenDto);
	/**
	 * 获取用户登陆信息
	 * @param getLoginUserDto
	 * @return
	 */
	@YapiMock(projectId="133", returnClass = Void.class)
    @PostMapping(value = "/sys/pubc/token/queryLoginUser")
    Wrapper<LoginTokenVO> queryLoginUser();
}




