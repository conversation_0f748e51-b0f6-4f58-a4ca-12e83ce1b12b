/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2015-2015 All Rights Reserved.
 */
package com.xcwlkj.pubc.share.seq;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @version $Id: Sequence.java, v 0.1 2015年5月7日 下午10:43:18 admoy Exp $
 */
public class Sequence implements Serializable {

    /**  */
    private static final long serialVersionUID = 1628391565721055393L;
    /** sequence开始值 */
    private long              beginValue;
    /** sequence执行次数 */
    private long              executeNum       = 0;

    /**
     * Getter method for property <tt>beginValue</tt>.
     * 
     * @return property value of beginValue
     */
    public long getBeginValue() {
        return beginValue;
    }

    /**
     * Setter method for property <tt>beginValue</tt>.
     * 
     * @param beginValue value to be assigned to property beginValue
     */
    public void setBeginValue(long beginValue) {
        this.beginValue = beginValue;
    }

    /**
     * Getter method for property <tt>executeNum</tt>.
     * 
     * @return property value of executeNum
     */
    public long getExecuteNum() {
        return executeNum;
    }

    /**
     * Setter method for property <tt>executeNum</tt>.
     * 
     * @param executeNum value to be assigned to property executeNum
     */
    public void setExecuteNum(long executeNum) {
        this.executeNum = executeNum;
    }

}
