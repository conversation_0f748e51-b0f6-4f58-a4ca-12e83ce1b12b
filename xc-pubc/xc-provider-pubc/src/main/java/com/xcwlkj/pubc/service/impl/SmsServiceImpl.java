/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.xcwlkj.base.constant.GlobalConstant;
import com.xcwlkj.pubc.config.PubcBusiCacheInitConfig;
import com.xcwlkj.pubc.exceptions.PubcBusiErrorCode;
import com.xcwlkj.pubc.exceptions.PubcBusiException;
import com.xcwlkj.pubc.service.SmsService;
import com.xcwlkj.util.CheckParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 短信发送实现
 * <AUTHOR>
 * @version $Id: SmsServiceImpl.java, v 0.1 2018年8月28日 下午5:58:26 danfeng.zhou Exp $
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {
    @Autowired
    private IAcsClient iAcsClient;
    @Resource
    private PubcBusiCacheInitConfig pubcBusiCacheInitConfig;
    @Value("${spring.profiles.active}")
    private String     profile;
    
    @Override
    public SendSmsResponse sendSms(SendSmsRequest sendSmsRequest) {
        checkParam(sendSmsRequest);
        SendSmsResponse acsResponse;
        try {
            if (GlobalConstant.DEV_PROFILE.equals(profile)) {
                log.error("dev环境不发送短信, 验证码={}", sendSmsRequest.getTemplateCode());
                return new SendSmsResponse();
            }
            if (GlobalConstant.TEST_PROFILE.equals(profile)) {
                log.error("test环境不发送短信", sendSmsRequest.getTemplateCode());
                return new SendSmsResponse();
            }
            acsResponse = iAcsClient.getAcsResponse(sendSmsRequest);
        } catch (ClientException e) {
            log.error("send sms message error={}", e.getMessage(), e);
            throw new PubcBusiException(PubcBusiErrorCode.SEND_SMS_FAIL, e);
        }
        log.info("send sms message OK acsResponse={}", acsResponse);
        return acsResponse;
    }
    
    /**
     * 参数验证
     * @param sendSmsRequest
     */
    private void checkParam(SendSmsRequest sendSmsRequest) {
        String templateCode = sendSmsRequest.getTemplateCode();
        String signName = sendSmsRequest.getSignName();
        if (StringUtils.isBlank(signName)) {
            sendSmsRequest.setSignName(pubcBusiCacheInitConfig.getAliyun().getSms().getSignName());
        }
        String templateParam = sendSmsRequest.getTemplateParam();
        String phoneNumbers = sendSmsRequest.getPhoneNumbers();
        CheckParamUtil.checkStringNotBlank(templateCode, "短信模板不能为空");
        CheckParamUtil.checkStringNotBlank(phoneNumbers, "手机号码不能为空");
        CheckParamUtil.checkStringNotBlank(templateParam, "短信内容不能为空");
    }
}
