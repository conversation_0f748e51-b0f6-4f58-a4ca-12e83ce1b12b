/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.xcwlkj.base.mybatis.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 附件定义表
 * 
 * <AUTHOR>
 * @version $Id: InitAttachmentDefineReqModel.java, v 0.1 2019年09月04日 12时04分 XcDev Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "attachment_define")
public class AttachmentDefine extends BaseEntity {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 顺序 */
    @Column(name = "order_seq")
    private Integer            orderSeq;
    /** 业务类型 */
    @Column(name = "busi_type")
    private String            busiType;
    /** 附件种类代码 */
    @Column(name = "attachment_code")
    private String            attachmentCode;
    /** 附件种类名称 */
    @Column(name = "attachment_name")
    private String            attachmentName;
    /** 必传数量 */
    @Column(name = "require_num")
    private Integer            requireNum;
    /** 是否必须上传 */
    @Column(name = "is_require")
    private String            isRequire;
    /** 附件支持类型 */
    @Column(name = "attachement_apply_type")
    private String            attachementApplyType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}




