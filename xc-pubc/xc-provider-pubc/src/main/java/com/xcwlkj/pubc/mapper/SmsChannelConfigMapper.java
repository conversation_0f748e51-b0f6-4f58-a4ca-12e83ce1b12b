/**
 * xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.pubc.model.domain.SmsChannelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * sms_channel_config数据库操作
 * <AUTHOR>
 * @version $Id: InitSmsChannelConfigMapper.java, v 0.1 2019年12月30日 17时04分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface SmsChannelConfigMapper extends MyMapper<SmsChannelConfig> {

    /**
     * 根据短信渠道查询数据
     * @param channel
     * @return
     */
    SmsChannelConfig selectByChannel(String channel);

    /**
	 * 分页查询sms_channel_config
	 * 
	 * @param example
	 * @return
	 */
	List<SmsChannelConfig> pageList(SmsChannelConfig example);
}
