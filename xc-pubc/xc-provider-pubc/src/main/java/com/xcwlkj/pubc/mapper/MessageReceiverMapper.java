/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.pubc.model.domain.MessageReceiver;



/**
 * 消息接收者数据库操作
 * <AUTHOR>
 * @version $Id: InitMessageReceiverReqModel.java, v 0.1 2019年10月17日 11时43分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface MessageReceiverMapper extends MyMapper<MessageReceiver> {

    /**
	 * 分页查询消息接收者
	 * 
	 * @param example
	 * @return
	 */
	List<MessageReceiver> pageList(MessageReceiver example);
}
