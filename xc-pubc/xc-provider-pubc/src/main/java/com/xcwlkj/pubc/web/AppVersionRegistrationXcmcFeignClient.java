/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.web;


import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.pubc.model.dto.pubc.XcmcAppVersionAddDTO;
import com.xcwlkj.pubc.service.AppVersionRegistrationService;
import com.xcwlkj.pubc.service.AppVersionRegistrationXcmcFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.pubc.model.dto.pubc.XcmcAppVersionListDTO;
import com.xcwlkj.pubc.model.vo.pubc.XcmcAppVersionListVO;
import com.xcwlkj.pubc.model.dto.pubc.XcmcAppVersionModifyDTO;
import com.xcwlkj.pubc.model.dto.pubc.XcmcAppVersionDeleteDTO;
import com.xcwlkj.pubc.model.dto.pubc.XcmcAppVersionDetailDTO;
import com.xcwlkj.pubc.model.vo.pubc.XcmcAppVersionDetailVO;
import com.xcwlkj.pubc.model.dto.pubc.XcmcAppVersionDesignatedUserDTO;
import org.springframework.validation.annotation.Validated;
import com.xcwlkj.pubc.model.vo.pubc.XcmcAppVersionDesignatedUserVO;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * AppVersionRegistration接口
 * <AUTHOR>
 * @version $Id: AppVersionRegistrationXcmcFeignClient.java, v 0.1 2019年11月18日 15时51分 xcwlkj.com Exp $
 */
@RestController
public class AppVersionRegistrationXcmcFeignClient extends BaseFeignClient implements AppVersionRegistrationXcmcFeignApi{
	@Resource
	private AppVersionRegistrationService appVersionRegistrationService;
    
    /** 
     * @see com.xcwlkj.service.AppVersionRegistrationXcmcFeignApi#xcmcAppVersionAdd(com.xcwlkj.model.dto.pubc.XcmcAppVersionAddDTO)
     */
	@Override
	public Wrapper<Void> xcmcAppVersionAdd(@RequestBody @Validated XcmcAppVersionAddDTO dto) {
		logger.info("管理台app版本增加XcmcAppVersionAddDTO={}", dto);
		
		appVersionRegistrationService.xcmcAppVersionAdd(dto);
		logger.info("xcmcAppVersionAdd - 管理台app版本增加. [OK] ");
		return WrapMapper.ok();
	}
    /** 
     * @see com.xcwlkj.service.AppVersionRegistrationXcmcFeignApi#xcmcAppVersionList(com.xcwlkj.model.dto.pubc.XcmcAppVersionListDTO)
     */
	@Override
	public Wrapper<XcmcAppVersionListVO> xcmcAppVersionList(@RequestBody @Validated XcmcAppVersionListDTO dto) {
		logger.info("管理台app版本列表XcmcAppVersionListDTO={}", dto);
		XcmcAppVersionListVO result = appVersionRegistrationService.xcmcAppVersionList(dto);
		logger.info("xcmcAppVersionList - 管理台app版本列表. [OK] XcmcAppVersionListVO={}", result);
		return WrapMapper.ok(result);
	}
    /** 
     * @see com.xcwlkj.service.AppVersionRegistrationXcmcFeignApi#xcmcAppVersionModify(com.xcwlkj.model.dto.pubc.XcmcAppVersionModifyDTO)
     */
	@Override
	public Wrapper<Void> xcmcAppVersionModify(@RequestBody @Validated XcmcAppVersionModifyDTO dto) {
		logger.info("管理台app版本修改XcmcAppVersionModifyDTO={}", dto);
		appVersionRegistrationService.xcmcAppVersionModify(dto);
		logger.info("xcmcAppVersionModify - 管理台app版本修改. [OK] ");
		return WrapMapper.ok();
	}
    /** 
     * @see com.xcwlkj.service.AppVersionRegistrationXcmcFeignApi#xcmcAppVersionDelete(com.xcwlkj.model.dto.pubc.XcmcAppVersionDeleteDTO)
     */
	@Override
	public Wrapper<Void> xcmcAppVersionDelete(@RequestBody @Validated XcmcAppVersionDeleteDTO dto) {
		logger.info("管理台app版本删除XcmcAppVersionDeleteDTO={}", dto);
		appVersionRegistrationService.xcmcAppVersionDelete(dto);
		logger.info("xcmcAppVersionDelete - 管理台app版本删除. [OK] ");
		return WrapMapper.ok();
	}
    /** 
     * @see com.xcwlkj.service.AppVersionRegistrationXcmcFeignApi#xcmcAppVersionDetail(com.xcwlkj.model.dto.pubc.XcmcAppVersionDetailDTO)
     */
	@Override
	public Wrapper<XcmcAppVersionDetailVO> xcmcAppVersionDetail(@RequestBody @Validated XcmcAppVersionDetailDTO dto) {
		logger.info("管理台app版本详情XcmcAppVersionDetailDTO={}", dto);
		XcmcAppVersionDetailVO result = appVersionRegistrationService.xcmcAppVersionDetail(dto);
		logger.info("xcmcAppVersionDetail - 管理台app版本详情. [OK] XcmcAppVersionDetailVO={}", result);
		return WrapMapper.ok(result);
	}
    /** 
     * @see com.xcwlkj.service.AppVersionRegistrationXcmcFeignApi#xcmcAppVersionDesignatedUser(com.xcwlkj.model.dto.pubc.XcmcAppVersionDesignatedUserDTO)
     */
	@Override
	public Wrapper<XcmcAppVersionDesignatedUserVO> xcmcAppVersionDesignatedUser(@RequestBody @Validated XcmcAppVersionDesignatedUserDTO dto) {
		logger.info("指定更新人员XcmcAppVersionDesignatedUserDTO={}", dto);
		XcmcAppVersionDesignatedUserVO result = appVersionRegistrationService.xcmcAppVersionDesignatedUser(dto);
		logger.info("xcmcAppVersionDesignatedUser - 指定更新人员. [OK] XcmcAppVersionDesignatedUserVO={}", result);
		return WrapMapper.ok(result);
	}
}



