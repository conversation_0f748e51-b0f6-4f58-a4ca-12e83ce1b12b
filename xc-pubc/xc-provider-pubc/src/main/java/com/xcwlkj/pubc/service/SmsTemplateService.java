/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.service;

import com.xcwlkj.pubc.model.dto.sms.SmsTemplateListDTO;
import com.xcwlkj.pubc.model.vo.sms.SmsTemplateListVO;
import org.springframework.stereotype.Service;
import com.xcwlkj.base.IService;
import com.xcwlkj.pubc.model.domain.SmsTemplate;


/**
 * sms_template服务
 * <AUTHOR>
 * @version $Id: SmsTemplateService.java, v 0.1 2020年01月03日 18时30分 xcwlkj.com Exp $
 */
@Service
public interface SmsTemplateService extends IService<SmsTemplate> {


    /**
     * 根据模板编号查询数据
     * @param num 模板编号
     * @return
     */
    SmsTemplate selectByNum(String num);

    /**
     * 短信模板列表
     * @param dto
     * @return
     */
    SmsTemplateListVO templateList(SmsTemplateListDTO dto);
}