/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.base.BaseService;
import com.xcwlkj.pubc.mapper.SystemConfigMapper;
import com.xcwlkj.pubc.model.domain.SystemConfig;
import com.xcwlkj.console.model.dto.systemconfig.ModifySystemConfigDTO;
import com.xcwlkj.console.model.dto.systemconfig.QuerySystemConfigDTO;
import com.xcwlkj.console.model.dto.systemconfig.SaveSystemConfigDTO;
import com.xcwlkj.console.model.vo.systemconfig.QuerySystemConfigListVO;
import com.xcwlkj.console.model.vo.systemconfig.SystemConfigDetailVO;
import com.xcwlkj.console.model.vo.systemconfig.SystemConfigSummaryVO;
import com.xcwlkj.pubc.service.SystemConfigService;
import com.xcwlkj.pubc.share.seq.SequenceEnum;
import com.xcwlkj.pubc.share.seq.service.SequenceService;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.CheckParamUtil;
import com.xcwlkj.utils.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * system_config服务
 * <AUTHOR>
 * @version $Id: SystemConfigService.java, v 0.1 2019年08月25日 11时32分 XcDev Exp $
 */
@Service("systemConfigService")
@Transactional(rollbackFor = Exception.class)
public class SystemConfigServiceImpl extends BaseService<SystemConfig> implements SystemConfigService  {
	/** system_config库表操作 */
    @Resource
    private SystemConfigMapper modelMapper;
    @Autowired
    private SequenceService sequenceService;


    @Override
    public QuerySystemConfigListVO querySystemConfigListWithPage(QuerySystemConfigDTO systemConfigDto) {

        QuerySystemConfigListVO result = new QuerySystemConfigListVO();
        PageHelper.startPage(systemConfigDto.getPageNum(), systemConfigDto.getPageSize());
        SystemConfig systemConfig = new SystemConfig();
        systemConfig.setConfigType(systemConfigDto.getConfigType());
        systemConfig.setConfigName(systemConfigDto.getConfigName());
        systemConfig.setConfigKey(systemConfigDto.getConfigKey());
        List<SystemConfig> list = modelMapper.selectSystemConfigs(systemConfig);
        List<SystemConfigSummaryVO> systemConfigSummaryVOList = new ArrayList<>();
        BeanUtil.copyListProperties(list,systemConfigSummaryVOList,SystemConfigSummaryVO.class);
        PageInfo<SystemConfig> pageInfo = new PageInfo<>(list);
        result.setSystemConfigSummaryVOList(systemConfigSummaryVOList);
        result.setTotalRows((int)pageInfo.getTotal());
        return result;
    }

    @Override
    public void modifySystemConfigById(ModifySystemConfigDTO modifySystemConfigDto) {
        SystemConfig systemConfig = new SystemConfig();
        BeanUtil.copyProperties(modifySystemConfigDto, systemConfig);
        modelMapper.updateByPrimaryKeySelective(systemConfig);
    }

    @Override
    public void saveSystemConfig(SaveSystemConfigDTO saveSystemConfigDto) {
        XcSsoUser xcSsoUser = RequestUtil.getLoginUser();
        SystemConfig systemConfig = new SystemConfig();
        BeanUtil.copyProperties(saveSystemConfigDto, systemConfig);
        String nbr = sequenceService.createKeyNo(SequenceEnum.SYSTEM_CONFIG);
        systemConfig.setId(nbr);
        systemConfig.setCreatedBy(xcSsoUser.getUserName());
        systemConfig.setCreatedTime(new Date());
        systemConfig.setCreatorId(xcSsoUser.getUserId());
        systemConfig.setLastOperatorId(xcSsoUser.getUserId());;
        systemConfig.setLastOperator(xcSsoUser.getUserName());
        systemConfig.setLastOperatorTime(new Date());
        modelMapper.insert(systemConfig);
    }

    @Override
    public void deleteSystemConfigById(String systemConfigIds) {
        String[] systemConfigIdArray = systemConfigIds.split(",");
        for (int i = 0; i < systemConfigIdArray.length; i++) {
            modelMapper.deleteByPrimaryKey(systemConfigIdArray[i]);
        }
    }

    @Override
    public SystemConfigDetailVO getSystemConfigById(String id) {
        SystemConfigDetailVO result = new SystemConfigDetailVO();
        SystemConfig systemConfig = modelMapper.selectByPrimaryKey(id);
        BeanUtil.copyProperties(systemConfig, result);
        return result;
    }

    /**
     */
    @Override
    public String queryConfigValueByKey(String configKey) {
        //TODO 后续redis缓存
        SystemConfig systemConfig = modelMapper.queryConfigValueByKey(configKey);
        CheckParamUtil.checkObjectNotNull(systemConfig, "根据key:" + configKey + "对应的配置信息");
        return systemConfig.getConfigValue();
    }
}