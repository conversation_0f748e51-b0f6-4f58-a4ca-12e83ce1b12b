/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.pubc.model.domain.AttachmentDefine;



/**
 * 附件定义表数据库操作
 * <AUTHOR>
 * @version $Id: InitAttachmentDefineReqModel.java, v 0.1 2019年09月04日 12时04分 XcDev Exp $
 */
@Mapper
@Repository
public interface AttachmentDefineMapper extends MyMapper<AttachmentDefine> {

    /**
	 * 分页查询附件定义表
	 * 
	 * @param example
	 * @return
	 */
	List<AttachmentDefine> pageList(AttachmentDefine example);
}
