package com.xcwlkj.pubc.aop;

import com.xcwlkj.resourcecenter.service.BasicDeviceFeignApi;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
@Slf4j
public class DeviceIdValidationAspect {

    @Resource
    private BasicDeviceFeignApi basicDeviceFeignApi;

    @Before("execution(* com.xcwlkj.pubc.facade.remote.UserController.userDetail(..))")
    public void validateDeviceId() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String deviceId = request.getParameter("deviceId");
        if (deviceId == null || deviceId.isEmpty()) {
            throw new IllegalArgumentException("deviceId不能为空");
        }
        Wrapper<Boolean> wrapper = basicDeviceFeignApi.xlhCheck(deviceId);
        if (!wrapper.getResult()){
            throw new IllegalArgumentException("该设备不存在, deviceId=" + deviceId);
        }
    }
}
