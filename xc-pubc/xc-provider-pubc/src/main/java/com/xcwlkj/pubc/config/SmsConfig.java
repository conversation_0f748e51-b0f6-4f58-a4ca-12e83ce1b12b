/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $Id: SmsConfig.java, v 0.1 2018年8月28日 下午3:46:55 danfeng.zhou Exp $
 */
@Slf4j
@Configuration
public class SmsConfig {
    @Resource
    private PubcBusiCacheInitConfig pubcBusiCacheInitConfig;

    /**
     * Acs client acs client.
     *
     * @return the acs client
     * @throws ClientException the client exception
     */
    @Bean
    public IAcsClient acsClient() throws ClientException {
        log.info("SMS Bean IAcsClient Start");
        IClientProfile profile = DefaultProfile.getProfile(pubcBusiCacheInitConfig.getAliyun().getSms().getRegionId(), pubcBusiCacheInitConfig.getAliyun().getKey().getAccessKeyId(),
                pubcBusiCacheInitConfig.getAliyun().getKey().getAccessKeySecret());
        DefaultProfile.addEndpoint(pubcBusiCacheInitConfig.getAliyun().getSms().getEndpointName(), pubcBusiCacheInitConfig.getAliyun().getSms().getRegionId(), pubcBusiCacheInitConfig.getAliyun().getSms().getProduct(),
                pubcBusiCacheInitConfig.getAliyun().getSms().getDomain());
        DefaultAcsClient defaultAcsClient = new DefaultAcsClient(profile);
        log.info("加载SMS Bean IAcsClient OK");
        return defaultAcsClient;
    }

}
