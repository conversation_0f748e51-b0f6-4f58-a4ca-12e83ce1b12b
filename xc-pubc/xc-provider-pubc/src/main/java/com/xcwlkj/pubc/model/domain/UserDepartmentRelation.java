/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * user_department_relation
 * 
 * <AUTHOR>
 * @version $Id: UserDepartmentRelation.java, v 0.1 2020年11月13日 14时42分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "user_department_relation")
public class UserDepartmentRelation implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 关系id */
    @Id
    @Column(name = "id")
    private String            id;
    /** 版本号 */
    @Column(name = "version")
    private Integer            version;
    /** 用户id */
    @Column(name = "user_id")
    private String            userId;
    /** 部门id */
    @Column(name = "department_id")
    private String            departmentId;
    /** 创建时间 */
    @Column(name = "created_time")
    private Date            createdTime;
    /** 最后操作时间 */
    @Column(name = "last_operator_time")
    private Date            lastOperatorTime;
    /** 创建人 */
    @Column(name = "created_by")
    private String            createdBy;
    /** 创建人ID */
    @Column(name = "creator_id")
    private String            creatorId;
    /** 最近操作人 */
    @Column(name = "last_operator")
    private String            lastOperator;
    /** 最后操作人ID */
    @Column(name = "last_operator_id")
    private String            lastOperatorId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


