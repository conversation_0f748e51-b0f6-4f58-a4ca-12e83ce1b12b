package com.xcwlkj.pubc.service.impl;

import com.xcwlkj.console.model.vo.ssoAuth.UserDetailVO;
import com.xcwlkj.core.sso.store.SsoLoginStore;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.pubc.mapper.ManagerOperateLogMapper;
import com.xcwlkj.pubc.model.domain.ManagerOperateLog;
import com.xcwlkj.pubc.model.domain.ManagerOperateLogRule;
import com.xcwlkj.pubc.model.dto.log.AddManagerOperateLogDTO;
import com.xcwlkj.pubc.model.enums.OperateLogRuleStatusEnum;
import com.xcwlkj.pubc.service.OperateLogRuleCache;
import com.xcwlkj.pubc.service.OperateLogService;
import com.xcwlkj.pubc.service.UserService;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> shark
 * @date 2020/11/30 16:16
 */
@Slf4j
@Service
public class OperateLogServiceImpl implements OperateLogService {
    @Resource
    private ManagerOperateLogMapper managerOperateLogMapper;
    @Resource
    private OperateLogRuleCache operateLogRuleCache;
    @Resource
    private OperateLogService operateLogService;
    @Resource
    private UserService userService;

    /**
     * 保存操作日志
     *
     * @param log
     */
    @Override
    public void saveOperateLog(ManagerOperateLog log) {
        Date now = new Date();
        log.setCreateTime(now);
        log.setUpdateTime(now);
        managerOperateLogMapper.insertSelective(log);
    }
    /** 
     * @see OperateLogService#addManagerOperateLog(AddManagerOperateLogDTO)
     */
	@Override
	public void addManagerOperateLog(AddManagerOperateLogDTO dto) {
        // 判断是否需要记录操作日志
        List<ManagerOperateLogRule> logRules = operateLogRuleCache.get(OperateLogRuleStatusEnum.ENABLE);
        List<ManagerOperateLogRule> matchLogRules = logRules.stream().filter(logRule -> logRule.getRuleCondition().equals(dto.getOperateUrl()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(matchLogRules)){
            return;
        }
        // 保存操作日志
        ManagerOperateLog operateLog = new ManagerOperateLog();
        BeanUtils.copyProperties(dto, operateLog);
        operateLog.setOperateAction(matchLogRules.get(0).getRuleDescription());
        String sessionId = dto.getSessionId();
        if(StringUtil.isEmpty(sessionId) || "null".equals(sessionId)){
            log.warn("this operate log has no sessionId");
        } else{
            XcSsoUser ssoUser = SsoLoginStore.get(dto.getSessionId());
            if(ssoUser != null){
                operateLog.setOperatorId(ssoUser.getUserId());
                operateLog.setOperatorName(ssoUser.getRealName());
            }else {
                log.warn("can not find user by sessionId:{}", dto.getSessionId());
                String userId = sessionId.split("_")[0];
                operateLog.setOperatorId(userId);
                if(StringUtil.isNotEmpty(userId)){
                    UserDetailVO user = userService.queryUserSimpleById(userId);
                    operateLog.setOperatorName(user.getName());
                }
            }
        }
        operateLogService.saveOperateLog(operateLog);
	}
}
