/**
 * xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.domain;

import com.xcwlkj.base.mybatis.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Table;


/**
 * sms_channel_config
 * 
 * <AUTHOR>
 * @version $Id: SmsChannelConfig.java, v 0.1 2019年12月30日 17时04分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "sms_channel_config")
public class SmsChannelConfig extends BaseEntity {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 第三方短信渠道 */
    @Column(name = "channel")
    private String            channel;
    /** 有效时间 */
    @Column(name = "effective_time")
    private Integer            effectiveTime;
    /** 渠道当日发送最大次数 */
    @Column(name = "channel_send_max")
    private Integer            channelSendMax;
    /** 用户接收时间间隔 */
    @Column(name = "receive_interval")
    private Integer            receiveInterval;
    /** 用户当日接收最大次数 */
    @Column(name = "receive_max")
    private Integer            receiveMax;
    /** 渠道参数配置 */
    @Column(name = "config")
    private String            config;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


