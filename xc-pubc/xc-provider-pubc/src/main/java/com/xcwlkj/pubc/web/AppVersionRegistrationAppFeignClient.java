/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.web;


import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.pubc.model.vo.AppRnVersionRegistrationVO;
import com.xcwlkj.pubc.service.AppVersionRegistrationFeignApi;
import com.xcwlkj.pubc.service.AppVersionRegistrationService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.pubc.model.dto.pubc.AppVersionLatestDTO;
import org.springframework.validation.annotation.Validated;
import com.xcwlkj.pubc.model.vo.pubc.AppVersionLatestVO;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * AppVersionRegistration接口
 * <AUTHOR>
 * @version $Id: AppVersionRegistrationXcmcFeignClient.java, v 0.1 2019年11月18日 15时51分 xcwlkj.com Exp $
 */
@RestController
public class AppVersionRegistrationAppFeignClient extends BaseFeignClient implements AppVersionRegistrationFeignApi{
	@Resource
	private AppVersionRegistrationService appVersionRegistrationService;

	@Override
	public Wrapper<AppRnVersionRegistrationVO> getAppRnVersionRegistration(String appType, String appVersion,
			String rnVersion) {
		
		rnVersion = rnVersion.replace("xcdot", ".");
		logger.info("app请求数据---appType={},appVersion={},rnVersion={}", appType,appVersion,rnVersion);
		AppRnVersionRegistrationVO result = appVersionRegistrationService.getAppVersionLatestMsg(appType, appVersion, rnVersion);
		logger.info("app最新版本信息. [OK] AppRnVersionRegistrationVO={}", result);
		return WrapMapper.ok(result);
	}
    
    
   
  
    /** 
     * @see com.xcwlkj.service.AppVersionRegistrationAppFeignApi#appVersionLatest(com.xcwlkj.model.dto.pubc.AppVersionLatestDTO)
     */
	@Override
	public Wrapper<AppVersionLatestVO> appVersionLatest(@RequestBody @Validated AppVersionLatestDTO dto) {
		logger.info("骑客官网app最新版本AppVersionLatestDTO={}", dto);
		AppVersionLatestVO result = appVersionRegistrationService.appVersionLatest(dto);
		logger.info("appVersionLatest - 骑客官网app最新版本. [OK] AppVersionLatestVO={}", result);
		return WrapMapper.ok(result);
	}
}



