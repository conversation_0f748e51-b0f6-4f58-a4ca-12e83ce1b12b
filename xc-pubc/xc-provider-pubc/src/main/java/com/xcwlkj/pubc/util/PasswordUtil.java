package com.xcwlkj.pubc.util;

import java.util.Random;

public class PasswordUtil {

    private static final char[] candidatePwdChar = new char[]{'a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','0','1','2','3','4','5','6','7','8','9'};

    public static String randomPassword(int length){
        StringBuilder stringBuilder = new StringBuilder();

        Random random = new Random();
        for (int i = 0; i < length; i++) {
            stringBuilder.append(candidatePwdChar[random.nextInt(candidatePwdChar.length)]);
        }
        return stringBuilder.toString();
    }
}
