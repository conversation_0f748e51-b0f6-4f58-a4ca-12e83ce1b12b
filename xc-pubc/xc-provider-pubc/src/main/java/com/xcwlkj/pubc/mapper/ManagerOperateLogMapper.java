/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.pubc.model.domain.ManagerOperateLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;



/**
 * manager_operate_log数据库操作
 * <AUTHOR>
 * @version $Id: InitManagerOperateLogMapper.java, v 0.1 2020年12月01日 16时39分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface ManagerOperateLogMapper extends MyMapper<ManagerOperateLog> {

    /**
	 * 分页查询manager_operate_log
	 * 
	 * @param example
	 * @return
	 */
	List<ManagerOperateLog> pageList(ManagerOperateLog example);
}
