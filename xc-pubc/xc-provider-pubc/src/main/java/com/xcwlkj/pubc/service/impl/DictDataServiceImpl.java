/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.base.BaseService;
import com.xcwlkj.console.model.dto.dictdata.ModifyDictDataDTO;
import com.xcwlkj.console.model.dto.dictdata.QueryDictDataDTO;
import com.xcwlkj.console.model.dto.dictdata.SaveDictDataDTO;
import com.xcwlkj.console.model.vo.dict.*;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.pubc.mapper.DictDataMapper;
import com.xcwlkj.pubc.model.domain.DictData;
import com.xcwlkj.pubc.service.DictDataService;
import com.xcwlkj.pubc.share.seq.SequenceEnum;
import com.xcwlkj.pubc.share.seq.service.SequenceService;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.utils.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * dict_data服务
 * <AUTHOR>
 * @version $Id: DictDataService.java, v 0.1 2019年08月23日 13时52分 XcDev Exp $
 */
@Service("dictDataService")
@Transactional(rollbackFor = Exception.class)
public class DictDataServiceImpl extends BaseService<DictData> implements DictDataService {
	/** dict_data库表操作 */
    @Resource
    private DictDataMapper modelMapper;
    @Autowired
    private SequenceService sequenceService;


    @Override
    public QueryDictDataListVO queryDictDataListWithPage(QueryDictDataDTO dictDataDto) {

        QueryDictDataListVO result = new QueryDictDataListVO();
        DictData dictData = new DictData();
        BeanUtil.copyProperties(dictDataDto, dictData);
        PageHelper.startPage(dictDataDto.getPageNum(), dictDataDto.getPageSize());
        List<DictData> list = modelMapper.selectDictDatas(dictData);
        List<DictDataSummaryVO> dictDataSummaryVOList = new ArrayList<>();
        BeanUtil.copyListProperties(list,dictDataSummaryVOList,DictDataSummaryVO.class);
        PageInfo<DictData> pageInfo = new PageInfo<>(list);
        result.setDictDataSummaryVOList(dictDataSummaryVOList);
        result.setTotalRows((int)pageInfo.getTotal());
        return result;
    }

    @Override
    public void modifyDictDataById(ModifyDictDataDTO modifyDictDataDto) {
        DictData dictData = new DictData();
        BeanUtil.copyProperties(modifyDictDataDto, dictData);
        modelMapper.updateByPrimaryKeySelective(dictData);
    }

    @Override
    public void saveDictData(SaveDictDataDTO saveDictDataDto) {
        XcSsoUser xcSsoUser = RequestUtil.getLoginUser();
        DictData dictData = new DictData();
        BeanUtil.copyProperties(saveDictDataDto, dictData);
        String nbr = sequenceService.createKeyNo(SequenceEnum.DICT_DATA);
        dictData.setId(nbr);
        dictData.setCreatedBy(xcSsoUser.getUserName());
        dictData.setCreatedTime(new Date());
        dictData.setCreatorId(xcSsoUser.getUserId());
        dictData.setUpdateInfo(xcSsoUser);
        modelMapper.insert(dictData);
    }

    @Override
    public void deleteDictDataById(String dictDataIds) {
        String[] dictTypeIdArray = dictDataIds.split(",");
        for (int i = 0; i < dictTypeIdArray.length; i++) {
            modelMapper.deleteByPrimaryKey(dictTypeIdArray[i]);
        }
    }

    @Override
    public DictDataDetailVO getDictDataById(String id) {
        DictDataDetailVO result = new DictDataDetailVO();
        DictData dictData = modelMapper.selectByPrimaryKey(id);
        BeanUtil.copyProperties(dictData, result);
        return result;
    }
}