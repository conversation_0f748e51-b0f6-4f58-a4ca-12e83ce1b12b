/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.web;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.pubc.model.domain.Districts;
import com.xcwlkj.pubc.model.dto.pubc.DistrictsGetChildByPidDTO;
import com.xcwlkj.pubc.model.vo.pubc.DistrictsGetChildByPidVO;
import com.xcwlkj.pubc.model.vo.pubc.DistrictsSummaryItemVO;
import com.xcwlkj.pubc.service.DistrictsService;
import com.xcwlkj.pubc.service.DistrictsXcmcFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;

import tk.mybatis.mapper.entity.Example;

/**
 * 行政区划接口
 * <AUTHOR>
 * @version $Id: DistrictsXcmcFeignClient.java, v 0.1 2020年04月24日 01时27分 xcwlkj.com Exp $
 */
@RestController
public class DistrictsXcmcFeignClient extends BaseFeignClient implements DistrictsXcmcFeignApi {

    @Autowired
    private DistrictsService districtsService;

    /** 
     * @see com.xcwlkj.service.DistrictsXcmcFeignApi#districtsGetChildByPid(com.xcwlkj.model.dto.pubc.DistrictsGetChildByPidDTO)
     */
    @Override
    public Wrapper<DistrictsGetChildByPidVO> districtsGetChildByPid(@RequestBody @Validated DistrictsGetChildByPidDTO dto) {
        logger.info("省市区联动查询DistrictsGetChildByPidDTO={}", dto);
        DistrictsGetChildByPidVO result = new DistrictsGetChildByPidVO();

        Example example = new Example(Districts.class);
        example.createCriteria().andEqualTo("pid", dto.getPid());
        example.selectProperties("code", "name", "pid");
        List<Districts> districtsList = districtsService.selectByExample(example);
        List<DistrictsSummaryItemVO> districtsSummaryList = new ArrayList<DistrictsSummaryItemVO>();
        for (Districts districts : districtsList) {
            DistrictsSummaryItemVO districtsSummaryItemVO = new DistrictsSummaryItemVO();
            districtsSummaryItemVO.setCode(districts.getCode());
            districtsSummaryItemVO.setName(districts.getName());
            districtsSummaryItemVO.setPid(districts.getPid());
            districtsSummaryList.add(districtsSummaryItemVO);
        }
        result.setDistrictsSummaryList(districtsSummaryList);
        logger.info("districtsGetChildByPid - 省市区联动查询. [OK] DistrictsGetChildByPidVO={}", result);
        return WrapMapper.ok(result);
    }
}
