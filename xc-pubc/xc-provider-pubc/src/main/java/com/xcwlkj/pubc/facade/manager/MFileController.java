/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.facade.manager;

import com.xcwlkj.base.BaseController;
import com.xcwlkj.pubc.model.dto.file.UploadImageDTO;
import com.xcwlkj.pubc.model.req.file.UploadImageReqModel;
import com.xcwlkj.pubc.model.resp.file.UploadImageRespModel;
import com.xcwlkj.pubc.model.vo.file.UploadImageVO;
import com.xcwlkj.pubc.service.FileService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * File控制层
 * <AUTHOR>
 * @version $Id: FileController.java, v 0.1 2020年11月16日 13时29分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("MFileRemoteController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class MFileController extends BaseController {
    @Resource
    private FileService fileService;
	
   /**
    * 上传图片
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/pubc/file/uploadImage")
    public Wrapper<UploadImageRespModel> uploadImage(UploadImageReqModel reqModel,
                                                     @RequestParam("file") MultipartFile file, HttpServletRequest request) {
		log.info("收到请求开始：[上传图片][/manager/pubc/file/uploadImage]reqModel:"+reqModel.toString());
		UploadImageDTO dto = new UploadImageDTO();
        if (file.isEmpty()){
            return WrapMapper.error("文件不能为空");
        }
        dto.setFile(file);
        UploadImageVO result = fileService.uploadImage(dto);
        UploadImageRespModel respModel = new UploadImageRespModel();
        respModel.setImagePath(result.getImagePath());
        respModel.setThumb(result.getThumb());
		log.info("处理请求结束：[上传图片][/manager/pubc/file/uploadImage]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);		
    }

}