/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.pubc.facade.remote;

import com.xcwlkj.base.BaseController;
import com.xcwlkj.console.model.dto.user.LoginDTO;
import com.xcwlkj.console.model.dto.user.ResetPwdDTO;
import com.xcwlkj.console.model.vo.ssoAuth.LoginVO;
import com.xcwlkj.console.model.vo.ssoAuth.UserDetailVO;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.pubc.model.dto.user.ForgetPasswordDTO;
import com.xcwlkj.pubc.model.dto.user.LogoutDTO;
import com.xcwlkj.pubc.model.dto.user.OpenAccountDTO;
import com.xcwlkj.pubc.model.req.user.*;
import com.xcwlkj.pubc.model.resp.user.*;
import com.xcwlkj.pubc.service.UserService;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.utils.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * User控制层
 * <AUTHOR>
 * @version $Id: UserController.java, v 0.1 2022年10月19日 13时59分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("UserRemoteController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class UserController extends BaseController {

    @Resource
    private UserService userService;
	
   /**
    * 学生和老师开通账户
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/pubc/user/openAccount")
    public Wrapper<RemoteOpenAccountRespModel> remoteOpenAccount(@Validated @RequestBody RemoteOpenAccountReqModel reqModel) {
		log.info("收到请求开始：[学生和老师开通账户][/remote/pubc/user/openAccount]reqModel:"+reqModel.toString());

        OpenAccountDTO openAccountDTO = new OpenAccountDTO();
        BeanUtil.copyProperties(reqModel, openAccountDTO);
        userService.openAccount(openAccountDTO);
        RemoteOpenAccountRespModel respModel = new RemoteOpenAccountRespModel();

        log.info("处理请求结束：[学生和老师开通账户][/remote/pubc/user/openAccount]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 用户登录
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/remote/pubc/user/login")
    public Wrapper<RemoteUserLoginRespModel> login(HttpServletRequest request, HttpServletResponse response,
                                                   @Validated @RequestBody RemoteUserLoginReqModel reqModel) {
        logger.info("用户登录 ManagerUserLoginReqModel={}", reqModel);
        // valid login
        LoginDTO loginDto = new LoginDTO();
        loginDto.setUserName(reqModel.getUserName());
        loginDto.setPassword(reqModel.getPassword());
        loginDto.setChannel(reqModel.getTransChannel());
        LoginVO loginVO = userService.login(loginDto);
        RemoteUserLoginRespModel respModel = new RemoteUserLoginRespModel();
        // 4、return sessionId
        respModel.setSessionId(loginVO.getSessionId());
        respModel.setRoleId(loginVO.getCurrRoleId());
        respModel.setRealName(loginVO.getRealName());
        logger.info("用户登录 ManagerUserLoginRespModel={}", respModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 退出登录
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/remote/pubc/user/logout")
    public Wrapper<LogoutRespModel> logout(@Validated @RequestBody LogoutReqModel reqModel) {
        logger.info("收到请求开始：[退出登录][/manager/pubc/user/logout]reqModel:" + reqModel.toString());
        LogoutDTO dto = new LogoutDTO();

        userService.logout(dto);
        LogoutRespModel respModel = new LogoutRespModel();

        logger.info("处理请求结束：[退出登录][/manager/pubc/user/logout]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 用户修改密码
     *
     * @return Integer
     */
    @PostMapping(value = "/remote/pubc/user/resetUserPwd")
    public Wrapper<ResetUserPwdRespModel> resetUserPwd(@Validated @RequestBody ResetUserPwdReqModel reqModel) {
        logger.info("用户修改密码 ResetUserPwdReqModel={}", reqModel);
        ResetUserPwdRespModel respModel = new ResetUserPwdRespModel();
        ResetPwdDTO resetPwdDTO = new ResetPwdDTO();
        BeanUtil.copyProperties(reqModel, resetPwdDTO);
        userService.resetPwd(resetPwdDTO);
        logger.info("用户修改密码 ResetUserPwdRespModel={}", respModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 用户修改
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/remote/pubc/user/modify")
    public Wrapper<RemoteModifyRespModel> remoteModify(@Validated @RequestBody RemoteModifyReqModel reqModel) {
        log.info("收到请求开始：[用户修改][/remote/pubc/user/modify]reqModel:" + reqModel.toString());
        // TODO:

        RemoteModifyRespModel respModel = new RemoteModifyRespModel();

		log.info("处理请求结束：[用户修改][/remote/pubc/user/modify]reqModel:"+reqModel.toString()
			+",respModel:"+ respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 当前登录用户详情
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/pubc/user/currentUser")
    public Wrapper<CurrentUserRespModel> currentUser(@Validated @RequestBody CurrentUserReqModel reqModel) {
		log.info("收到请求开始：[当前登录用户详情][/remote/pubc/user/currentUser]reqModel:"+reqModel.toString());
        XcSsoUser xcSsoUser = RequestUtil.getLoginUser();

        UserDetailVO result = userService.queryUserDetailById(xcSsoUser.getUserId());
        CurrentUserRespModel respModel = new CurrentUserRespModel();
        BeanUtil.copyProperties(result, respModel);
		log.info("处理请求结束：[当前登录用户详情][/remote/pubc/user/currentUser]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);		
    }
   /**
    * 忘记密码
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/pubc/user/forgetPassword")
    public Wrapper<ForgetPasswordRespModel> forgetPassword(@Validated @RequestBody ForgetPasswordReqModel reqModel) {
		log.info("收到请求开始：[忘记密码][/remote/pubc/user/forgetPassword]reqModel:"+reqModel.toString());
		ForgetPasswordDTO dto = new ForgetPasswordDTO();
        dto.setGh(reqModel.getGh());
        dto.setUserType(reqModel.getUserType());
        dto.setXm(reqModel.getXm());
        dto.setEmail(reqModel.getEmail());
        userService.forgetPassword(dto);
        ForgetPasswordRespModel respModel = new ForgetPasswordRespModel();

		log.info("处理请求结束：[忘记密码][/remote/pubc/user/forgetPassword]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);		
    }
   /**
    * 查询用户详情
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/pubc/user/userDetail")
    public Wrapper<UserDetailRespModel> userDetail(@Validated @RequestBody UserDetailReqModel reqModel) {
		log.info("收到请求开始：[查询用户详情][/remote/pubc/user/userDetail]reqModel:"+reqModel.toString());
        UserDetailRespModel respModel = new UserDetailRespModel();
        UserDetailVO userDetailVO = userService.queryUserDetailByXgh(reqModel.getXgh());
        BeanUtil.copyProperties(userDetailVO, respModel);

		log.info("处理请求结束：[查询用户详情][/remote/pubc/user/userDetail]reqModel:"+reqModel.toString() + ",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    @PostMapping(value = "/remote/pubc/user/userTokenCheck")
    public Wrapper<Void> userTokenCheck() {
        log.info("收到请求开始：[用户token校验][/remote/pubc/user/userTokenCheck]");
        userService.checkLoginToken();

        log.info("处理请求结束：[用户token校验][/remote/pubc/user/userTokenCheck]");
        return WrapMapper.ok();
    }

}