/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.base.exception.ApplicationBusinessException;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.base.exception.XcBaseErrorCode;
import com.xcwlkj.console.model.dto.user.*;
import com.xcwlkj.console.model.vo.ssoAuth.*;
import com.xcwlkj.core.sso.store.SsoSessionIdHelper;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.enums.YesNoEnum;
import com.xcwlkj.model.enums.ChannelEnum;
import com.xcwlkj.model.enums.ShowAreaEnum;
import com.xcwlkj.pubc.exceptions.PubcBusiErrorCode;
import com.xcwlkj.pubc.exceptions.PubcBusiException;
import com.xcwlkj.pubc.mapper.UserMapper;
import com.xcwlkj.pubc.model.domain.Department;
import com.xcwlkj.pubc.model.domain.User;
import com.xcwlkj.pubc.model.domain.UserDepartmentRelation;
import com.xcwlkj.pubc.model.domain.UserRoleRelation;
import com.xcwlkj.pubc.model.domain.dos.UserExportDO;
import com.xcwlkj.pubc.model.domain.dos.UserRelationDO;
import com.xcwlkj.pubc.model.dto.user.ForgetPasswordDTO;
import com.xcwlkj.pubc.model.dto.user.LogoutDTO;
import com.xcwlkj.pubc.model.dto.user.OpenAccountDTO;
import com.xcwlkj.pubc.model.dto.user.UserExportDTO;
import com.xcwlkj.pubc.model.enums.DefaultUserRoleEnum;
import com.xcwlkj.pubc.model.enums.UserTypeEnum;
import com.xcwlkj.pubc.model.vo.user.FailItemVO;
import com.xcwlkj.pubc.model.vo.user.UserExportVO;
import com.xcwlkj.pubc.model.vo.user.UserImportVO;
import com.xcwlkj.pubc.service.*;
import com.xcwlkj.pubc.share.seq.SequenceEnum;
import com.xcwlkj.pubc.share.seq.service.SequenceService;
import com.xcwlkj.pubc.util.ExcelFormatUtil;
import com.xcwlkj.pubc.util.PasswordUtil;
import com.xcwlkj.pubc.util.VerifyUtil;
import com.xcwlkj.resourcecenter.model.dto.teacher.DetailsForRegisterV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.ListByGhV1DTO;
import com.xcwlkj.resourcecenter.model.vo.teacher.DetailsForRegisterV1VO;
import com.xcwlkj.resourcecenter.model.vo.teacher.ListByGhV1VO;
import com.xcwlkj.resourcecenter.model.vo.teacher.SysTeacherItemVO;
import com.xcwlkj.resourcecenter.service.TeacherFeignApi;
import com.xcwlkj.sturegister.model.dto.stuinfo.DetailForRegisterV1DTO;
import com.xcwlkj.sturegister.model.vo.stuinfo.DetailForRegisterV1VO;
import com.xcwlkj.sturegister.service.StuInfoFeignApi;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.CheckParamUtil;
import com.xcwlkj.util.FileUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.utils.ExcelUtil;
import com.xcwlkj.utils.RequestUtil;
import com.xcwlkj.utils.SsoLoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户服务
 * 
 * <AUTHOR>
 * @version $Id: UserServiceImpl.java, v 0.1 2020年09月01日 01时09分 xcwlkj.com Exp $
 */
@Service("userService")
@Slf4j
public class UserServiceImpl implements UserService {

	@Resource
	private UserMapper modelMapper;

	@Autowired
	private DepartmentService departmentService;

	@Autowired
	private TransactionTemplate transactionTemplate;

	@Autowired
	private PasswordEncoder passwordEncoder;

	@Autowired
	private SequenceService sequenceService;

	@Autowired
	private UserDepartmentRelationService userDepartmentRelationService;

	@Autowired
	private UserRoleRelationService userRoleRelationService;

	@Autowired
	private ResourceService resourceService;
	@Resource
	private StuInfoFeignApi stuInfoFeignApi;
	@Resource
	private TeacherFeignApi teacherFeignApi;
	@Resource
	private SendMailService sendMailService;
	@Resource
	private RedisUtil redisUtil;
	@Resource
	private VerifyUtil verifyUtil;

	@Value("${user.defaultPassword}")
	private String defaultPassword;

	@Value("${xc.export.tempPath}")
	private String exportTempPath;
	@Value("${xc.export.muban}")
	private String exportMubanPath;
	@Value("${xc.xcDfs.serviceAddress}")
	private String dfsServerUrl;

	@Override
	public QueryUserMangerListVO queryUserrListWithPage(QueryUserDTO userDto) {

		List<String> deptList = departmentService.getDepartmentByUserId(RequestUtil.getLoginUser().getUserId())
				.stream().map(Department::getId).collect(Collectors.toList());
		if (!StringUtil.isEmpty(userDto.getDepartmentId())) {
			deptList = Arrays.asList(userDto.getDepartmentId());
		}

		Page<Object> page = PageHelper.startPage(userDto.getPageNum(), userDto.getPageSize());
		List<UserManagerSummaryVO> userManagerSummaryVOList = modelMapper.queryUserrListWithPage(deptList, userDto.getMobile(), userDto.getUserName());
		QueryUserMangerListVO queryUserMangerListVO = new QueryUserMangerListVO();
		queryUserMangerListVO.setUserSummaryVOList(userManagerSummaryVOList);
		queryUserMangerListVO.setTotalRows(page.getTotal());

		return queryUserMangerListVO;
	}

	private List<String> deptRecursion(List<String> childList, List<Department> deptList, String pid) {
		for (Department dept : deptList) {
			if (dept.getParentId() != null) {
				if (dept.getParentId().equals(pid)) {
					deptRecursion(childList, deptList, dept.getId());
					childList.add(dept.getId());
				}
			}
		}
		return childList;
	}

	@Override
	public UserDetailVO queryUserDetailById(String userId) {

		User user = modelMapper.selectByPrimaryKey(userId);
		return formatUserDetail(user);
	}

	@Override
	public UserDetailVO queryUserDetailByXgh(String xgh) {
		Example example = new Example(User.class);
		example.createCriteria()
				.andEqualTo("gh", xgh);
		User user = modelMapper.selectOneByExample(example);

		return formatUserDetail(user);
	}

	private UserDetailVO formatUserDetail(User user){
		if (user == null){
			throw new PubcBusiException("该用户不存在");
		}

		UserDetailVO userDetailVO = new UserDetailVO();
		BeanUtil.copyProperties(user, userDetailVO);
		userDetailVO.setXgh(user.getGh());

		// 查询用户的角色
		List<UserRoleVO> userRoleVOList = userRoleRelationService.queryRoleIdByUserId(user.getId());
		userDetailVO.setUserRoleVOList(userRoleVOList);
		// 查询部门名称
		// 2024/12/17 部门修改为多部门, 暂时保持原有结构不变
		List<Department> departments = departmentService.getDepartmentByUserId(user.getId());
		// 查询顶级部门(学校信息)
		if (departments != null && !departments.isEmpty()) {
			List<UserDetailDepartmentItemVO> userDetailDepartmentItemVOS = new ArrayList<>();
			for (Department department : departments) {
				UserDetailDepartmentItemVO userDetailDepartmentItemVO = new UserDetailDepartmentItemVO();
				userDetailDepartmentItemVO.setDepartmentId(department.getId());
				userDetailDepartmentItemVO.setDepartmentName(department.getName());
				userDetailDepartmentItemVOS.add(userDetailDepartmentItemVO);
			}
			userDetailVO.setDepartments(userDetailDepartmentItemVOS);
			userDetailVO.setDepartmentId(departments.get(0).getId());
			userDetailVO.setDepartmentName(departments.get(0).getName());
			String topDepartmentId = departments.get(0).getAncestors().split(",")[0];
			Department topDepartment = departmentService.selectByPrimaryKey(topDepartmentId);
			userDetailVO.setSchoolId(topDepartment!=null?topDepartment.getId():null);
			userDetailVO.setSchoolName(topDepartment!=null?topDepartment.getName():null);
		}

		if(UserTypeEnum.TEACHER.getCode().equals(user.getUserType())){
			// 查询二级学科码和性别码
			List<String> gh = new ArrayList<>();
			ListByGhV1DTO listByGhV1DTO = new ListByGhV1DTO();
			gh.add(user.getGh());
			listByGhV1DTO.setGhList(gh);
			Wrapper<ListByGhV1VO> listByGhV1VOWrapper = teacherFeignApi.listByGhV1(listByGhV1DTO);
			if(listByGhV1VOWrapper.getResult()!=null&&!CollectionUtils.isEmpty(listByGhV1VOWrapper.getResult().getTeacherList())){
				SysTeacherItemVO sysTeacherItemVO = listByGhV1VOWrapper.getResult().getTeacherList().get(0);
				userDetailVO.setEjxkm(sysTeacherItemVO.getEjxkm());
				userDetailVO.setXbm(sysTeacherItemVO.getXbm());
			}
		}else if (UserTypeEnum.STUDENT.getCode().equals(user.getUserType())){
			// 查询性别码
			DetailForRegisterV1DTO detailForRegisterV1DTO = new DetailForRegisterV1DTO();
			detailForRegisterV1DTO.setXh(user.getGh());
			Wrapper<DetailForRegisterV1VO> detailForRegisterV1VOWrapper = stuInfoFeignApi.detailForRegisterV1(detailForRegisterV1DTO);
			userDetailVO.setXbm(detailForRegisterV1VOWrapper.getResult().getXbm());
		}
		return userDetailVO;
	}

	@Override
	public UserDetailVO queryUserSimpleById(String userId) {
		UserDetailVO userDetailVO = new UserDetailVO();
		User user = modelMapper.selectByPrimaryKey(userId);
		BeanUtil.copyProperties(user, userDetailVO);
		if(user != null){
			userDetailVO.setXgh(user.getGh());
		}
		return userDetailVO;
	}

	@Override
	public void saveUser(SaveUserDTO userDto) {

		transactionTemplate.execute(new TransactionCallback<String>() {

			@Override
			public String doInTransaction(TransactionStatus status) {
				CheckParamUtil.checkObjectNotNull(userDto, "用户对象不能为空");
				String userName = userDto.getUserName();
				CheckParamUtil.checkStringNotBlank(userName, "用户名不能为空");
				if (StringUtils.isBlank(userDto.getPassword())) {
					userDto.setPassword(defaultPassword);
				}
				// CheckParamUtil.checkStringNotBlank(userDto.getPassword(), "密码不能为空");
				User checkModel = new User();
				checkModel.setUserName(userName);
				checkModel = modelMapper.selectOne(checkModel);
				if (checkModel != null) {
					throw new BusinessException("用户名已存在");
				}
//				XcSsoUser xcSsoUser = RequestUtil.getLoginUser();
				String nbr = sequenceService.createKeyNo(SequenceEnum.USER);
				userDto.setEnabled(YesNoEnum.YES_1.getValue());
				userDto.setAccountNonExpired(YesNoEnum.YES_1.getValue());
				userDto.setAccountNonLocked(YesNoEnum.YES_1.getValue());
				userDto.setCredentialsNonExpired(YesNoEnum.YES_1.getValue());
				User user = new User();
				BeanUtil.copyProperties(userDto, user);
				// 暂用第一个
				user.setDepartmentId(userDto.getDepartmentId().split(",")[0]);
				user.setId(nbr);
//				user.setCreatedBy(xcSsoUser.getUserName());
//				user.setCreatorId(xcSsoUser.getUserId());
				user.setCreatedTime(new Date());
				user.setPassword(passwordEncoder.encode(user.getPassword()));
				user.setEmail(userDto.getEmail());
				modelMapper.insertSelective(user);
				addUserRelationData(Arrays.asList(userDto.getDepartmentId().split(",")), user.getId(), userDto.getPositionId(),
						userDto.getRoleId());

				return "";
			}

		});

	}

	private void checkPassword(String password){
		String regex = "^((?=.*[A-Za-z])(?=.*\\d)|(?=.*[A-Za-z])(?=.*[^A-Za-z0-9])|(?=.*\\d)(?=.*[^A-Za-z0-9])).{6,16}$";
		Pattern p = Pattern.compile(regex);

		if (!p.matcher(password).matches()){
			throw new PubcBusiException("密码需要 6-16位，密码应包含字母、数字和特殊字符的两种或以上");
		}
	}

	private void addUserRelationData(List<String> departmentIdList, String userId, String positionId, String roleId) {
		for (String departmentId : departmentIdList) {
			UserDepartmentRelation userDepartmentRelation = new UserDepartmentRelation();
			userDepartmentRelation.setDepartmentId(departmentId);
			userDepartmentRelation.setUserId(userId);
			userDepartmentRelation.setId(null);
			userDepartmentRelationService.save(userDepartmentRelation);
		}

		UserRoleRelation userRoleRelation = new UserRoleRelation();
		userRoleRelation.setUserId(userId);
		userRoleRelation.setRoleId(roleId);
		userRoleRelation.setId(null);
		userRoleRelationService.save(userRoleRelation);
	}

	@Override
	public void modifyUserById(ModifyUserDTO modifyUserDto) {

		transactionTemplate.execute(new TransactionCallback<String>() {

			@Override
			public String doInTransaction(TransactionStatus status) {
				User user = new User();
				BeanUtil.copyProperties(modifyUserDto, user);
				// 暂用第一个
				user.setDepartmentId(modifyUserDto.getDepartmentId().split(",")[0]);
				modelMapper.updateByPrimaryKeySelective(user);
				userDepartmentRelationService.deleteByUserId(user.getId());
				userRoleRelationService.deleteByUserId(user.getId());

				addUserRelationData(Arrays.asList(modifyUserDto.getDepartmentId().split(",")), user.getId(), modifyUserDto.getPositionId(),
						modifyUserDto.getRoleId());
				return null;
			}
		});

	}

	@Override
	public void resetPwdByUserId(ResetPwdByUserIdDTO resetPwdByUserIdDTO) {
		CheckParamUtil.checkStringNotBlank(resetPwdByUserIdDTO.getNewPassword(), "新密码不能为空");

		User record = new User();
		record.setId(resetPwdByUserIdDTO.getUserId());
		record.setPassword(passwordEncoder.encode(resetPwdByUserIdDTO.getNewPassword()));
		modelMapper.updateByPrimaryKeySelective(record);

	}

	@Override
	public void resetPwd(ResetPwdDTO resetPwdDTO) {

		checkPassword(resetPwdDTO.getNewPassword());

		CheckParamUtil.checkStringNotBlank(resetPwdDTO.getOldPassword(), "旧密码不能为空");
		CheckParamUtil.checkStringNotBlank(resetPwdDTO.getNewPassword(), "新密码不能为空");
		CheckParamUtil.checkStringNotBlank(resetPwdDTO.getConfirmPassword(), "确认密码不能为空");

		if (!resetPwdDTO.getNewPassword().equals(resetPwdDTO.getConfirmPassword())) {
			throw new BusinessException("两次输入的密码不匹配");
		}
		User user = modelMapper.selectByPrimaryKey(RequestUtil.getLoginUser().getUserId());
		if (!passwordEncoder.matches(resetPwdDTO.getOldPassword(), user.getPassword())) {
			throw new BusinessException("原始密码不匹配");
		}

		XcSsoUser xcSsoUser = RequestUtil.getLoginUser();
		User record = new User();
		record.setId(xcSsoUser.getUserId());
		record.setPassword(passwordEncoder.encode(resetPwdDTO.getNewPassword()));
		modelMapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public void deleteUserById(String userIds) {
		String[] ids = StringUtils.split(userIds, ",");
		for (int i = 0; i < ids.length; i++) {
			modelMapper.deleteByPrimaryKey(ids[i]);
			userRoleRelationService.deleteByUserId(ids[i]);
			userDepartmentRelationService.deleteByUserId(ids[i]);
		}

	}

	@Override
	public LoginVO login(LoginDTO loginDto) {
		checkPassword(loginDto.getPassword());

		if (StringUtil.isNotBlank(loginDto.getVerifyCode())){
			// 验证码验证
			Object verifyCode = redisUtil.get(loginDto.getUuid());
			if (verifyCode != null){
				String code = verifyCode.toString();
				boolean checkResult = verifyUtil.check(code, loginDto.getVerifyCode());
				if (!checkResult){
					throw new BusinessException(PubcBusiErrorCode.LOGIN_CODE_ERROR,"验证码错误");
				}
			}
		}



		ChannelEnum channelEnum = ChannelEnum.get(loginDto.getChannel());

		Example example = new Example(User.class);
		Example.Criteria criteria = example.createCriteria()
				.andEqualTo("userName", loginDto.getUserName());
		if (ShowAreaEnum.MOBILE.equals(channelEnum.getShowArea()) ) {
			example.and()
					.andEqualTo("userType", 1);
		}else {
			example.and()
					.andEqualTo("userType", 0)
					.orIsNull("userType");
		}
		List<User> userList = modelMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(userList)){
			throw new PubcBusiException(PubcBusiErrorCode.INVALID_PARAM, "用户名或密码错误");
		}
		User user = userList.get(0);
		if (user.getAccountNonLocked() != null
				&& user.getAccountNonLocked().intValue() == YesNoEnum.NO_0.getValue().intValue()) {
			throw new PubcBusiException(PubcBusiErrorCode.INVALID_PARAM, "用户已锁定，请联系管理员");
		}
		if (!passwordEncoder.matches(loginDto.getPassword(), user.getPassword())) {
			throw new PubcBusiException(PubcBusiErrorCode.INVALID_PARAM, "用户名或密码错误");
		}

		return login(user, channelEnum);
	}

	@Override
	public LoginVO loginNoPassword(String xgh, Integer userType, ChannelEnum channelEnum) {

		Example example = new Example(User.class);
		example.createCriteria()
				.andEqualTo("gh", xgh)
				.andEqualTo("userType", userType);
		User user = modelMapper.selectOneByExample(example);

		return login(user, channelEnum);
	}

	private LoginVO login(User user, ChannelEnum channelEnum) {
		// 1、make xc-sso user
		XcSsoUser xcSsoUser = new XcSsoUser();
		xcSsoUser.setUserId(user.getId());
		xcSsoUser.setUserName(user.getUserName());
		xcSsoUser.setRealName(user.getName());
		xcSsoUser.setMobile(user.getMobile());
		xcSsoUser.setDepartmentId(user.getDepartmentId());
		DepartmentVO department = departmentService.getDepartmentById(user.getDepartmentId());
		xcSsoUser.setDepartmentName(department.getName());
		xcSsoUser.setVersion(UUID.randomUUID().toString().replaceAll("-", ""));
		xcSsoUser.setExpireMinite(SsoLoginHelper._LoginTokenLiveMap.get(channelEnum).intValue());
		xcSsoUser.setExpireFreshTime(System.currentTimeMillis());
		xcSsoUser.setChannel(channelEnum.getCode());

		// 获得当前用户的角色id
		List<UserRoleVO> currentRoleId = userRoleRelationService.queryRoleIdByUserId(xcSsoUser.getUserId());
		if (currentRoleId == null || currentRoleId.size() <= 0) {
			throw new PubcBusiException(PubcBusiErrorCode.INVALID_PARAM, "用户无权登录");
		}
		String roleId = currentRoleId.get(0).getRoleId();
		xcSsoUser.setCurrRoleId(roleId);
		List<RoleResourceRelationVO> roleResources = resourceService.queryCurrtRoleOfResource(roleId);
		String permissions = roleResources.stream().map(RoleResourceRelationVO::getUrl).collect(Collectors.joining(","));
		xcSsoUser.setPlugininfo(roleId, permissions);

		// 2、generate sessionId + storeKey
		String sessionId = SsoSessionIdHelper.makeSessionId(xcSsoUser);

		SsoLoginHelper.login(RequestUtil.getResponse(), sessionId, xcSsoUser, true, SsoLoginHelper._LoginTokenLiveMap.get(channelEnum));
		LoginVO loginVO = new LoginVO();
		loginVO.setSessionId(sessionId);
		loginVO.setRealName(user.getName());
		loginVO.setCurrRoleId(currentRoleId.get(0).getRoleId());
		loginVO.setUserId(user.getId());
		loginVO.setUserName(user.getUserName());
		return loginVO;
	}

	@Override
	public void modifyUserProfile(ModifyUserDTO modifyUserDto) {
		User model = new User();
		model.setLastOperatorId(RequestUtil.getLoginUser().getUserId());
		model.setLastOperatorTime(new Date());
		model.setId(modifyUserDto.getId());
		model.setAccountNonLocked(modifyUserDto.getAccountNonLocked());
		modelMapper.updateByPrimaryKeySelective(model);
	}

    /** 
     * @see com.xcwlkj.pubc.service.UserService#logout(com.xcwlkj.pubc.model.dto.user.LogoutDTO)
     */
	@Override
	public void logout(LogoutDTO dto) {
		SsoLoginHelper.logout(RequestUtil.getRequest(), RequestUtil.getResponse());
	}
    /** 
     * @see com.xcwlkj.pubc.service.UserService#openAccount(com.xcwlkj.pubc.model.dto.user.OpenAccountDTO)
     */
	@Override
	public void openAccount(OpenAccountDTO dto, boolean nameCheck) {

		Example example = new Example(User.class);
		example.createCriteria()
				.andEqualTo("gh", dto.getGh())
				.andEqualTo("userType", dto.getUserType());
		List<User> users = modelMapper.selectByExample(example);
		if (users.size() > 0){
			throw new PubcBusiException("该用户已经注册!");
		}

		if (!StringUtils.equals(dto.getPassword(),dto.getConfirmPassword())){
			throw new PubcBusiException("两次输入的密码不一致!");
		}

		SaveUserDTO saveUserDTO = new SaveUserDTO();
		switch (UserTypeEnum.get(dto.getUserType())){
			case TEACHER:
				DetailsForRegisterV1DTO teacherRegisterV1DTO = new DetailsForRegisterV1DTO();
				teacherRegisterV1DTO.setGh(dto.getGh());
				DetailsForRegisterV1VO result = teacherFeignApi.detailsForRegisterV1(teacherRegisterV1DTO).getResult();
				if (result == null || StringUtil.isBlank(result.getGh())){
					throw new PubcBusiException("教职工信息不存在, 无法注册");
				}
				saveUserDTO.setGh(result.getGh());
				saveUserDTO.setXqh(result.getXqh());
				saveUserDTO.setDwh(result.getDwh());
				saveUserDTO.setName(result.getXm());
				saveUserDTO.setIdCardNo(result.getIdCardNo());
				saveUserDTO.setRoleId(DefaultUserRoleEnum.TEACHER.getCode());
				break;
			case STUDENT:
				com.xcwlkj.sturegister.model.dto.stuinfo.DetailForRegisterV1DTO stuRegisterDTO = new DetailForRegisterV1DTO();
				stuRegisterDTO.setXh(dto.getGh());
				DetailForRegisterV1VO result1 = stuInfoFeignApi.detailForRegisterV1(stuRegisterDTO).getResult();
				if (result1 == null || StringUtil.isBlank(result1.getXm())){
					throw new PubcBusiException("学生信息不存在, 无法注册");
				}
				saveUserDTO.setGh(result1.getXh());
				saveUserDTO.setXqh(result1.getXqh());
				saveUserDTO.setDwh(result1.getDwh());
				saveUserDTO.setName(result1.getXm());
				saveUserDTO.setIdCardNo(result1.getIdCardNo());
				saveUserDTO.setRoleId(DefaultUserRoleEnum.STUDENT.getCode());
		}
		// 姓名校验
		if (nameCheck && !StringUtils.equals(saveUserDTO.getName(), dto.getXm())){
			throw new BusinessException("姓名校验未通过, 请检查输入信息");
		}
		saveUserDTO.setUserType(dto.getUserType());
		saveUserDTO.setUserName(saveUserDTO.getGh());
		saveUserDTO.setDepartmentId(saveUserDTO.getDwh());
		saveUserDTO.setEmail(dto.getEmail());
		saveUserDTO.setPassword(dto.getPassword());
		saveUser(saveUserDTO);

	}

	@Override
	public void openAccount(OpenAccountDTO dto) {
		openAccount(dto, true);
	}

	/**
     * @see com.xcwlkj.pubc.service.UserService#forgetPassword(com.xcwlkj.pubc.model.dto.user.ForgetPasswordDTO)
     */
	@Override
	public void forgetPassword(ForgetPasswordDTO dto) {
	
		Example example = new Example(User.class);
		example.createCriteria()
				.andEqualTo("gh", dto.getGh())
				.andEqualTo("name", dto.getXm())
				.andEqualTo("userType", dto.getUserType())
				.andEqualTo("email", dto.getEmail());
		List<User> users = modelMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(users)){
			throw new PubcBusiException("用户信息错误, 请检查输入信息");
		}
		User user = users.get(0);

		String newPassword = PasswordUtil.randomPassword(8);
		user.setPassword(passwordEncoder.encode(newPassword));
		modelMapper.updateByPrimaryKeySelective(user);
		sendMailService.sendSimpleMail("忘记密码", "用户["+user.getUserName()+"],您的密码是"+newPassword,new HashSet<String>(){{add(user.getEmail());}});

	}
    /** 
     * @see UserService#userImport(MultipartFile)
	 * @param file
	 * @return
	 */
	@Override
	public UserImportVO userImport(MultipartFile file) {
		UserImportVO result = new UserImportVO();
		List<FailItemVO> failItemVOS = new ArrayList<>();
		String localFilePath = ExcelFormatUtil.saveFileToService("temp/userImport", file);
		List<List<String>> datas;
		try {
			datas = ExcelUtil.readBigFile(localFilePath, 4);
		} catch (Exception e) {
			log.error("读取文件失败", e);
			throw new PubcBusiException("读取文件失败");
		}finally {
			FileUtil.deleteFile(localFilePath);
		}

		for (int i = 1; i < datas.size(); i++) {
			List<String> data = datas.get(i);
			try {
				OpenAccountDTO openAccountDTO = new OpenAccountDTO();
				openAccountDTO.setGh(data.get(0));
				openAccountDTO.setUserType(Integer.parseInt(data.get(1)));
				openAccountDTO.setXm(data.get(2));
				openAccountDTO.setEmail(data.get(3));
				openAccountDTO.setPassword(defaultPassword);
				openAccountDTO.setConfirmPassword(defaultPassword);
				openAccount(openAccountDTO);
			}catch (Exception e){
				log.error("导入失败",e);
				FailItemVO failItemVO = new FailItemVO();
				failItemVO.setRowNum(String.valueOf(i+1));
				failItemVO.setReason(String.format("学工号[%s], %s", data.get(0), e.getMessage()));
				failItemVOS.add(failItemVO);
			}
		}
		result.setFailList(failItemVOS);
		return result;
	}
    /** 
     * @see com.xcwlkj.pubc.service.UserService#userExport(com.xcwlkj.pubc.model.dto.user.UserExportDTO)
     */
	@Override
	public UserExportVO userExport(UserExportDTO dto) {
		UserExportVO result = new UserExportVO();
		QueryUserDTO userDto = new QueryUserDTO();
		userDto.setPageNum(1);
		userDto.setPageSize(9999);
		userDto.setDepartmentId(dto.getDepartmentId());
		userDto.setUserName(dto.getUserName());
		userDto.setMobile(dto.getMobile());
		QueryUserMangerListVO queryUserMangerListVO = queryUserrListWithPage(userDto);

		if (queryUserMangerListVO.getUserSummaryVOList().isEmpty()){
			throw new PubcBusiException("导出内容为空");
		}
		List<UserExportDO> userExportDOS = new ArrayList<>();
		Map<String, UserRelationDO> userRelationMap = getUserRelationMap(queryUserMangerListVO.getUserSummaryVOList().stream().map(UserManagerSummaryVO::getId).collect(Collectors.toList()));

		for (UserManagerSummaryVO userManagerSummaryVO : queryUserMangerListVO.getUserSummaryVOList()) {
			UserExportDO userExportDO = BeanUtil.copyProperties(userManagerSummaryVO, UserExportDO.class);
			UserRelationDO userRelationDO = userRelationMap.get(userManagerSummaryVO.getId());
			if (userRelationDO != null) {
				userExportDO.setRoleName(userRelationDO.getRoleName());
				userExportDO.setDepartmentName(userRelationDO.getDepartmentName());
			}
			userExportDOS.add(userExportDO);
		}
		String path = writeUserExportData(userExportDOS.stream()
				.map(userExportDO -> Arrays.asList(userExportDO.getUserName(),userExportDO.getName(),userExportDO.getMobile(),userExportDO.getEmail(),userExportDO.getAccountNonLocked().equals(1)?"可用":"不可用",userExportDO.getRoleName(), userExportDO.getDepartmentName()))
				.collect(Collectors.toList()));
		result.setFilePath(path);
		return result;
	}

	private String writeUserExportData(List<List<String>> dataList){
		String templatePath = exportMubanPath + File.separator + "user_list.xlsx";

		File file = new File(exportTempPath);
		if (!file.exists()){
			file.mkdirs();
		}
		String finalXlsxPath = exportTempPath + File.separator + "userExport" + File.separator + "用户数据导出" + System.currentTimeMillis() + ".xlsx";
		ExcelUtil.writeExcel(dataList, dataList.get(0).size(), templatePath, finalXlsxPath);

		// 上传dfs
		UploadVO uploadVO = XcDfsClient.uploadStream(finalXlsxPath);
		return dfsServerUrl + "/remote/file/" + uploadVO.getList().get(0).getFilePath();
	}
	private Map<String, UserRelationDO> getUserRelationMap(List<String> userIds){
		Map<String, UserRelationDO> map = new HashMap<>();
		List<UserRelationDO> userRelation = modelMapper.getUserRelation(userIds);
		for (UserRelationDO userRelationDO : userRelation) {
			map.put(userRelationDO.getUserId(), userRelationDO);
		}
		return map;
	}

	@Override
	public List<User> selectByExample(Example example) {
		return modelMapper.selectByExample(example);
	}

	@Override
	public void checkLoginToken() {
		String xc_sso_sessionid = RequestUtil.getRequest().getHeader("xc_sso_sessionid");
		if (!redisUtil.exists("xc_sso_sessionid:" + xc_sso_sessionid)){
			throw new ApplicationBusinessException(XcBaseErrorCode.ACCOUNT_NOT_LOGGED_IN,"请登录！");
		}
	}
}