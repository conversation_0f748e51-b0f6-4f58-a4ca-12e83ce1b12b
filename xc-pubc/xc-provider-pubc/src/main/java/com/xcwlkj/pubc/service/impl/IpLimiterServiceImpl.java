package com.xcwlkj.pubc.service.impl;

import cn.hutool.core.net.NetUtil;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.pubc.model.vo.WhiteIpVO;
import com.xcwlkj.pubc.service.IpLimiterService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * IP名单限制服务接口<br/>
 * 当前暂定不支持模糊记录
 *
 * <AUTHOR>
 * @date 2024/6/19 15:25
 */
@Service("ipLimiterService")
public class IpLimiterServiceImpl implements IpLimiterService {
    //    private Logger logger = LoggerFactory.getLogger(getClass());
    private final String IP_WHITE_PREFIX = "ip:white:";

//    private final String IP_WHITE_SYSTEM_PREFIX = IP_WHITE_PREFIX + "system:";

    private final String IP_WHITE_SINGLE_PREFIX = IP_WHITE_PREFIX + "single:";
    /**
     * IP白名单：单条：系统组
     */
    private final String IP_WHITE_SINGLE_SYSTEM_PREFIX = IP_WHITE_SINGLE_PREFIX + "sys:";
    /**
     * IP白名单：单条：用户组
     */
    private final String IP_WHITE_SINGLE_USER_PREFIX = IP_WHITE_SINGLE_PREFIX + "user:";

    private final String IP_WHITE_RANGE_PREFIX = IP_WHITE_PREFIX + "range:";
    /**
     * IP白名单：范围：系统组
     */
    private final String IP_WHITE_RANGE_SYSTEM_PREFIX = IP_WHITE_RANGE_PREFIX + "sys:";
    /**
     * IP白名单：范围：用户组
     */
    private final String IP_WHITE_RANGE_USER_PREFIX = IP_WHITE_RANGE_PREFIX + "user:";

//    private final String IP_WHITE_USER_PREFIX = IP_WHITE_PREFIX + "user:";
//
//    private final String IP_WHITE_USER_SINGLE_PREFIX = IP_WHITE_USER_PREFIX + "single:";
//
//    private final String IP_WHITE_USER_RANGE_PREFIX = IP_WHITE_USER_PREFIX + "range:";

    @Resource
    private RedisUtil redisUtil;

    @Override
    public List<WhiteIpVO> listWhiteIp() {
        List<String> list = redisUtil.queryStartWithKey(IP_WHITE_PREFIX);
        if (null == list || list.isEmpty()) {
            return new ArrayList<>(0);
        }
        List<WhiteIpVO> voList = new ArrayList<>();
        for (String temp : list) {
            // ip:white:single:sys:[text]
            final String[] split = StringUtils.split(temp, ":");
            if (null == split || 0 == split.length) {
                continue;
            }
            if (StringUtils.equals("sys", split[3])) {
                if (StringUtils.equals("single", split[2])) {
                    voList.add(new WhiteIpVO(temp, 1, split[4], 1));
                } else {
                    voList.add(new WhiteIpVO(temp, 1, split[4], 2));
                }
            } else {
                if (StringUtils.equals("single", split[2])) {
                    voList.add(new WhiteIpVO(temp, 0, split[4], 1));
                } else {
                    voList.add(new WhiteIpVO(temp, 0, split[4], 2));
                }
            }
        }
        return voList;
    }

    @Override
    public void addWhiteIp(String filter, int type) {
        // value内容暂时不做对象化存储
        if (1 == type) {
            redisUtil.set(IP_WHITE_SINGLE_SYSTEM_PREFIX + filter, filter);
        } else {
            redisUtil.set(IP_WHITE_SINGLE_USER_PREFIX + filter, filter);
        }
    }

    @Override
    public void addWhiteIpRange(String filter, int type) {
        // 暂不考虑分数化存储
        if (1 == type) {
            redisUtil.set(IP_WHITE_RANGE_SYSTEM_PREFIX + filter, filter);
        } else {
            redisUtil.set(IP_WHITE_RANGE_USER_PREFIX + filter, filter);
        }
    }

    @Override
    public void removeWhiteIp(List<String> keyList) {
        if (null == keyList || keyList.isEmpty()) {
            return;
        }
        // 暂定for循环移除
        for (String key : keyList) {
            redisUtil.del(key);
        }
    }

    @Override
    public int whiteIpCheck(String filter) {
        // 暂无合适方案，暂不加数字锁
//        boolean flag = redisUtil.exists(IP_WHITE_PREFIX);
//        if (!flag) {
//            return -1;
//        }

        // 判断是否有本地白名单
        List<String> all = redisUtil.queryStartWithKey(IP_WHITE_PREFIX);
        if (null == all || all.isEmpty()) {
            return -1;
        }

        // 判断是否命中白名单单条系统组
        boolean singleHit1 = redisUtil.exists(IP_WHITE_SINGLE_SYSTEM_PREFIX + filter);
        if (singleHit1) {
            return 1;
        }
        // 判断是否命中白名单单条用户组
        boolean singleHit2 = redisUtil.exists(IP_WHITE_SINGLE_USER_PREFIX + filter);
        if (singleHit2) {
            return 1;
        }
        // 白名单范围列表命中检测(需要严格测试异常问题)
        List<String> rangeList = redisUtil.queryStartWithKey(IP_WHITE_RANGE_PREFIX);
        for (String temp : rangeList) {
            Object ip = redisUtil.get(temp);
            if (null != ip) {
                String[] ips = StringUtils.split((String) ip, "-");
                boolean rangeHit = rangeHitCheck(ips[0], ips[1], filter);
                if (rangeHit) {
                    return 1;
                }
            }
        }
        return 0;
    }

    @Override
    public void clear() {
        redisUtil.delStartWithKey(IP_WHITE_PREFIX);
    }

    private boolean rangeHitCheck(String startIp, String endIp, String ip) {
        long startIpNum = NetUtil.ipv4ToLong(startIp);
        long endIpNum = NetUtil.ipv4ToLong(endIp);
        long ipNum = NetUtil.ipv4ToLong(ip);
//        logger.debug("startIp: [{}, {}], endIp: [{}, {}], ip: [{}, {}]", startIp, startIpNum, endIp, endIpNum, ip, ipNum);
        return ipNum >= startIpNum && ipNum <= endIpNum;
    }

//    private long ipToLong(String ip) {
//        String[] parts = StringUtils.split(ip, ".");
//        return (Long.parseLong(parts[0]) << 24) + (Long.parseLong(parts[1]) << 16)
//                + (Long.parseLong(parts[2]) << 8) + Long.parseLong(parts[3]);
//    }

}
