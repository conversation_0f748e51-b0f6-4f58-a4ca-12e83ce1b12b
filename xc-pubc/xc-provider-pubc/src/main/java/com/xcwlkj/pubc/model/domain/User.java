/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 用户表
 * 
 * <AUTHOR>
 * @version $Id: User.java, v 0.1 2022年10月19日 10时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "`user`")
public class User implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 用户id */
    @Id
    @Column(name = "id")
    private String            id;
    /** 工号/学号 */
    @Column(name = "gh")
    private String            gh;
    /** 用户类型
            0：教职工
            1：学生 */
    @Column(name = "user_type")
    private Integer            userType;
    /**  */
    @Column(name = "dwh")
    private String            dwh;
    /** 校区号 */
    @Column(name = "xqh")
    private String            xqh;
    /** 版本号 */
    @Column(name = "version")
    private Integer            version;
    /** 用户名 */
    @Column(name = "user_name")
    private String            userName;
    /** 用户密码密文 */
    @Column(name = "password")
    private String            password;
    /** 用户姓名 */
    @Column(name = "name")
    private String            name;
    /** 用户手机 */
    @Column(name = "mobile")
    private String            mobile;
    /** email */
    @Column(name = "email")
    private String            email;
    /** 部门编号 */
    @Column(name = "department_id")
    private String            departmentId;
    /** 所在省 */
    @Column(name = "province")
    private String            province;
    /** 所在市 */
    @Column(name = "city")
    private String            city;
    /** 所在区 */
    @Column(name = "district")
    private String            district;
    /** 地址 */
    @Column(name = "address")
    private String            address;
    /** 身份证号 */
    @Column(name = "id_card_no")
    private String            idCardNo;
    /** 是否有效 */
    @Column(name = "enabled")
    private Integer            enabled;
    /** 账号是否未过期 */
    @Column(name = "account_non_expired")
    private Integer            accountNonExpired;
    /** 密码是否未过期 */
    @Column(name = "credentials_non_expired")
    private Integer            credentialsNonExpired;
    /** 账户是否锁定 */
    @Column(name = "account_non_locked")
    private Integer            accountNonLocked;
    /** 最后登录失败时间 */
    @Column(name = "failure_time")
    private Date            failureTime;
    /** 失败次数 */
    @Column(name = "failure_count")
    private Integer            failureCount;
    /** 最后登录时间 */
    @Column(name = "last_login_time")
    private Date            lastLoginTime;
    /** 最后登录ip */
    @Column(name = "last_login_ip")
    private String            lastLoginIp;
    /** 创建时间 */
    @Column(name = "created_time")
    private Date            createdTime;
    /** 最后操作时间 */
    @Column(name = "last_operator_time")
    private Date            lastOperatorTime;
    /** 创建人 */
    @Column(name = "created_by")
    private String            createdBy;
    /** 创建人ID */
    @Column(name = "creator_id")
    private String            creatorId;
    /** 最近操作人 */
    @Column(name = "last_operator")
    private String            lastOperator;
    /** 最后操作人ID */
    @Column(name = "last_operator_id")
    private String            lastOperatorId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


