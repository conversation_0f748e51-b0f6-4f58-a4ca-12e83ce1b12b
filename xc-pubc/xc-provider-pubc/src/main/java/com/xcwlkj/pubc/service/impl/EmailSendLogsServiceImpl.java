/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import com.xcwlkj.base.BaseService;
import com.xcwlkj.pubc.mapper.EmailSendLogsMapper;
import com.xcwlkj.pubc.model.domain.EmailSendLogs;
import com.xcwlkj.pubc.service.EmailSendLogsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * email_send_logs服务
 * <AUTHOR>
 * @version $Id: EmailSendLogsServiceImpl.java, v 0.1 2019年12月30日 17时04分 xcwlkj.com Exp $
 */
@Service("emailSendLogsService")
public class EmailSendLogsServiceImpl extends BaseService<EmailSendLogs> implements EmailSendLogsService {

    @Resource
    private EmailSendLogsMapper modelMapper;
    

}