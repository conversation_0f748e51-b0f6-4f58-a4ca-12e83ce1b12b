package com.xcwlkj.pubc.model.enums;

import com.xcwlkj.base.exception.BusinessException;

public enum DefaultUserRoleEnum {

    TEACHER("001", "教职工"),
    STUDENT("002", "学生")
    ;

    String code;
    String name;

    DefaultUserRoleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static DefaultUserRoleEnum get(String code) {
        for (DefaultUserRoleEnum ele : DefaultUserRoleEnum.values()) {
            if (ele.getCode().equals(code)) {
                return ele;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }
}
