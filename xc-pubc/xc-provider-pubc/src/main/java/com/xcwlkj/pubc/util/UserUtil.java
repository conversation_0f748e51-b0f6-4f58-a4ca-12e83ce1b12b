package com.xcwlkj.pubc.util;

import com.xcwlkj.pubc.model.vo.token.LoginTokenVO;
import com.xcwlkj.pubc.service.TokenService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020-05-15 15:32
 * 类作用说明
 */
@Deprecated
//@Component
public class UserUtil {

    private static TokenService tokenService;

    @Resource
    private void setLoginUserService(TokenService tokenService) {
        UserUtil.tokenService = tokenService;
    }

    public static LoginTokenVO currentUser() {
        return tokenService.getLoginUser();
    }

}
