/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * sms_auth
 * 
 * <AUTHOR>
 * @version $Id: SmsAuth.java, v 0.1 2020年11月11日 13时35分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "sms_auth")
public class SmsAuth implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 编号 */
    @Id
    @Column(name = "id")
    private Integer            id;
    /** 场景类型
            通用：GENERAL */
    @Column(name = "scene_type")
    private String            sceneType;
    /** 用户编号 */
    @Column(name = "member_id")
    private Integer            memberId;
    /** 手机号 */
    @Column(name = "mobile")
    private String            mobile;
    /** 短信验证码 */
    @Column(name = "token")
    private String            token;
    /** 上次验证时间 */
    @Column(name = "last_auth_time")
    private Date            lastAuthTime;
    /** 周期验证失败次数 */
    @Column(name = "fail_num")
    private Integer            failNum;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 修改时间 */
    @Column(name = "update_time")
    private Date            updateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


