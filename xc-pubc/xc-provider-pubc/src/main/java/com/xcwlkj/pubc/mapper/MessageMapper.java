/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.pubc.model.domain.Message;



/**
 * 消息数据库操作
 * <AUTHOR>
 * @version $Id: InitMessageReqModel.java, v 0.1 2019年10月18日 12时43分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface MessageMapper extends MyMapper<Message> {

    /**
	 * 分页查询消息
	 * 
	 * @param example
	 * @return
	 */
	List<Message> pageList(Message example);
}
