/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.task.TaskExecutor;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.sun.mail.smtp.SMTPAddressFailedException;
import com.xcwlkj.pubc.exceptions.PubcBusiErrorCode;
import com.xcwlkj.pubc.exceptions.PubcBusiException;
import com.xcwlkj.pubc.model.constant.PubcApiConstant;
import com.xcwlkj.pubc.model.domain.EmailAnnex;
import com.xcwlkj.pubc.model.domain.EmailSendLogs;
import com.xcwlkj.pubc.model.dto.mail.MailEntity;
import com.xcwlkj.pubc.model.dto.mail.Send2MailDTO;
import com.xcwlkj.pubc.service.EmailAnnexService;
import com.xcwlkj.pubc.service.EmailSendLogsService;
import com.xcwlkj.pubc.service.FreeMarkerService;
import com.xcwlkj.pubc.service.SendMailService;
import com.xcwlkj.util.CheckParamUtil;
import com.xcwlkj.util.FileUtil;
import com.xcwlkj.util.ObjectUtil;
import com.xcwlkj.util.ValidateUtil;

import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @version $Id: SendMailServiceImpl.java, v 0.1 2018年8月28日 下午5:54:57 danfeng.zhou Exp $
 */
@Slf4j
@Service
@Transactional
public class SendMailServiceImpl implements SendMailService {
    @Resource
    private TaskExecutor      taskExecutor;
    @Resource
    private FreeMarkerService optVelocityService;

    @Resource
    private JavaMailSender    mailSender;

    @Value("${spring.mail.username}")
    private String from;
    @Value("${xc.local.oss.path}")
    private String tempFilePath;
    @Resource
    private EmailSendLogsService emailSendLogsService;
    @Resource
    private EmailAnnexService emailAnnexService;

    @Async
    @Override
    public void sendMail(Send2MailDTO send2EmailDTO) {
        log.info("开始向邮箱【{}】发送邮件", send2EmailDTO.getTo());
        Date date = new Date();
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = null;
        List<String> delPaths = new ArrayList<>();
        try {
            mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
            String nick = "";
            try {
                // 使用邮箱别名
                nick = javax.mail.internet.MimeUtility.encodeText(PubcApiConstant.EMAIL_NICK_NAME);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            mimeMessageHelper.setFrom(new InternetAddress(nick + "<" + from + ">"));
            mimeMessageHelper.setTo(send2EmailDTO.getTo());
            mimeMessageHelper.setSubject(send2EmailDTO.getSubject());
            mimeMessageHelper.setText(send2EmailDTO.getText(), true);

            //  邮件发送记录
            EmailSendLogs emailSendLogs = new EmailSendLogs();
            emailSendLogs.setRelativeId(send2EmailDTO.getRelativeId());
            emailSendLogs.setFromEmail(from);
            emailSendLogs.setToName(send2EmailDTO.getToName());
            emailSendLogs.setToEmail(send2EmailDTO.getTo());
            emailSendLogs.setContent(send2EmailDTO.getText());
            emailSendLogs.setSubject(send2EmailDTO.getSubject());
            emailSendLogs.setCreatorId(send2EmailDTO.getCurrentUserId());
            emailSendLogs.setCreatedBy(send2EmailDTO.getCurrentUserName());
            emailSendLogs.setCreatedTime(date);
            emailSendLogs.setLastOperatorId(send2EmailDTO.getCurrentUserId());
            emailSendLogs.setLastOperator(send2EmailDTO.getCurrentUserName());
            emailSendLogs.setLastOperatorTime(date);
            emailSendLogsService.save(emailSendLogs);

            if (send2EmailDTO.getOssFilePath() != null) {
                List<EmailAnnex> emailAnnexList = new ArrayList<>();
                for (String filePath : send2EmailDTO.getOssFilePath()) {
                    String fileName = UUID.randomUUID().toString().replace("-", "");
                    String pathname = tempFilePath + fileName;
                    FileUtil.file(filePath, pathname);
                    // 邮件附件
                    File file = new File(pathname);
                    FileSystemResource fileSystemResource = new FileSystemResource(file);
                    mimeMessageHelper.addAttachment(file.getName(), fileSystemResource);
                    delPaths.add(pathname);

                    EmailAnnex emailAnnex = new EmailAnnex();
                    emailAnnex.setOsspath(filePath);
                    emailAnnex.setFileName(fileName);
                    emailAnnex.setRelativeId(emailSendLogs.getId());
                    emailAnnex.setCreatorId(send2EmailDTO.getCurrentUserId());
                    emailAnnex.setCreatedBy(send2EmailDTO.getCurrentUserName());
                    emailAnnex.setCreatedTime(date);
                    emailAnnex.setLastOperatorId(send2EmailDTO.getCurrentUserId());
                    emailAnnex.setLastOperator(send2EmailDTO.getCurrentUserName());
                    emailAnnex.setLastOperatorTime(date);
                    emailAnnexList.add(emailAnnex);
                }
                if (!emailAnnexList.isEmpty()) {
                    // 邮件附件记录
                    emailAnnexService.batchSave(emailAnnexList);
                }
            }

            // 发送邮件
            mailSender.send(mimeMessage);
            delPaths.forEach(s -> {
                // 删除文件
                FileUtil.deleteFile(s);
            });
        } catch (SMTPAddressFailedException e) {
            log.error("邮件发送失败，邮箱地址错误，检查邮箱输入是否有误 ex={}", e.getMessage(), e);
        } catch (MessagingException e) {
            log.error("邮件发送失败 ex={}", e.getMessage(), e);
        }
        log.info("邮件发送成功");
    }

    @Override
    public int sendSimpleMail(String subject, String text, Set<String> to) {
        log.info("sendSimpleMail - 发送简单邮件. subject={}, text={}, to={}", subject, text, to);
        int result = 1;
        try {
            SimpleMailMessage message = MailEntity.createSimpleMailMessage(subject, text, to);
            message.setFrom(from);
            taskExecutor.execute(() -> mailSender.send(message));
        } catch (Exception e) {
            log.info("sendSimpleMail [FAIL] ex={}", e.getMessage(), e);
            result = 0;
        }
        return result;
    }

    @Override
    public int sendTemplateMail(String subject, String text, Set<String> to) {
        log.info("sendTemplateMail - 发送模板邮件. subject={}, text={}, to={}", subject, text, to);
        int result = 1;
        try {
            MimeMessage mimeMessage = getMimeMessage(subject, text, to);
            taskExecutor.execute(() -> mailSender.send(mimeMessage));
        } catch (Exception e) {
            log.info("sendTemplateMail [FAIL] ex={}", e.getMessage(), e);
            result = 0;
        }
        return result;
    }

    private MimeMessage getMimeMessage(String subject, String text, Set<String> to) {
        CheckParamUtil.checkStringNotBlank(subject, "发送主题为空");
        CheckParamUtil.checkStringNotBlank(text, "发送内容为空");
        CheckParamUtil.checkObjectNotNull(to, "收件人列表为空");
        CheckParamUtil.checkCollectionNotNullAndSizeGreaterZero(to, "收件人列表为空");
        String[] toArray = setToArray(to);
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper helper;
        try {
            helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(from);
            helper.setTo(toArray);
            helper.setSubject(subject);
            helper.setText(text, true);
        } catch (MessagingException e) {
            log.error("生成邮件消息体, 出现异常={}", e.getMessage(), e);
            throw new PubcBusiException(PubcBusiErrorCode.PRODUCE_MAIL_MESSAGE_BODY_ERROR,e);
        }
        return mimeMessage;
    }

    @Override
    public int sendTemplateMail(Map<String, Object> model, String templateLocation, String subject, Set<String> to) {
        log.info("sendTemplateMail - 发送模板邮件. subject={}, model={}, to={}, templateLocation={}", subject, model, to, templateLocation);

        String text;
        try {
            text = optVelocityService.getTemplate(model, templateLocation);
        } catch (IOException | TemplateException e) {
            log.info("sendTemplateMail [FAIL] ex={}", e.getMessage(), e);
            throw new PubcBusiException(PubcBusiErrorCode.MAIL_TEMPLATE_NOT_FOUND, e);
        }
        return this.sendTemplateMail(subject, text, to);
    }

    private String[] setToArray(Set<String> to) {
        Preconditions.checkArgument(ObjectUtil.isNotEmpty(to), "请输入收件人邮箱");

        Set<String> toSet = Sets.newHashSet();
        for (String toStr : to) {
            toStr = toStr.trim();
            if (ValidateUtil.isEmail(toStr)) {
                toSet.add(toStr);
            }
        }
        if (ObjectUtil.isEmpty(toSet)) {
            return null;
        }
        return toSet.toArray(new String[toSet.size()]);
    }

}
