/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import com.xcwlkj.pubc.mapper.UserDepartmentRelationMapper;
import com.xcwlkj.pubc.model.domain.UserDepartmentRelation;
import com.xcwlkj.pubc.service.UserDepartmentRelationService;
import com.xcwlkj.pubc.share.seq.SequenceEnum;
import com.xcwlkj.pubc.share.seq.service.SequenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;


/**
 * 用户部门关联服务
 * <AUTHOR>
 * @version $Id: UserDepartmentRelationServiceImpl.java, v 0.1 2020年09月01日 01时09分 xcwlkj.com Exp $
 */
@Service("userDepartmentRelationService")
public class UserDepartmentRelationServiceImpl  implements UserDepartmentRelationService  {

    @Resource
    private UserDepartmentRelationMapper modelMapper;
	@Autowired
	private SequenceService sequenceService;

	@Override
	public void save(UserDepartmentRelation userDepartmentRelation) {
		String nbr = sequenceService.createKeyNo(SequenceEnum.USER_DEPARTMENT_RELATION);
		userDepartmentRelation.setId(nbr);
		modelMapper.insertSelective(userDepartmentRelation);
	}

	@Override
	public void deleteByUserId(String userId) {
		Example example = new Example(UserDepartmentRelation.class);
		example.createCriteria().andEqualTo("userId", userId);
		
		modelMapper.deleteByExample(example);
		
	}
    
	
	
    
    

}