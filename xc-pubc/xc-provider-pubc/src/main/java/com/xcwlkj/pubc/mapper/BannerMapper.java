/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.pubc.model.domain.Banner;



/**
 * banner数据库操作
 * <AUTHOR>
 * @version $Id: InitBannerReqModel.java, v 0.1 2019年10月22日 12时22分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface BannerMapper extends MyMapper<Banner> {

    /**
	 * 分页查询banner
	 * 
	 * @param example
	 * @return
	 */
	List<Banner> pageList(Banner example);
}
