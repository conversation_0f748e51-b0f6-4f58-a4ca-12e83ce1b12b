/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.sms.channel.yimei.service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.xcwlkj.pubc.model.dto.sms.SaveSmsSendLogsDTO;
import com.xcwlkj.pubc.model.enums.SendSmsBusiTypeEnum;
import com.xcwlkj.pubc.service.sms.channel.yimei.model.req.SmsSingleRequest;
import com.xcwlkj.pubc.service.sms.channel.yimei.model.resp.ResultModel;
import com.xcwlkj.pubc.service.sms.channel.yimei.model.resp.SmsResponse;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.AES;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.GZIPUtils;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.JsonHelper;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.http.HttpClient;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.http.HttpRequest;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.http.HttpRequestBytes;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.http.HttpRequestParams;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.http.HttpResponseBytes;
import com.xcwlkj.pubc.service.sms.channel.yimei.util.http.HttpResponseBytesPraser;
import com.xcwlkj.pubc.service.sms.impl.AbstractSmsService;
import com.xcwlkj.util.CheckParamUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 亿美
 * 
 * <AUTHOR>
 * @version 2019年8月9日
 */
@Slf4j
@Service("yiMeiSmsService")
public class YiMeiSmsServiceImpl extends AbstractSmsService {

    @Value("${xc.yimei.key.appId}")
    private String        appId;
    @Value("${xc.yimei.key.secretKey}")
    private String        secretKey;
    @Value("${xc.yimei.ip.baseUrl}")
    private String        baseUrl;
    @Value("${xc.yimei.ip.sendSingleSmsUrl}")
    private String        sendSingleSmsUrl;

    /** 
     * @see com.xcwlkj.service.sms.SmsService#sendSingleSms(java.lang.String, java.lang.String)
     */
    @Override
    public SaveSmsSendLogsDTO sendSingleSms(String busiType, String mobile, Object... params) {
        log.info("亿美短信接口  busiType={}, mobile={}", busiType, mobile);
        CheckParamUtil.checkObjectNotNull(busiType, "业务类型不能为空");
        CheckParamUtil.checkStringNotBlank(mobile, "手机号不能为空");

        //检查当天短信次数
        this.checkSendCount(busiType, mobile);

        //检查发送时间间隔
        this.checkSendSpace(busiType, mobile);

        //生成验证码
        String mobileCode = this.getMobileCode();

        //获取模板内容,加上验证码生成内容
        SendSmsBusiTypeEnum sendSmsBusiTypeEnum = SendSmsBusiTypeEnum.valueOfCode(busiType);
        String content = String.format(sendSmsBusiTypeEnum.getMould(), mobileCode);

        //获取当前短信发送时间
        Date nowSendTime = new Date();

        //调用亿美短信接口
        SmsSingleRequest pamars = new SmsSingleRequest();
        pamars.setContent(content);
        pamars.setMobile(mobile);
        ResultModel result = request(appId, secretKey, "AES", pamars,
            baseUrl + sendSingleSmsUrl, true, "UTF-8");
        String serno = null;
        if ("SUCCESS".equals(result.getCode())) {
            SmsResponse response = JsonHelper.fromJson(SmsResponse.class, result.getResult());
            serno = response.getSmsId();
        }

        //第三方返回,需要入库的数据
        SaveSmsSendLogsDTO saveSmsSendLogsDTO = new SaveSmsSendLogsDTO();
        saveSmsSendLogsDTO.setMobileCode(mobileCode);
        saveSmsSendLogsDTO.setContent(content);
        saveSmsSendLogsDTO.setSendTime(nowSendTime);
        saveSmsSendLogsDTO.setSerno(serno);
        saveSmsSendLogsDTO.setReqStatus(result.getCode());

        return saveSmsSendLogsDTO;

    }

    /**
     * 亿美公共请求方法
     */
    public static ResultModel request(String appId, String secretKey, String algorithm,
                                      Object content, String url, final boolean isGzip,
                                      String encode) {
        Map<String, String> headers = new HashMap<String, String>();
        HttpRequest<byte[]> request = null;
        try {
            headers.put("appId", appId);
            headers.put("encode", encode);
            String requestJson = JsonHelper.toJsonString(content);
            byte[] bytes = requestJson.getBytes(encode);
            if (isGzip) {
                headers.put("gzip", "on");
                bytes = GZIPUtils.compress(bytes);
            }
            byte[] parambytes = AES.encrypt(bytes, secretKey.getBytes(), algorithm);
            HttpRequestParams<byte[]> params = new HttpRequestParams<byte[]>();
            params.setCharSet("UTF-8");
            params.setMethod("POST");
            params.setHeaders(headers);
            params.setParams(parambytes);
            params.setUrl(url);
            request = new HttpRequestBytes(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        HttpClient client = new HttpClient();
        String code = null;
        String result = null;
        try {
            HttpResponseBytes res = client.service(request, new HttpResponseBytesPraser());
            if (res == null) {
                return new ResultModel(code, result);
            }
            if (res.getHttpCode() == 200) {
                code = res.getHeaders().get("result");
                if (code.equals("SUCCESS")) {
                    byte[] data = res.getResult();
                    data = AES.decrypt(data, secretKey.getBytes(), algorithm);
                    if (isGzip) {
                        data = GZIPUtils.decompress(data);
                    }
                    result = new String(data, encode);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        ResultModel re = new ResultModel(code, result);
        return re;
    }

}
