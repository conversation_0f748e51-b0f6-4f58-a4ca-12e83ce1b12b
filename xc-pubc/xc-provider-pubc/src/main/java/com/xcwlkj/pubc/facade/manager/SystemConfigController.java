/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.pubc.facade.manager;

import com.xcwlkj.base.BaseController;
import com.xcwlkj.console.model.dto.systemconfig.ModifySystemConfigDTO;
import com.xcwlkj.console.model.dto.systemconfig.QuerySystemConfigDTO;
import com.xcwlkj.console.model.dto.systemconfig.SaveSystemConfigDTO;
import com.xcwlkj.console.model.req.systemconfig.DeleteSystemConfigByIdReqModel;
import com.xcwlkj.console.model.req.systemconfig.QuerySystemConfigDetailByIdReqModel;
import com.xcwlkj.console.model.req.systemconfig.QuerySystemConfigListWithPageReqModel;
import com.xcwlkj.console.model.req.systemconfig.SaveSystemConfigReqModel;
import com.xcwlkj.console.model.resp.systemconfig.DeleteSystemConfigByIdRespModel;
import com.xcwlkj.console.model.resp.systemconfig.QuerySystemConfigDetailByIdRespModel;
import com.xcwlkj.console.model.resp.systemconfig.QuerySystemConfigListWithPageRespModel;
import com.xcwlkj.console.model.resp.systemconfig.SaveSystemConfigRespModel;
import com.xcwlkj.console.model.vo.systemconfig.QuerySystemConfigListVO;
import com.xcwlkj.console.model.vo.systemconfig.SystemConfigDetailVO;
import com.xcwlkj.pubc.service.SystemConfigService;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * system_config控制层
 * <AUTHOR>
 * @version $Id: SystemConfigController.java, v 0.1 2019年08月25日 11时32分 XcDev Exp $
 */
@RestController
@RequestMapping(value = "/manager/pubc", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class SystemConfigController extends BaseController {

    @Resource
    private SystemConfigService systemConfigService;

    /**
     * system_config查询列表
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/systemConfig/querySystemConfigListWithPage")
    public Wrapper<QuerySystemConfigListWithPageRespModel> querySystemConfigrListWithPage(@RequestBody QuerySystemConfigListWithPageReqModel reqModel) {
        logger.info("system_config查询列表 QuerySystemConfigListWithPageReqModel={}", reqModel);
        QuerySystemConfigDTO systemConfigDto = new QuerySystemConfigDTO();
        BeanUtil.copyProperties(reqModel, systemConfigDto);
        systemConfigDto.setPageNum(reqModel.getPageNum());
        systemConfigDto.setPageSize(reqModel.getPageSize());
        QuerySystemConfigListVO result = systemConfigService.querySystemConfigListWithPage(systemConfigDto);
        QuerySystemConfigListWithPageRespModel respModel = new QuerySystemConfigListWithPageRespModel();
        respModel.setSystemConfigSummaryVOList(result.getSystemConfigSummaryVOList());
        respModel.setTotalRows(result.getTotalRows());
        logger.info("system_config查询列表 QuerySystemConfigListWithPageRespModel={}", respModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
    * system_config新增/修改
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/systemConfig/saveSystemConfig")
    public Wrapper<SaveSystemConfigRespModel> saveSystemConfig(@RequestBody SaveSystemConfigReqModel reqModel) {
        logger.info("system_config新增/修改 SaveSystemConfigReqModel={}", reqModel);
        SaveSystemConfigRespModel respModel = new SaveSystemConfigRespModel();
        if (StringUtil.isNotBlank(reqModel.getId())) {
            ModifySystemConfigDTO modifySystemConfigDto = new ModifySystemConfigDTO();
            BeanUtil.copyProperties(reqModel, modifySystemConfigDto);
            systemConfigService.modifySystemConfigById(modifySystemConfigDto);

        } else {
            SaveSystemConfigDTO saveSystemConfigDto = new SaveSystemConfigDTO();
            BeanUtil.copyProperties(reqModel, saveSystemConfigDto);
            systemConfigService.saveSystemConfig(saveSystemConfigDto);
        }
        logger.info("system_config新增/修改 SaveSystemConfigRespModel={}", respModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
    * system_config删除
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/systemConfig/deleteSystemConfigById")
    public Wrapper<DeleteSystemConfigByIdRespModel> deleteSystemConfigById(@RequestBody DeleteSystemConfigByIdReqModel reqModel) {
        logger.info("system_config删除 DeleteSystemConfigByIdReqModel={}", reqModel);
        DeleteSystemConfigByIdRespModel respModel = new DeleteSystemConfigByIdRespModel();
        systemConfigService.deleteSystemConfigById(reqModel.getSystemConfigIds());
        logger.info("system_config删除 DeleteSystemConfigByIdRespModel={}", respModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
    * system_config查询
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/systemConfig/getSystemConfigById")
    public Wrapper<QuerySystemConfigDetailByIdRespModel> getSystemConfigById(@RequestBody QuerySystemConfigDetailByIdReqModel reqModel) {
        logger.info("system_config查询 QuerySystemConfigDetailByIdReqModel={}", reqModel);
        QuerySystemConfigDetailByIdRespModel respModel = new QuerySystemConfigDetailByIdRespModel();
        
        SystemConfigDetailVO result = systemConfigService.getSystemConfigById(reqModel.getId());
        respModel.setSystemConfigDetailVO(result);
        logger.info("system_config查询 QuerySystemConfigDetailByIdRespModel={}", respModel);
        return WrapMapper.ok(reqModel, respModel);
    }

}
