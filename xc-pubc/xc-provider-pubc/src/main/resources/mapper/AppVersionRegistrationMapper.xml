<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.pubc.mapper.AppVersionRegistrationMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.pubc.model.domain.AppVersionRegistration">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="app_type" jdbcType="VARCHAR" property="appType" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="app_version" jdbcType="VARCHAR" property="appVersion" />
        <result column="is_mandatory" jdbcType="VARCHAR" property="isMandatory" />
        <result column="is_prompt" jdbcType="VARCHAR" property="isPrompt" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="update_content" jdbcType="VARCHAR" property="updateContent" />
        <result column="designated_user_id" jdbcType="VARCHAR" property="designatedUserId" />
        <result column="designated_user_name" jdbcType="VARCHAR" property="designatedUserName" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="last_operator_time" jdbcType="TIMESTAMP" property="lastOperatorTime" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
        <result column="last_operator" jdbcType="VARCHAR" property="lastOperator" />
        <result column="last_operator_id" jdbcType="VARCHAR" property="lastOperatorId" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        app_type,
        version,
        app_version,
        is_mandatory,
        is_prompt,
        url,
        update_content,
        designated_user_id,
        designated_user_name,
        created_time,
        last_operator_time,
        created_by,
        creator_id,
        last_operator,
        last_operator_id

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="appType != null and appType != ''">
            AND app_type = #{appType,jdbcType=VARCHAR}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version,jdbcType=BIGINT}
        </if>
        <if test="appVersion != null and appVersion != ''">
            AND app_version = #{appVersion,jdbcType=VARCHAR}
        </if>
        <if test="isMandatory != null and isMandatory != ''">
            AND is_mandatory = #{isMandatory,jdbcType=VARCHAR}
        </if>
        <if test="isPrompt != null and isPrompt != ''">
            AND is_prompt = #{isPrompt,jdbcType=VARCHAR}
        </if>
        <if test="url != null and url != ''">
            AND url = #{url,jdbcType=VARCHAR}
        </if>
        <if test="updateContent != null and updateContent != ''">
            AND update_content = #{updateContent,jdbcType=VARCHAR}
        </if>
        <if test="designatedUserId != null and designatedUserId != ''">
            AND designated_user_id = #{designatedUserId,jdbcType=VARCHAR}
        </if>
        <if test="designatedUserName != null and designatedUserName != ''">
            AND designated_user_name = #{designatedUserName,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time = #{createdTime,jdbcType=TIMESTAMP}
        </if>
        <if test="lastOperatorTime != null and lastOperatorTime != ''">
            AND last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        <if test="creatorId != null and creatorId != ''">
            AND creator_id = #{creatorId,jdbcType=VARCHAR}
        </if>
        <if test="lastOperator != null and lastOperator != ''">
            AND last_operator = #{lastOperator,jdbcType=VARCHAR}
        </if>
        <if test="lastOperatorId != null and lastOperatorId != ''">
            AND last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="appType != null ">
            app_type = #{appType,jdbcType=VARCHAR},
        </if>
        <if test="version != null ">
            version = #{version,jdbcType=BIGINT},
        </if>
        <if test="appVersion != null ">
            app_version = #{appVersion,jdbcType=VARCHAR},
        </if>
        <if test="isMandatory != null ">
            is_mandatory = #{isMandatory,jdbcType=VARCHAR},
        </if>
        <if test="isPrompt != null ">
            is_prompt = #{isPrompt,jdbcType=VARCHAR},
        </if>
        <if test="url != null ">
            url = #{url,jdbcType=VARCHAR},
        </if>
        <if test="updateContent != null ">
            update_content = #{updateContent,jdbcType=VARCHAR},
        </if>
        <if test="designatedUserId != null ">
            designated_user_id = #{designatedUserId,jdbcType=VARCHAR},
        </if>
        <if test="designatedUserName != null ">
            designated_user_name = #{designatedUserName,jdbcType=VARCHAR},
        </if>
        <if test="createdTime != null ">
            created_time = #{createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="lastOperatorTime != null ">
            last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP},
        </if>
        <if test="createdBy != null ">
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test="creatorId != null ">
            creator_id = #{creatorId,jdbcType=VARCHAR},
        </if>
        <if test="lastOperator != null ">
            last_operator = #{lastOperator,jdbcType=VARCHAR},
        </if>
        <if test="lastOperatorId != null ">
            last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.pubc.model.domain.AppVersionRegistration"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from app_version_registration
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
	
	<!-- 根据appType查询app表里最新版本信息 -->
	<select id="getAppVersionLatestMsg" resultMap="BaseResultMap">
		SELECT app_version,is_mandatory,is_prompt,url,update_content 
		FROM app_version_registration 
		WHERE (FIND_IN_SET(#{userId},designated_user_id) OR designated_user_id='')
		AND app_type=#{appType}
		AND app_version =
		(SELECT MAX(app_version) FROM app_version_registration WHERE app_type=#{appType} AND (FIND_IN_SET(#{userId},designated_user_id) OR designated_user_id=''))
	</select>

    <!-- 根据appName查询app表里最新版本信息 -->
    <select id="appVersionLatest" resultType="com.xcwlkj.pubc.model.vo.appversion.VersionLatestVO">
        select * from (
        SELECT app_version,
        version,
        url pkgUrl,
        is_mandatory mandatory,
        app_type appType,
        update_content updateContent,
        is_prompt prompt,
        app_name appName
        FROM app_version_registration
        <where>
            app_name = #{appName}
            <if test="appVersion != null and appVersion != ''">
                and app_version = #{appVersion}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
        </where>
        order by version desc ) tmp WHERE ROWNUM = 1
    </select>
</mapper>
