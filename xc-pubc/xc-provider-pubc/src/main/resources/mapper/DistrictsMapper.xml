<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.pubc.mapper.DistrictsMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.pubc.model.domain.Districts">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="pid" jdbcType="VARCHAR" property="pid" />
        <result column="initial" jdbcType="VARCHAR" property="initial" />
        <result column="initials" jdbcType="VARCHAR" property="initials" />
        <result column="pinyin" jdbcType="VARCHAR" property="pinyin" />
        <result column="suffix" jdbcType="VARCHAR" property="suffix" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
        <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
        <result column="level" jdbcType="TINYINT" property="level" />
        <result column="is_leaf" jdbcType="VARCHAR" property="isLeaf" />
        <result column="order_seq" jdbcType="TINYINT" property="orderSeq" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="last_operator_time" jdbcType="TIMESTAMP" property="lastOperatorTime" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
        <result column="last_operator" jdbcType="VARCHAR" property="lastOperator" />
        <result column="last_operator_id" jdbcType="VARCHAR" property="lastOperatorId" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        version,
        name,
        pid,
        initial,
        initials,
        pinyin,
        suffix,
        code,
        district_code,
        area_code,
        level,
        is_leaf,
        order_seq,
        created_time,
        last_operator_time,
        created_by,
        creator_id,
        last_operator,
        last_operator_id

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version,jdbcType=BIGINT}
        </if>
        <if test="name != null and name != ''">
            AND name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="pid != null and pid != ''">
            AND pid = #{pid,jdbcType=VARCHAR}
        </if>
        <if test="initial != null and initial != ''">
            AND initial = #{initial,jdbcType=VARCHAR}
        </if>
        <if test="initials != null and initials != ''">
            AND initials = #{initials,jdbcType=VARCHAR}
        </if>
        <if test="pinyin != null and pinyin != ''">
            AND pinyin = #{pinyin,jdbcType=VARCHAR}
        </if>
        <if test="suffix != null and suffix != ''">
            AND suffix = #{suffix,jdbcType=VARCHAR}
        </if>
        <if test="code != null and code != ''">
            AND code = #{code,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            AND district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="areaCode != null and areaCode != ''">
            AND area_code = #{areaCode,jdbcType=VARCHAR}
        </if>
        <if test="level != null and level != ''">
            AND level = #{level,jdbcType=TINYINT}
        </if>
        <if test="isLeaf != null and isLeaf != ''">
            AND is_leaf = #{isLeaf,jdbcType=VARCHAR}
        </if>
        <if test="orderSeq != null and orderSeq != ''">
            AND order_seq = #{orderSeq,jdbcType=TINYINT}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time = #{createdTime,jdbcType=TIMESTAMP}
        </if>
        <if test="lastOperatorTime != null and lastOperatorTime != ''">
            AND last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        <if test="creatorId != null and creatorId != ''">
            AND creator_id = #{creatorId,jdbcType=VARCHAR}
        </if>
        <if test="lastOperator != null and lastOperator != ''">
            AND last_operator = #{lastOperator,jdbcType=VARCHAR}
        </if>
        <if test="lastOperatorId != null and lastOperatorId != ''">
            AND last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="version != null ">
            version = #{version,jdbcType=BIGINT},
        </if>
        <if test="name != null ">
            name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="pid != null ">
            pid = #{pid,jdbcType=VARCHAR},
        </if>
        <if test="initial != null ">
            initial = #{initial,jdbcType=VARCHAR},
        </if>
        <if test="initials != null ">
            initials = #{initials,jdbcType=VARCHAR},
        </if>
        <if test="pinyin != null ">
            pinyin = #{pinyin,jdbcType=VARCHAR},
        </if>
        <if test="suffix != null ">
            suffix = #{suffix,jdbcType=VARCHAR},
        </if>
        <if test="code != null ">
            code = #{code,jdbcType=VARCHAR},
        </if>
        <if test="districtCode != null ">
            district_code = #{districtCode,jdbcType=VARCHAR},
        </if>
        <if test="areaCode != null ">
            area_code = #{areaCode,jdbcType=VARCHAR},
        </if>
        <if test="level != null ">
            level = #{level,jdbcType=TINYINT},
        </if>
        <if test="isLeaf != null ">
            is_leaf = #{isLeaf,jdbcType=VARCHAR},
        </if>
        <if test="orderSeq != null ">
            order_seq = #{orderSeq,jdbcType=TINYINT},
        </if>
        <if test="createdTime != null ">
            created_time = #{createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="lastOperatorTime != null ">
            last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP},
        </if>
        <if test="createdBy != null ">
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test="creatorId != null ">
            creator_id = #{creatorId,jdbcType=VARCHAR},
        </if>
        <if test="lastOperator != null ">
            last_operator = #{lastOperator,jdbcType=VARCHAR},
        </if>
        <if test="lastOperatorId != null ">
            last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.pubc.model.domain.Districts"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from districts
		where 1=1
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
	
	<select id="getAllProvince" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from districts
		where pid=0
	</select>
	
	<select id="getChildByParentName"  parameterType="string" resultMap="BaseResultMap">
		select
			child.id,
	        child.version,
	        child.name,
	        child.pid,
	        child.initial,
	        child.initials,
	        child.pinyin,
	        child.suffix,
	        child.code,
	        child.district_code,
	        child.area_code,
	        child.level,
	        child.is_leaf,
	        child.order_seq,
	        child.created_time,
	        child.last_operator_time,
	        child.created_by,
	        child.creator_id,
	        child.last_operator,
	        child.last_operator_id
		from districts parent,districts child
		where parent.name=#{parentName}
		and parent.id=child.pid
	</select>
</mapper>
