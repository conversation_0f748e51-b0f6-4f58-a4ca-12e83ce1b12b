<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.pubc.mapper.MessageCategoryMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.pubc.model.domain.MessageCategory">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="logo" jdbcType="VARCHAR" property="logo" />
        <result column="summary" jdbcType="VARCHAR" property="summary" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="last_operator_time" jdbcType="TIMESTAMP" property="lastOperatorTime" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
        <result column="last_operator" jdbcType="VARCHAR" property="lastOperator" />
        <result column="last_operator_id" jdbcType="VARCHAR" property="lastOperatorId" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        name,
        logo,
        summary,
        status,
        tenant_id,
        version,
        created_time,
        last_operator_time,
        created_by,
        creator_id,
        last_operator,
        last_operator_id

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != ''">
            AND name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="logo != null and logo != ''">
            AND logo = #{logo,jdbcType=VARCHAR}
        </if>
        <if test="summary != null and summary != ''">
            AND summary = #{summary,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version,jdbcType=BIGINT}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time = #{createdTime,jdbcType=TIMESTAMP}
        </if>
        <if test="lastOperatorTime != null and lastOperatorTime != ''">
            AND last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        <if test="creatorId != null and creatorId != ''">
            AND creator_id = #{creatorId,jdbcType=VARCHAR}
        </if>
        <if test="lastOperator != null and lastOperator != ''">
            AND last_operator = #{lastOperator,jdbcType=VARCHAR}
        </if>
        <if test="lastOperatorId != null and lastOperatorId != ''">
            AND last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="name != null ">
            name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="logo != null ">
            logo = #{logo,jdbcType=VARCHAR},
        </if>
        <if test="summary != null ">
            summary = #{summary,jdbcType=VARCHAR},
        </if>
        <if test="status != null ">
            status = #{status,jdbcType=VARCHAR},
        </if>
        <if test="tenantId != null ">
            tenant_id = #{tenantId,jdbcType=VARCHAR},
        </if>
        <if test="version != null ">
            version = #{version,jdbcType=BIGINT},
        </if>
        <if test="createdTime != null ">
            created_time = #{createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="lastOperatorTime != null ">
            last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP},
        </if>
        <if test="createdBy != null ">
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test="creatorId != null ">
            creator_id = #{creatorId,jdbcType=VARCHAR},
        </if>
        <if test="lastOperator != null ">
            last_operator = #{lastOperator,jdbcType=VARCHAR},
        </if>
        <if test="lastOperatorId != null ">
            last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.pubc.model.domain.MessageCategory"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from message_category
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
