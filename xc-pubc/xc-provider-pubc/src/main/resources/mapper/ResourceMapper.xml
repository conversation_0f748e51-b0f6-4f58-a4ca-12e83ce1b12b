<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.pubc.mapper.ResourceMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.pubc.model.domain.Resource">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="leaf" jdbcType="TINYINT" property="leaf" />
        <result column="level" jdbcType="BIGINT" property="level" />
        <result column="icon" jdbcType="VARCHAR" property="icon" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="method" jdbcType="VARCHAR" property="method" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="order_num" jdbcType="BIGINT" property="orderNum" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="last_operator_time" jdbcType="TIMESTAMP" property="lastOperatorTime" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
        <result column="last_operator" jdbcType="VARCHAR" property="lastOperator" />
        <result column="last_operator_id" jdbcType="VARCHAR" property="lastOperatorId" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        version,
        parent_id,
        code,
        name,
        type,
        leaf,
        level,
        icon,
        url,
        method,
        description,
        order_num,
        created_time,
        last_operator_time,
        created_by,
        creator_id,
        last_operator,
        last_operator_id

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version,jdbcType=BIGINT}
        </if>
        <if test="parentId != null and parentId != ''">
            AND parent_id = #{parentId,jdbcType=VARCHAR}
        </if>
        <if test="code != null and code != ''">
            AND code = #{code,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != ''">
            AND name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type,jdbcType=TINYINT}
        </if>
        <if test="leaf != null and leaf != ''">
            AND leaf = #{leaf,jdbcType=TINYINT}
        </if>
        <if test="level != null and level != ''">
            AND level = #{level,jdbcType=BIGINT}
        </if>
        <if test="icon != null and icon != ''">
            AND icon = #{icon,jdbcType=VARCHAR}
        </if>
        <if test="url != null and url != ''">
            AND url = #{url,jdbcType=VARCHAR}
        </if>
        <if test="method != null and method != ''">
            AND method = #{method,jdbcType=VARCHAR}
        </if>
        <if test="description != null and description != ''">
            AND description = #{description,jdbcType=VARCHAR}
        </if>
        <if test="orderNum != null and orderNum != ''">
            AND order_num = #{orderNum,jdbcType=BIGINT}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time = #{createdTime,jdbcType=TIMESTAMP}
        </if>
        <if test="lastOperatorTime != null and lastOperatorTime != ''">
            AND last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        <if test="creatorId != null and creatorId != ''">
            AND creator_id = #{creatorId,jdbcType=VARCHAR}
        </if>
        <if test="lastOperator != null and lastOperator != ''">
            AND last_operator = #{lastOperator,jdbcType=VARCHAR}
        </if>
        <if test="lastOperatorId != null and lastOperatorId != ''">
            AND last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="version != null ">
            version = #{version,jdbcType=BIGINT},
        </if>
        <if test="parentId != null ">
            parent_id = #{parentId,jdbcType=VARCHAR},
        </if>
        <if test="code != null ">
            code = #{code,jdbcType=VARCHAR},
        </if>
        <if test="name != null ">
            name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="type != null ">
            type = #{type,jdbcType=TINYINT},
        </if>
        <if test="leaf != null ">
            leaf = #{leaf,jdbcType=TINYINT},
        </if>
        <if test="level != null ">
            level = #{level,jdbcType=BIGINT},
        </if>
        <if test="icon != null ">
            icon = #{icon,jdbcType=VARCHAR},
        </if>
        <if test="url != null ">
            url = #{url,jdbcType=VARCHAR},
        </if>
        <if test="method != null ">
            method = #{method,jdbcType=VARCHAR},
        </if>
        <if test="description != null ">
            description = #{description,jdbcType=VARCHAR},
        </if>
        <if test="orderNum != null ">
            order_num = #{orderNum,jdbcType=BIGINT},
        </if>
        <if test="createdTime != null ">
            created_time = #{createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="lastOperatorTime != null ">
            last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP},
        </if>
        <if test="createdBy != null ">
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test="creatorId != null ">
            creator_id = #{creatorId,jdbcType=VARCHAR},
        </if>
        <if test="lastOperator != null ">
            last_operator = #{lastOperator,jdbcType=VARCHAR},
        </if>
        <if test="lastOperatorId != null ">
            last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.pubc.model.domain.Resource"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from resource
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>

    <!-- 根据角色code查询到角色把对应的资源定义 -->
    <select id="queryByRoleCodes" resultMap="BaseResultMap">
        SELECT DISTINCT
        rs.code,rs.url,rs.name,rs.type,rs.leaf,rs.level,rs.method,rs.description
        FROM role r INNER JOIN
        role_resource_relation
        rrr ON r.id = rrr.role_id
        INNER JOIN resource rs ON rs.id = rrr.resource_id WHERE r.code IN
        <foreach collection="roleCodes" item="roleCode" index="index"
                 open="(" close=")" separator=",">
            #{roleCode}
        </foreach>
    </select>

    <!-- 根据角色code查询到角色把对应的资源定义 -->
    <select id="queryByRoleIds" resultMap="BaseResultMap">
        SELECT *
        FROM resource WHERE id IN
        <foreach collection="ids" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by parent_id, order_num
    </select>

    <select id="selectResourceTree" parameterType="java.lang.String" resultType="String">
		select concat(m.id)
		from resource m
			left join role_resource_relation rm on m.id = rm.resource_id
		where rm.role_id = #{roleId}
		order by m.parent_id, m.order_num
	</select>

    <select id="selectAllResourceTree" parameterType="java.lang.String" resultType="String">
		select concat(m.id)
		from resource m
			left join role_resource_relation rm on m.id = rm.resource_id
		order by m.parent_id, m.order_num
	</select>

</mapper>
