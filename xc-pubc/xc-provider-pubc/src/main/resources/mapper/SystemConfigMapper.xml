<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.pubc.mapper.SystemConfigMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.pubc.model.domain.SystemConfig">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="config_name" jdbcType="VARCHAR" property="configName" />
        <result column="config_key" jdbcType="VARCHAR" property="configKey" />
        <result column="config_value" jdbcType="VARCHAR" property="configValue" />
        <result column="config_type" jdbcType="VARCHAR" property="configType" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="last_operator_time" jdbcType="TIMESTAMP" property="lastOperatorTime" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
        <result column="last_operator" jdbcType="VARCHAR" property="lastOperator" />
        <result column="last_operator_id" jdbcType="VARCHAR" property="lastOperatorId" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        version,
        config_name,
        config_key,
        config_value,
        config_type,
        remark,
        created_time,
        last_operator_time,
        created_by,
        creator_id,
        last_operator,
        last_operator_id

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version,jdbcType=BIGINT}
        </if>
        <if test="configName != null and configName != ''">
            AND config_name like "%"#{configName,jdbcType=VARCHAR}"%"
        </if>
        <if test="configKey != null and configKey != ''">
            AND config_key like "%"#{configKey,jdbcType=VARCHAR}"%"
        </if>
        <if test="configValue != null and configValue != ''">
            AND config_value = #{configValue,jdbcType=VARCHAR}
        </if>
        <if test="configType != null and configType != ''">
            AND config_type = #{configType,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time = #{createdTime,jdbcType=TIMESTAMP}
        </if>
        <if test="lastOperatorTime != null and lastOperatorTime != ''">
            AND last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        <if test="creatorId != null and creatorId != ''">
            AND creator_id = #{creatorId,jdbcType=VARCHAR}
        </if>
        <if test="lastOperator != null and lastOperator != ''">
            AND last_operator = #{lastOperator,jdbcType=VARCHAR}
        </if>
        <if test="lastOperatorId != null and lastOperatorId != ''">
            AND last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="version != null ">
            version = #{version,jdbcType=BIGINT},
        </if>
        <if test="configName != null ">
            config_name = #{configName,jdbcType=VARCHAR},
        </if>
        <if test="configKey != null ">
            config_key = #{configKey,jdbcType=VARCHAR},
        </if>
        <if test="configValue != null ">
            config_value = #{configValue,jdbcType=VARCHAR},
        </if>
        <if test="configType != null ">
            config_type = #{configType,jdbcType=VARCHAR},
        </if>
        <if test="remark != null ">
            remark = #{remark,jdbcType=VARCHAR},
        </if>
        <if test="createdTime != null ">
            created_time = #{createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="lastOperatorTime != null ">
            last_operator_time = #{lastOperatorTime,jdbcType=TIMESTAMP},
        </if>
        <if test="createdBy != null ">
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test="creatorId != null ">
            creator_id = #{creatorId,jdbcType=VARCHAR},
        </if>
        <if test="lastOperator != null ">
            last_operator = #{lastOperator,jdbcType=VARCHAR},
        </if>
        <if test="lastOperatorId != null ">
            last_operator_id = #{lastOperatorId,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.pubc.model.domain.SystemConfig"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from system_config
		where 1=1
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
	
	<select id="queryConfigValueByKey" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from system_config where config_key = #{configKey,jdbcType=VARCHAR}
	</select>

    <select id="selectSystemConfigs" parameterType="com.xcwlkj.pubc.model.domain.SystemConfig"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from system_config
        where 1=1
        <if test="configName != null and configName != ''">
            and config_name like concat(#{configName,jdbcType=VARCHAR},'%')
        </if>
        <if test="configKey != null and configKey != ''">
            and config_key like concat(#{configKey,jdbcType=VARCHAR},'%')
        </if>
        <if test="configType != null and configType != ''">
            and config_type = #{configType,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
