<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.pubc.mapper.SmsAuthMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.pubc.model.domain.SmsAuth">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="scene_type" jdbcType="VARCHAR" property="sceneType" />
        <result column="member_id" jdbcType="BIGINT" property="memberId" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="token" jdbcType="VARCHAR" property="token" />
        <result column="last_auth_time" jdbcType="TIMESTAMP" property="lastAuthTime" />
        <result column="fail_num" jdbcType="BIGINT" property="failNum" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        scene_type,
        member_id,
        mobile,
        token,
        last_auth_time,
        fail_num,
        create_time,
        update_time

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=BIGINT}
        </if>
        <if test="sceneType != null and sceneType != ''">
            AND scene_type = #{sceneType,jdbcType=VARCHAR}
        </if>
        <if test="memberId != null and memberId != ''">
            AND member_id = #{memberId,jdbcType=BIGINT}
        </if>
        <if test="mobile != null and mobile != ''">
            AND mobile = #{mobile,jdbcType=VARCHAR}
        </if>
        <if test="token != null and token != ''">
            AND token = #{token,jdbcType=VARCHAR}
        </if>
        <if test="lastAuthTime != null and lastAuthTime != ''">
            AND last_auth_time = #{lastAuthTime,jdbcType=TIMESTAMP}
        </if>
        <if test="failNum != null and failNum != ''">
            AND fail_num = #{failNum,jdbcType=BIGINT}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=BIGINT},
        </if>
        <if test="sceneType != null ">
            scene_type = #{sceneType,jdbcType=VARCHAR},
        </if>
        <if test="memberId != null ">
            member_id = #{memberId,jdbcType=BIGINT},
        </if>
        <if test="mobile != null ">
            mobile = #{mobile,jdbcType=VARCHAR},
        </if>
        <if test="token != null ">
            token = #{token,jdbcType=VARCHAR},
        </if>
        <if test="lastAuthTime != null ">
            last_auth_time = #{lastAuthTime,jdbcType=TIMESTAMP},
        </if>
        <if test="failNum != null ">
            fail_num = #{failNum,jdbcType=BIGINT},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.pubc.model.domain.SmsAuth"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from sms_auth
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
