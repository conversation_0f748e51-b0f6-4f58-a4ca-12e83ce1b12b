{"info": {"name": "空考位统计查询接口测试", "description": "空考位统计查询和详情查询接口的测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "testKsjhbh", "value": "TEST2025001", "type": "string"}], "item": [{"name": "空考位统计查询", "item": [{"name": "基础查询-查询所有考场", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwtj", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwtj"]}}, "response": []}, {"name": "按场次查询-场次001", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"001\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwtj", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwtj"]}}, "response": []}, {"name": "按场次查询-场次002", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"002\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwtj", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwtj"]}}, "response": []}, {"name": "查询已上报考场", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"001\",\n  \"sbzt\": \"1\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwtj", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwtj"]}}, "response": []}, {"name": "查询未上报考场", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"001\",\n  \"sbzt\": \"0\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwtj", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwtj"]}}, "response": []}, {"name": "参数验证-缺少必填参数", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwtj", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwtj"]}}, "response": []}]}, {"name": "空考位详情查询", "item": [{"name": "基础查询-查询所有详情", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwxq", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwxq"]}}, "response": []}, {"name": "按场次查询详情", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"001\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwxq", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwxq"]}}, "response": []}, {"name": "查询缺考（空位）详情", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"001\",\n  \"yclx\": \"6\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwxq", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwxq"]}}, "response": []}, {"name": "查询无编排详情", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"001\",\n  \"yclx\": \"7\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwxq", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwxq"]}}, "response": []}, {"name": "按准考证号查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"zkzh\": \"202501003\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwxq", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwxq"]}}, "response": []}, {"name": "按考场号查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"kch\": \"101\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwxq", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwxq"]}}, "response": []}, {"name": "组合条件查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ksjhbh\": \"{{testKsjhbh}}\",\n  \"ccm\": \"001\",\n  \"yclx\": \"2\",\n  \"kch\": \"103\",\n  \"pageNum\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/manager/identity/cxtj/kkwxq", "host": ["{{baseUrl}}"], "path": ["manager", "identity", "cxtj", "kkwxq"]}}, "response": []}]}]}