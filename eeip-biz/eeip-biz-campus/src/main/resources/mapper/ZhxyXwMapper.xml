<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.campus.mapper.ZhxyXwMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.campus.model.domain.ZhxyXw">
        <id column="bh" jdbcType="BIGINT" property="bh" />
        <result column="bt" jdbcType="VARCHAR" property="bt" />
        <result column="fmt" jdbcType="VARCHAR" property="fmt" />
        <result column="nrlx" jdbcType="VARCHAR" property="nrlx" />
        <result column="nrjj" jdbcType="VARCHAR" property="nrjj" />
        <result column="nrxq" jdbcType="VARCHAR" property="nrxq" />
        <result column="fbfwlx" jdbcType="VARCHAR" property="fbfwlx" />
        <result column="zsms" jdbcType="TINYINT" property="zsms" />
        <result column="yxjsrq" jdbcType="DATE" property="yxjsrq" />
        <result column="fbsj" jdbcType="TIMESTAMP" property="fbsj" />
        <result column="fbr" jdbcType="VARCHAR" property="fbr" />
        <result column="fbrmc" jdbcType="VARCHAR" property="fbrmc" />
        <result column="zt" jdbcType="TINYINT" property="zt" />
        <result column="cjsj" jdbcType="TIMESTAMP" property="cjsj" />
        <result column="gxsj" jdbcType="TIMESTAMP" property="gxsj" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        bh,
        bt,
        fmt,
        nrlx,
        nrjj,
        nrxq,
        fbfwlx,
        zsms,
        yxjsrq,
        fbsj,
        fbr,
        fbrmc,
        zt,
        cjsj,
        gxsj

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="bh != null and bh != ''">
            AND bh = #{bh,jdbcType=BIGINT}
        </if>
        <if test="bt != null and bt != ''">
            AND bt = #{bt,jdbcType=VARCHAR}
        </if>
        <if test="fmt != null and fmt != ''">
            AND fmt = #{fmt,jdbcType=VARCHAR}
        </if>
        <if test="nrlx != null and nrlx != ''">
            AND nrlx = #{nrlx,jdbcType=VARCHAR}
        </if>
        <if test="nrjj != null and nrjj != ''">
            AND nrjj = #{nrjj,jdbcType=VARCHAR}
        </if>
        <if test="nrxq != null and nrxq != ''">
            AND nrxq = #{nrxq,jdbcType=VARCHAR}
        </if>
        <if test="fbfwlx != null and fbfwlx != ''">
            AND fbfwlx = #{fbfwlx,jdbcType=VARCHAR}
        </if>
        <if test="zsms != null and zsms != ''">
            AND zsms = #{zsms,jdbcType=TINYINT}
        </if>
        <if test="yxjsrq != null and yxjsrq != ''">
            AND yxjsrq = #{yxjsrq,jdbcType=DATE}
        </if>
        <if test="fbsj != null and fbsj != ''">
            AND fbsj = #{fbsj,jdbcType=TIMESTAMP}
        </if>
        <if test="fbr != null and fbr != ''">
            AND fbr = #{fbr,jdbcType=VARCHAR}
        </if>
        <if test="fbrmc != null and fbrmc != ''">
            AND fbrmc = #{fbrmc,jdbcType=VARCHAR}
        </if>
        <if test="zt != null and zt != ''">
            AND zt = #{zt,jdbcType=TINYINT}
        </if>
        <if test="cjsj != null and cjsj != ''">
            AND cjsj = #{cjsj,jdbcType=TIMESTAMP}
        </if>
        <if test="gxsj != null and gxsj != ''">
            AND gxsj = #{gxsj,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="bh != null ">
            bh = #{bh,jdbcType=BIGINT},
        </if>
        <if test="bt != null ">
            bt = #{bt,jdbcType=VARCHAR},
        </if>
        <if test="fmt != null ">
            fmt = #{fmt,jdbcType=VARCHAR},
        </if>
        <if test="nrlx != null ">
            nrlx = #{nrlx,jdbcType=VARCHAR},
        </if>
        <if test="nrjj != null ">
            nrjj = #{nrjj,jdbcType=VARCHAR},
        </if>
        <if test="nrxq != null ">
            nrxq = #{nrxq,jdbcType=VARCHAR},
        </if>
        <if test="fbfwlx != null ">
            fbfwlx = #{fbfwlx,jdbcType=VARCHAR},
        </if>
        <if test="zsms != null ">
            zsms = #{zsms,jdbcType=TINYINT},
        </if>
        <if test="yxjsrq != null ">
            yxjsrq = #{yxjsrq,jdbcType=DATE},
        </if>
        <if test="fbsj != null ">
            fbsj = #{fbsj,jdbcType=TIMESTAMP},
        </if>
        <if test="fbr != null ">
            fbr = #{fbr,jdbcType=VARCHAR},
        </if>
        <if test="fbrmc != null ">
            fbrmc = #{fbrmc,jdbcType=VARCHAR},
        </if>
        <if test="zt != null ">
            zt = #{zt,jdbcType=TINYINT},
        </if>
        <if test="cjsj != null ">
            cjsj = #{cjsj,jdbcType=TIMESTAMP},
        </if>
        <if test="gxsj != null ">
            gxsj = #{gxsj,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

    <insert id="insertZhxyXw" parameterType="com.xcwlkj.campus.model.domain.ZhxyXw" useGeneratedKeys="true" keyProperty="bh">
        insert into zhxy_xw
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bt != null and bt != ''">bt,</if>
            <if test="fmt != null and fmt != ''">fmt,</if>
            <if test="nrlx != null and nrlx != ''">nrlx,</if>
            <if test="nrjj != null and nrjj != ''">nrjj,</if>
            <if test="nrxq != null">nrxq,</if>
            <if test="fbfwlx != null and fbfwlx != ''">fbfwlx,</if>
            <if test="zsms != null">zsms,</if>
            <if test="yxjsrq != null">yxjsrq,</if>
            <if test="fbsj != null">fbsj,</if>
            <if test="fbr != null">fbr,</if>
            <if test="fbrmc != null">fbrmc,</if>
            <if test="zt != null">zt,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="gxsj != null">gxsj,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bt != null and bt != ''">#{bt},</if>
            <if test="fmt != null and fmt != ''">#{fmt},</if>
            <if test="nrlx != null and nrlx != ''">#{nrlx},</if>
            <if test="nrjj != null and nrjj != ''">#{nrjj},</if>
            <if test="nrxq != null">#{nrxq},</if>
            <if test="fbfwlx != null and fbfwlx != ''">#{fbfwlx},</if>
            <if test="zsms != null">#{zsms},</if>
            <if test="yxjsrq != null">#{yxjsrq},</if>
            <if test="fbsj != null">#{fbsj},</if>
            <if test="fbr != null">#{fbr},</if>
            <if test="fbrmc != null">#{fbrmc},</if>
            <if test="zt != null">#{zt},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="gxsj != null">#{gxsj},</if>
        </trim>
    </insert>

    <select id="remoteZhxyXwList" resultType="com.xcwlkj.campus.model.vo.news.RemoteXwItemVO">
        SELECT
            xw.bh bh,
            xw.bt bt,
            xw.fmt fmt,
            xw.nrlx nrlx,
            xw.nrjj nrjj,
            xw.zsms zsms,
            DATE_FORMAT( xw.yxjsrq, '%Y-%m-%d' ) yxjsrq,
            DATE_FORMAT( xw.fbsj, '%Y-%m-%d %H:%i:%s' ) fbsj,
            xw.fbrmc fbrmc,
            xw.zt zt
        FROM
            zhxy_xw xw
        <where>
            xw.zt = 1
            <if test="bt != null and bt != ''">
                and xw.bt like concat('%',#{bt},'%')
            </if>
            <if test="fbkssj != null and fbkssj != ''">
                and xw.fbsj &gt;= #{fbkssj}
            </if>
            <if test="fbjssj != null and fbjssj != ''">
                and xw.fbsj &lt;= #{fbjssj}
            </if>
            <if test="zt != null and zt != ''">
                and xw.zt = #{zt}
            </if>
            <if test="xwbhList != null and xwbhList.size()>0">
                and xw.bh in
                <foreach collection="xwbhList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by xw.fbsj desc
    </select>

    <select id="remoteZhxyXwDetails" resultType="com.xcwlkj.campus.model.vo.news.RemoteZhxyXwDetailsVO">
        SELECT
            xw.bh bh,
            xw.bt bt,
            xw.fmt fmt,
            xw.nrlx nrlx,
            xw.nrjj nrjj,
            xw.nrxq nrxq,
            xw.fbfwlx fbfwlx,
            xw.zsms zsms,
            DATE_FORMAT( xw.yxjsrq, '%Y-%m-%d' ) yxjsrq,
            DATE_FORMAT( xw.fbsj, '%Y-%m-%d %H:%i:%s' ) fbsj,
            xw.fbr fbr,
            xw.fbrmc fbrmc,
            xw.zt zt,
            DATE_FORMAT( xw.cjsj, '%Y-%m-%d %H:%i:%s' ) cjsj,
            DATE_FORMAT( xw.gxsj, '%Y-%m-%d %H:%i:%s' ) gxsj
        FROM
            zhxy_xw xw
        <where>
            <if test="bh != null and bh != ''">
                and xw.bh = #{bh}
            </if>
        </where>
    </select>
</mapper>
