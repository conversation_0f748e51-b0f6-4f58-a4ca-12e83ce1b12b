#spring:
#  cloud:
#    config:
#      fail-fast: true
#      discovery:
#        service-id: config-service
#        enabled: true
#      retry: 
#        max-attempts: 6 #最大重试次数
#      request-read-timeout: 6000 #读取配置超时时间
#      label: master
#      profile: ${spring.profiles.active}
#      username: admin
#      password: admin

eureka:
  client:
    serviceUrl:
      defaultZone: *******************************/eureka/
    registry-fetch-interval-seconds: 5 #拉取服务列表间隔时间
  instance:
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true #优先使用IP地址方式进行注册服务
    lease-expiration-duration-in-seconds: 15 #指定时间内没有数据上报可能会被清理掉
    lease-renewal-interval-in-seconds: 5 #服务状态上报间隔