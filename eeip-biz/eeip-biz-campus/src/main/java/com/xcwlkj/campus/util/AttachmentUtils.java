package com.xcwlkj.campus.util;

import com.xcwlkj.pubc.model.dto.attachment.GetAttachUrlDTO;
import com.xcwlkj.pubc.model.vo.attachment.GetAttachUrlVO;
import com.xcwlkj.pubc.service.AttachmentSysFeignApi;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 附件工具
 */
@Component("CampusAttachmentUtils")
public class AttachmentUtils {

    @Resource
    private AttachmentSysFeignApi attachmentSysFeignApi;

    public String getUrl(String id) {
        GetAttachUrlDTO getAttachUrlDto = new GetAttachUrlDTO();
        List<String> fileList = new ArrayList<String>();
        fileList.add(id);
        getAttachUrlDto.setFileIds(fileList);

        GetAttachUrlVO getAttachUrlVO = attachmentSysFeignApi.getAttachUrl(getAttachUrlDto)
                .getResult();

        List<String> urls = new ArrayList<String>();
        if (getAttachUrlVO != null) {
            urls = getAttachUrlVO.getUrls();
        }
        if (urls != null && !urls.isEmpty()) {
            return urls.get(0);
        }

        return null;
    }
}
