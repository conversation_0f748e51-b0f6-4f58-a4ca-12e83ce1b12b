/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.service;

import com.xcwlkj.campus.model.dto.advert.DeleteZhxyXucsDTO;
import com.xcwlkj.campus.model.dto.advert.InsertZhxyXucDTO;
import com.xcwlkj.campus.model.dto.advert.PublishZhxyXucDTO;
import com.xcwlkj.campus.model.dto.advert.RemoteZhxyXucDetailsDTO;
import com.xcwlkj.campus.model.dto.advert.RemoteZhxyXucListDTO;
import com.xcwlkj.campus.model.dto.advert.SelectZhxyXucByBhDTO;
import com.xcwlkj.campus.model.dto.advert.SelectZhxyXucListDTO;
import com.xcwlkj.campus.model.dto.advert.UnPublishZhxyXucDTO;
import com.xcwlkj.campus.model.dto.advert.UpdateZhxyXucDTO;
import com.xcwlkj.campus.model.vo.advert.RemoteZhxyXucDetailsVO;
import com.xcwlkj.campus.model.vo.advert.RemoteZhxyXucListVO;
import com.xcwlkj.campus.model.vo.advert.SelectZhxyXucByBhVO;
import com.xcwlkj.campus.model.vo.advert.SelectZhxyXucListVO;
import org.springframework.stereotype.Service;



/**
 * 校园宣传表服务
 * <AUTHOR>
 * @version $Id: ZhxyXucService.java, v 0.1 2023年07月17日 14时57分 xcwlkj.com Exp $
 */
@Service
public interface ZhxyXucService  {


    SelectZhxyXucListVO selectZhxyXucList(SelectZhxyXucListDTO dto);

    void insertZhxyXuc(InsertZhxyXucDTO dto);

    void publishZhxyXuc(PublishZhxyXucDTO dto);

    void unPublishZhxyXuc(UnPublishZhxyXucDTO dto);

    void updateZhxyXuc(UpdateZhxyXucDTO dto);

    void deleteZhxyXucs(DeleteZhxyXucsDTO dto);

    SelectZhxyXucByBhVO selectZhxyXucByBh(SelectZhxyXucByBhDTO dto);

    /**
     * 校园宣传列表-外部服务
     *
     * @param dto
     * @return 结果
     */
    RemoteZhxyXucListVO remoteZhxyXucList(RemoteZhxyXucListDTO dto);

    /**
     * 校园宣传详情-外部服务
     *
     * @param dto
     * @return 结果
     */
    RemoteZhxyXucDetailsVO remoteZhxyXucDetails(RemoteZhxyXucDetailsDTO dto);

}