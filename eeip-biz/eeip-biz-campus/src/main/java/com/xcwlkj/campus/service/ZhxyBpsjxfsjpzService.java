/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.service;

import com.xcwlkj.campus.model.dto.datadistribute.CreateTaskConfigDTO;
import com.xcwlkj.campus.model.dto.datadistribute.DeleteTaskConfigDTO;
import com.xcwlkj.campus.model.dto.datadistribute.ModifyTaskConfigDTO;
import com.xcwlkj.campus.model.dto.datadistribute.TaskConfigListDTO;
import com.xcwlkj.campus.model.vo.datadistribute.TaskConfigListVO;
import org.springframework.stereotype.Service;



/**
 * 班牌数据下发配置表服务
 * <AUTHOR>
 * @version $Id: ZhxyBpsjxfsjpzService.java, v 0.1 2023年07月20日 16时24分 xcwlkj.com Exp $
 */
@Service
public interface ZhxyBpsjxfsjpzService  {

	
	/**
	 * 下发配置创建
	 * @param dto
	 * @return
	 */
	void createTaskConfig(CreateTaskConfigDTO dto);
	/**
	 * 下发配置修改
	 * @param dto
	 * @return
	 */
	void modifyTaskConfig(ModifyTaskConfigDTO dto);
	/**
	 * 下发配置列表
	 * @param dto
	 * @return
	 */
	TaskConfigListVO taskConfigList(TaskConfigListDTO dto);
	/**
	 * 下发配置删除
	 * @param dto
	 * @return
	 */
	void deleteTaskConfig(DeleteTaskConfigDTO dto);


	void autoCreateDistributeTask();
}