/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.campus.model.domain.ZhxyBprw;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;



/**
 * 班牌任务表数据库操作
 * <AUTHOR>
 * @version $Id: InitZhxyBprwMapper.java, v 0.1 2023年07月19日 14时14分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface ZhxyBprwMapper extends MyMapper<ZhxyBprw> {

    /**
     * 新增班牌任务
     *
     * @param zhxyBprw 班牌任务
     * @return 结果
     */
    public int insertZhxyBprw(ZhxyBprw zhxyBprw);
}
