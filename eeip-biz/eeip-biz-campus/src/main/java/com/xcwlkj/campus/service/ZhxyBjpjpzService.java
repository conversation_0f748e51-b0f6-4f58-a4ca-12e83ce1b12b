/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.service;

import com.xcwlkj.campus.model.domain.ZhxyBjpjpz;

import com.xcwlkj.campus.model.dto.classevaluation.BindDTO;
import com.xcwlkj.campus.model.dto.classevaluation.BindXqDTO;
import com.xcwlkj.campus.model.dto.classevaluation.GetClassMbDTO;
import com.xcwlkj.campus.model.vo.classevaluation.BindXqVO;
import com.xcwlkj.campus.model.vo.classevaluation.GetClassMbVO;
import org.springframework.stereotype.Service;



/**
 * 班级评价配置表服务
 * <AUTHOR>
 * @version $Id: ZhxyBjpjpzService.java, v 0.1 2025年05月20日 13时27分 xcwlkj.com Exp $
 */
@Service
public interface ZhxyBjpjpzService extends BaseService<ZhxyBjpjpz> {


    void bind(BindDTO dto);

    /**
     * 班级评价-班级绑定详情
     * @param dto 查询条件
     * @return 绑定详情
     */
    BindXqVO bindXq(BindXqDTO dto);

    GetClassMbVO getClassMb(GetClassMbDTO dto);
}