package com.xcwlkj.campus.model.enums;

/**
 * 发布业务类型
 * 
 * <AUTHOR>
 */
public enum PublishBizType
{
    NEWS("news", "新闻"),
    CLASS_NEWS("class_news", "班级新闻"),
    NOTICE("notice", "公告"),
    CLASS_NOTICE("class_notice", "班级公告"),
    EMERGENCY_NOTICE("emergencyNotice", "紧急通知"),
    CLASS_EMERGENCY_NOTICE("class_emergencyNotice", "班级紧急通知"),
    COUNTDOWN("countdown", "倒计时"),
    CLASS_COUNTDOWN("class_countdown", "班级倒计时"),
    PHOTO("photo","相册"),
    CLASS_PHOTO("class_photo","班级相册"),
    VIDEO("video","视频"),
    CLASS_VIDEO("class_video","班级视频"),
    ARRANGE("examArrangement","考试安排"),
    CLASS_ARRANGE("class_examArrangement","班级考试安排"),
//    HONORS("honors", "班级荣誉"),
    CLASS_HONORS("class_honors", "班级荣誉"),
    CLASS_STUDENTHONORS("class_studentHonors", "学生荣誉"),
    EVALUATION("evaluation", "班级评价"),
    CLASS_DUTY("class_duty", "班级值日"),
    APP("app", "应用"),
    LAYOUT("layout", "布局"),
    PUBLICITY("publicity", "宣传"),
    CLASS_PUBLICITY("class_publicity", "班级宣传");

    private final String code;
    private final String info;

    PublishBizType(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
