/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.biz.core.model.InfoPubBodyParams;
import com.xcwlkj.biz.core.model.enums.BusinessType;
import com.xcwlkj.biz.core.model.enums.InfoPubActionType;
import com.xcwlkj.biz.core.service.BoardOperateService;
import com.xcwlkj.biz.core.util.SbxxUtils;
import com.xcwlkj.campus.mapper.ZhxyFbfwMapper;
import com.xcwlkj.campus.mapper.ZhxyKskcapMapper;
import com.xcwlkj.campus.model.domain.ZhxyFbfw;
import com.xcwlkj.campus.model.domain.ZhxyKskcap;
import com.xcwlkj.campus.model.dos.UnPublishVO;
import com.xcwlkj.campus.model.dto.arrange.*;
import com.xcwlkj.campus.model.enums.PublishBizType;
import com.xcwlkj.campus.model.enums.PublishRangeType;
import com.xcwlkj.campus.model.enums.PublishStatus;
import com.xcwlkj.campus.model.vo.arrange.*;
import com.xcwlkj.campus.service.ZhxyFbfwService;
import com.xcwlkj.campus.service.ZhxyKskcapService;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.coursearrangement.model.dto.examcourseplan.SyncPlanListDTO;
import com.xcwlkj.coursearrangement.model.vo.examcourseplan.KskcjhItemVO;
import com.xcwlkj.coursearrangement.model.vo.examcourseplan.SyncPlanListVO;
import com.xcwlkj.coursearrangement.service.ExamcourseplanFeignApi;
import com.xcwlkj.sturegister.model.dto.bj.BjTreeDTO;
import com.xcwlkj.sturegister.model.vo.bj.BjTreeVO;
import com.xcwlkj.sturegister.service.BjFeignApi;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.common.Convert;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.utils.RequestUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 考试课程安排服务
 * <AUTHOR>
 * @version $Id: ZhxyKskcapServiceImpl.java, v 0.1 2023年07月24日 15时54分 xcwlkj.com Exp $
 */
@Service("zhxyKskcapService")
public class ZhxyKskcapServiceImpl  implements ZhxyKskcapService  {

    @Resource
    private ZhxyKskcapMapper modelMapper;
    @Resource
    private ExamcourseplanFeignApi examcourseplanFeignApi;
    @Resource
    private ZhxyFbfwService zhxyFbfwService;
    @Resource
    private SbxxUtils sbxxUtils;
    @Resource(name = "campusBoardOperateService")
    private BoardOperateService boardOperateService;
    @Resource
    private ZhxyFbfwMapper zhxyFbfwMapper;
    @Resource
    private BjFeignApi bjFeignApi;

    @Transactional
    @Override
    public void insertZhxyKs(InsertZhxyKsDTO dto) {

        // 若为班级发布，添加发布权限
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            String roleId = com.xcwlkj.utils.RequestUtil.getLoginUser().getCurrRoleId();
            // 如果用户是教师
            if ("001".equals(roleId)) {
                // 判断是否为班主任
                Wrapper<BjTreeVO> bjTreeVOWrapper = bjFeignApi.bjTree(new BjTreeDTO());
                if (bjTreeVOWrapper.getResult() == null){
                    throw new BusinessException("当前登录用户为普通教师，不允许进行班级发布");
                }
            }
        }

        SyncPlanListDTO syncPlanListDTO = new SyncPlanListDTO();
        syncPlanListDTO.setKsjhid(dto.getKsjhid());
        syncPlanListDTO.setKchList(dto.getKchList());

        // 判断考试计划是否已存在
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhid",dto.getKsjhid());
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(zhxyKskcapList)){
            throw new BusinessException("当前考试计划已创建考试课程安排");
        }

        Wrapper<SyncPlanListVO> syncPlanListVOWrapper = examcourseplanFeignApi.syncPlanList(syncPlanListDTO);
        SyncPlanListVO syncPlanListVO = syncPlanListVOWrapper.getResult();
        if (syncPlanListVO.getKskcjhList().isEmpty() ){
            throw new BusinessException("当前考试计划没有对应的考试课程计划");
        }
        for (KskcjhItemVO vo :syncPlanListVO.getKskcjhList() ){
            ZhxyKskcap zhxyKskcap = new ZhxyKskcap();
            BeanUtils.copyProperties(vo,zhxyKskcap);
            zhxyKskcap.setFbfwlx(dto.getFbfwlx());
            zhxyKskcap.setZt(PublishStatus.UNPUBLISHED.getValue());
            zhxyKskcap.setCjsj(new Date());
            zhxyKskcap.setXgsj(new Date());
            modelMapper.insertZhxyKskcap(zhxyKskcap);
            // 添加发布范围
            insertZhxyFbfw(dto.getBjjshList(), dto.getBjjsmcList(), dto.getBjjsbjList(),zhxyKskcap);
        }
    }

    @Transactional
    @Override
    public void publishZhxyKs(PublishZhxyKsDTO dto) {
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("ksjhid",dto.getKsjhidList());
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);

        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }

        for (ZhxyKskcap item : zhxyKskcapList) {
            ZhxyKskcap zhxyKskcap = modelMapper.selectByPrimaryKey(item.getBh());
            zhxyKskcap.setZt(PublishStatus.PUBLISHED.getValue());
            XcSsoUser loginUser = RequestUtil.getLoginUser();
            zhxyKskcap.setFbr(loginUser.getUserName());
            zhxyKskcap.setFbrmc(loginUser.getRealName());
            zhxyKskcap.setFbsj(new Date());
            modelMapper.updateByPrimaryKey(zhxyKskcap);

            //获取教室号列表
            List<String> jshList = zhxyFbfwService.selectJshList(bizType, (long)item.getBh());
            // 组织入参
            InfoPubBodyParams pubBodyParams = InfoPubBodyParams.of(InfoPubActionType.pub.getValue(),
                    bizType.getCode(), IdGenerateUtil.generateId(), (JSONObject)JSONObject.toJSON(zhxyKskcap));
            boardOperateService.infoPublish(sbxxUtils.getDevicesSupplier(jshList,"4869"), PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())? BusinessType.class_examArrangement.getType() : BusinessType.examArrangement.getType(), item.getBh().toString(), pubBodyParams);
        }
    }

    @Transactional
    @Override
    public void unPublishZhxyKs(UnPublishZhxyKsDTO dto) {
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhid",dto.getKsjhid());
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);

        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }

        for (ZhxyKskcap item : zhxyKskcapList){
            ZhxyKskcap zhxyKskcap = modelMapper.selectByPrimaryKey(item.getBh());
            zhxyKskcap.setZt(PublishStatus.UNPUBLISHED.getValue());
            zhxyKskcap.setFbr(null);
            zhxyKskcap.setFbrmc(null);
            zhxyKskcap.setFbsj(null);
            zhxyKskcap.setXgsj(new Date());
            modelMapper.updateByPrimaryKey(zhxyKskcap);

            UnPublishVO unPublishVO = new UnPublishVO();
            BeanUtils.copyProperties(zhxyKskcap,unPublishVO);

            //获取教室号列表
            List<String> jshList = zhxyFbfwService.selectJshList(bizType, (long)item.getBh());
            // 组织入参
            InfoPubBodyParams pubBodyParams = InfoPubBodyParams.of(InfoPubActionType.unpub.getValue(),
                    bizType.getCode(), IdGenerateUtil.generateId(), (JSONObject)JSONObject.toJSON(unPublishVO));
            boardOperateService.infoPublish(sbxxUtils.getDevicesSupplier(jshList,"4869"), PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())? BusinessType.class_examArrangement.getType() : BusinessType.examArrangement.getType(), item.getBh().toString(), pubBodyParams);
        }
    }

    @Transactional
    @Override
    public void unPublishAllZhxyKs(UnPublishAllZhxyKsDTO dto) {
        // 查询所有考试安排
        SelectZhxyKsListDTO selectZhxyKsListDTO = new SelectZhxyKsListDTO();
        selectZhxyKsListDTO.setZt(1);
        selectZhxyKsListDTO.setFbfwlx(dto.getFbfwlx());
        selectZhxyKsListDTO.setPageNum(1);
        selectZhxyKsListDTO.setPageSize(99999);
        SelectZhxyKsListVO selectZhxyKsListVO = selectZhxyKsList(selectZhxyKsListDTO);
        if (selectZhxyKsListVO != null){
            List<KsItemVO> ksList = selectZhxyKsListVO.getKsList();
            // 全部撤销发布
            for (KsItemVO ksItemVO : ksList){
                UnPublishZhxyKsDTO unPublishZhxyKsDTO = new UnPublishZhxyKsDTO();
                unPublishZhxyKsDTO.setKsjhid(ksItemVO.getKsjhid());
                unPublishZhxyKsDTO.setFbfwlx(dto.getFbfwlx());
                unPublishZhxyKs(unPublishZhxyKsDTO);
            }
        }
    }

    @Transactional
    @Override
    public void batchDeleteZhxyKs(BatchDeleteZhxyKsDTO dto) {
        Example example = new Example(ZhxyKskcap.class);
        example.createCriteria()
                .andIn("ksjhid",dto.getKsjhbhList())
                .andEqualTo("fbfwlx",dto.getFbfwlx());

        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }

        List<Integer> bhs = new ArrayList<>();
        List<ZhxyKskcap> zhxyKskcaps = modelMapper.selectByExample(example);
        for (ZhxyKskcap ksap : zhxyKskcaps){
            bhs.add(ksap.getBh());
        }

        zhxyFbfwService.deleteZhxyFbfwByYwbhs(bizType, Convert.toStrArray(bhs));
        modelMapper.deleteByExample(example);
    }

    @Transactional
    @Override
    public void allDeleteZhxyKs(AllDeleteZhxyKsDTO dto) {
        // 查询所有考试安排
        SelectZhxyKsListDTO selectZhxyKsListDTO = new SelectZhxyKsListDTO();
        selectZhxyKsListDTO.setPageNum(1);
        selectZhxyKsListDTO.setPageSize(99999);
        selectZhxyKsListDTO.setFbfwlx(dto.getFbfwlx());
        SelectZhxyKsListVO selectZhxyKsListVO = selectZhxyKsList(selectZhxyKsListDTO);
        if (selectZhxyKsListVO != null){
            List<KsItemVO> ksList = selectZhxyKsListVO.getKsList();
            // 全部删除
            List<String> ksjhbhList = new ArrayList<>();
            for (KsItemVO ksItemVO : ksList){
                ksjhbhList.add(ksItemVO.getKsjhid());
            }
            BatchDeleteZhxyKsDTO batchDeleteZhxyKsDTO = new BatchDeleteZhxyKsDTO();
            batchDeleteZhxyKsDTO.setKsjhbhList(ksjhbhList);
            batchDeleteZhxyKsDTO.setFbfwlx(dto.getFbfwlx());
            batchDeleteZhxyKs(batchDeleteZhxyKsDTO);
        }
    }

    @Override
    public ZhxyKsDetailVO zhxyKsDetail(ZhxyKsDetailDTO dto) {
        ZhxyKsDetailVO result = new ZhxyKsDetailVO();
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("cjsj desc");

        if (StringUtils.isNotBlank(dto.getKsjhid())) {
            criteria.andEqualTo("ksjhid",dto.getKsjhid());
        }
        if (StringUtils.isNotBlank(dto.getFbfwlx())) {
            criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        }
        if (StringUtils.isNotBlank(dto.getKcmc())) {
            criteria.andLike("kcmc","%" + dto.getKcmc() + "%");
        }

        Page<ZhxyKskcap> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);

        List<KcapItemVO> itemVOS = zhxyKskcapList.stream().map(zhxyKskcap -> {
            KcapItemVO item = new KcapItemVO();
            BeanUtils.copyProperties(zhxyKskcap, item);
            item.setKskssj(DateUtil.format(zhxyKskcap.getKskssj(), DateUtil.DEFAULT_TIME));
            item.setKsjssj(DateUtil.format(zhxyKskcap.getKsjssj(), DateUtil.DEFAULT_TIME));
            item.setKsrq(DateUtil.format(zhxyKskcap.getKsrq(), DateUtil.DEFAULT_DATE));
            return item;
        }).collect(Collectors.toList());
        result.setKcapList(itemVOS);
        result.setTotal((int)page.getTotal());
        return result;
    }

    @Override
    public SelectZhxyKsListVO selectZhxyKsList(SelectZhxyKsListDTO dto) {

        SelectZhxyKsListVO result = new SelectZhxyKsListVO();
//        Example example = new Example(ZhxyKskcap.class);
//        Example.Criteria criteria = example.createCriteria();
//        example.setOrderByClause("cjsj desc");

        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }

        //新加权限班级控制
        List<Integer> bhList = zhxyFbfwService.getBhByUserInfo(bizType.getCode());

//        if (bhList.isEmpty()){
//            return result;
//        }
//        criteria.andIn("bh",bhList);
//        if (StringUtils.isNotBlank(dto.getKsmc())) {
//            criteria.andLike("ksmc","%" + dto.getKsmc() + "%");
//        }
//        if (StringUtils.isNotBlank(dto.getKcmc())) {
//            criteria.andLike("kcmc","%" + dto.getKcmc() + "%");
//        }
//        if (StringUtils.isNotBlank(dto.getSt())) {
//            criteria.andGreaterThanOrEqualTo("fbsj",dto.getSt());
//        }
//        if (StringUtils.isNotBlank(dto.getEt())) {
//            criteria.andLessThanOrEqualTo("fbsj",dto.getEt());
//        }
//        if (dto.getZt() != null) {
//            criteria.andEqualTo("zt",dto.getZt());
//        }
//
//        Page<ZhxyKskcap> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
//        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);
//
//        List<KsItemVO> collect = zhxyKskcapList.stream().map(zhxyKskcap -> {
//            KsItemVO item = new KsItemVO();
//            BeanUtils.copyProperties(zhxyKskcap, item);
//            item.setFbsj(DateUtil.format(zhxyKskcap.getFbsj(), DateUtil.DEFAULT_DATE_TIME));
//            return item;
//        }).collect(Collectors.toList());

        Page<ZhxyKskcap> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KsItemVO> zhxyKskcapList = modelMapper.zhxyKskcapList(bhList,dto);

        result.setTotal((int)page.getTotal());
        result.setKsList(zhxyKskcapList);
        return result;

    }

    @Override
    public SelectAppKsListVO selectAppKsList(SelectAppKsListDTO dto) {
        SelectAppKsListVO result = new SelectAppKsListVO();

        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }

        List<Long> bhList = zhxyFbfwMapper.selectYwbhList(bizType.getCode(), dto.getBjjsh());
        if (bhList == null || bhList.isEmpty()){
            return result;
        }

        //查询考试安排列表
        Example ksap_exa = new Example(ZhxyKskcap.class);
        ksap_exa.createCriteria().andEqualTo("zt", PublishStatus.PUBLISHED.getValue())
                .andIn("bh", bhList);

        Page<ZhxyKskcap> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(ksap_exa);

        List<AppKsapItemVO> collect = zhxyKskcapList.stream().map(zhxyKskcap -> {
            AppKsapItemVO item = new AppKsapItemVO();
            BeanUtils.copyProperties(zhxyKskcap, item);
            item.setKskssj(DateUtil.format(zhxyKskcap.getKskssj(), DateUtil.DEFAULT_DATE_TIME));
            item.setKsjssj(DateUtil.format(zhxyKskcap.getKsjssj(), DateUtil.DEFAULT_DATE_TIME));
            Date ksrq = zhxyKskcap.getKsrq();
            item.setKsrq(DateUtil.format(ksrq, DateUtil.DEFAULT_DATE));
            // 创建Calendar对象并设置日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(ksrq);
            // 获取星期几，1代表星期日，2代表星期一，依此类推
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            item.setXq(dayOfWeek == 1 ? 7 : (dayOfWeek - 1));
            return item;
        }).sorted(Comparator.comparing(AppKsapItemVO::getKskssj)).collect(Collectors.toList());


        result.setTotal((int)page.getTotal());
        result.setAppKsapList(collect);
        return result;
    }

    @Transactional
    @Override
    public void updateZhxyKs(UpdateZhxyKsDTO dto) {
        // 查询考试安排的发布状态，若发布状态为已发布则不允许修改
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("cjsj desc");
        criteria.andEqualTo("ksjhid",dto.getKsjhid());
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(zhxyKskcapList)){
            ZhxyKskcap zhxyKskcap = zhxyKskcapList.get(0);
            if (zhxyKskcap == null){
                throw new BusinessException("无考试课程安排");
            }
            if (PublishStatus.PUBLISHED.getValue().equals(zhxyKskcap.getZt())){
                throw new BusinessException("已发布的校园考试安排不允许修改发布范围");
            }
            // 根据考试计划id,查询校园考试安排编号列表
            List<Integer> bhs = new ArrayList<>();
            for (ZhxyKskcap item : zhxyKskcapList){
                bhs.add(item.getBh());
            }

            PublishBizType bizType = PublishBizType.ARRANGE;
            if (PublishRangeType.CLASS.getCode().equals(zhxyKskcap.getFbfwlx())){
                bizType = PublishBizType.CLASS_ARRANGE;
            }

            // 批量删除原发布范围
            batchDeleteZhxyFbfw(Convert.toStrArray(bhs),bizType);
            // 新增校园发布范围
            for (ZhxyKskcap item : zhxyKskcapList){
                insertZhxyFbfw(dto.getBjjshList(), dto.getBjjsmcList(), dto.getBjjsbjList(),item);
            }
        }
    }

    @Override
    public OptionalKskcListVO optionalKskcList(OptionalKskcListDTO dto) {
        OptionalKskcListVO result = new OptionalKskcListVO();

        // 查询已选择的课程
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhid",dto.getKsjhid());
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);

        List<String> chosedList = new ArrayList<>();
        for (ZhxyKskcap ksap : zhxyKskcapList){
            chosedList.add(ksap.getKch());
        }

        SyncPlanListDTO syncPlanListDTO = new SyncPlanListDTO();
        syncPlanListDTO.setKsjhid(dto.getKsjhid());
        syncPlanListDTO.setChosedList(chosedList);
        Wrapper<SyncPlanListVO> syncPlanListVOWrapper = examcourseplanFeignApi.syncPlanList(syncPlanListDTO);
        SyncPlanListVO syncPlanListVO = syncPlanListVOWrapper.getResult();
        if (syncPlanListVO.getKskcjhList().isEmpty() ){
            return null;
        }
        List<OptionalKskcItemVO> optionalKskcList = new ArrayList<>();
        for (KskcjhItemVO vo :syncPlanListVO.getKskcjhList() ){
            OptionalKskcItemVO itemVO = new OptionalKskcItemVO();
            itemVO.setKch(vo.getKch());
            itemVO.setKcmc(vo.getKcmc());
            optionalKskcList.add(itemVO);
        }
        result.setOptionalKskcList(optionalKskcList);
        return result;
    }

    @Override
    public ZhxyKsEditDetailVO zhxyKsEditDetail(ZhxyKsEditDetailDTO dto) {
        ZhxyKsEditDetailVO result = new ZhxyKsEditDetailVO();

        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("cjsj desc");
        criteria.andEqualTo("ksjhid",dto.getKsjhid());
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);

        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }
        if (CollectionUtils.isNotEmpty(zhxyKskcapList)){
            ZhxyKskcap zhxyKskcap = zhxyKskcapList.get(0);
            if (zhxyKskcap == null){
                throw new BusinessException("无考试课程安排");
            }
            result.setKsjhid(dto.getKsjhid());
            result.setKsjhmc(zhxyKskcap.getKsmc());

            List<ZhxyFbfw> zhxyFbfwList = zhxyFbfwMapper.selectFbfwList(bizType.getCode(), zhxyKskcapList);
            List<String> bjjshList = new ArrayList<>();
            List<String> bjjsmcList = new ArrayList<>();
            List<String> bjjsbjList = new ArrayList<>();
            for (ZhxyFbfw fbfw : zhxyFbfwList){
                bjjshList.add(fbfw.getBjjsh());
                bjjsmcList.add(fbfw.getMc());
                bjjsbjList.add("bjjs");
            }
            result.setBjjshList(bjjshList);
            result.setBjjsmcList(bjjsmcList);
            result.setBjjsbjList(bjjsbjList);
        }
        return result;
    }

    @Transactional
    @Override
    public void insertKskc(InsertKskcDTO dto) {
        SyncPlanListDTO syncPlanListDTO = new SyncPlanListDTO();
        syncPlanListDTO.setKsjhid(dto.getKsjhid());
        syncPlanListDTO.setKchList(dto.getKchList());

        Wrapper<SyncPlanListVO> syncPlanListVOWrapper = examcourseplanFeignApi.syncPlanList(syncPlanListDTO);
        SyncPlanListVO syncPlanListVO = syncPlanListVOWrapper.getResult();
        if (syncPlanListVO.getKskcjhList().isEmpty() ){
            throw new BusinessException("当前考试计划没有对应的考试课程计划");
        }

        //查询当前考试课程安排的发布范围
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("cjsj desc");
        criteria.andEqualTo("ksjhid",dto.getKsjhid());
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);

        String fbfwlx = dto.getFbfwlx();
        List<String> bjjshList = new ArrayList<>();
        List<String> bjjsmcList = new ArrayList<>();
        List<String> bjjsbjList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(zhxyKskcapList)){
            ZhxyKskcap zhxyKskcap = zhxyKskcapList.get(0);
            if (zhxyKskcap == null){
                throw new BusinessException("无考试课程安排，无法获取考试课程安排发布范围");
            }

            PublishBizType bizType = PublishBizType.ARRANGE;
            if (PublishRangeType.CLASS.getCode().equals(zhxyKskcap.getFbfwlx())){
                bizType = PublishBizType.CLASS_ARRANGE;
            }

            List<ZhxyFbfw> zhxyFbfwList = zhxyFbfwMapper.selectFbfwList(bizType.getCode(), zhxyKskcapList);
            for (ZhxyFbfw fbfw : zhxyFbfwList){
                bjjshList.add(fbfw.getBjjsh());
                bjjsmcList.add(fbfw.getMc());
                bjjsbjList.add("bjjs");
            }
        }

        for (KskcjhItemVO vo :syncPlanListVO.getKskcjhList() ){
            ZhxyKskcap zhxyKskcap = new ZhxyKskcap();
            BeanUtils.copyProperties(vo,zhxyKskcap);
            zhxyKskcap.setFbfwlx(fbfwlx);
            zhxyKskcap.setZt(PublishStatus.UNPUBLISHED.getValue());
            zhxyKskcap.setCjsj(new Date());
            zhxyKskcap.setXgsj(new Date());
            modelMapper.insertZhxyKskcap(zhxyKskcap);
            // 添加发布范围
            insertZhxyFbfw(bjjshList, bjjsmcList, bjjsbjList,zhxyKskcap);
        }
    }

    @Transactional
    @Override
    public void batchDeleteKskc(BatchDeleteKskcDTO dto) {
        //查询当前考试课程安排的发布范围
        Example example = new Example(ZhxyKskcap.class);
        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("cjsj desc");
        criteria.andIn("bh",dto.getBhList());
        List<ZhxyKskcap> zhxyKskcapList = modelMapper.selectByExample(example);
        // 批量删除课程
        modelMapper.deleteByExample(example);

        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }
        // 批量删除发布范围
        batchDeleteZhxyFbfw(Convert.toStrArray(dto.getBhList()),bizType);
    }

    /**
     * 新增校园发布范围信息
     *
     * @param zhxyKskcap 校园应用对象
     */
    public void insertZhxyFbfw(List<String> bjjshList, List<String> bjjsmcList, List<String> bjjsbjList, ZhxyKskcap zhxyKskcap)
    {
        PublishBizType bizType = PublishBizType.ARRANGE;
        if (PublishRangeType.CLASS.getCode().equals(zhxyKskcap.getFbfwlx())){
            bizType = PublishBizType.CLASS_ARRANGE;
        }
        zhxyFbfwService.batchZhxyFbfw(bjjshList, bjjsmcList, bjjsbjList, bizType, zhxyKskcap.getBh());
    }

    /**
     * 批量删除校园发布范围信息
     */
    public void batchDeleteZhxyFbfw(String[] ywbhs,PublishBizType bizType)
    {
        zhxyFbfwService.deleteZhxyFbfwByYwbhs(bizType, ywbhs);
    }

}