/**
 * xcwlkj.com Inc.
 * Copyright (c) 2025-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.campus.model.domain.ZhxyBjzr;
import com.xcwlkj.campus.model.domain.ZrDateDO;
import com.xcwlkj.campus.model.dto.classduty.DistributeDTO;
import com.xcwlkj.core.sso.user.XcSsoUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * 班级值日表数据库操作
 * <AUTHOR>
 * @version $Id: InitZhxyBjzrMapper.java, v 0.1 2025年06月19日 13时10分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface ZhxyBjzrMapper extends MyMapper<ZhxyBjzr> {

    /**
     * 班级值日下发
     */
    void publishByJshList(@Param("dto") DistributeDTO dto,
                          @Param("loginUser") XcSsoUser loginUser);

    /**
     * 获取今日值日记录
     */
    String todayDutyStuList(@Param("zrDateDO") ZrDateDO zrDateDO,
                            @Param("jsh") String jsh);
}
