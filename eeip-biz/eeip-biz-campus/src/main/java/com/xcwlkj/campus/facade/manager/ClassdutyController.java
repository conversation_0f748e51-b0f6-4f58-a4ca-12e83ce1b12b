/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.facade.manager;

import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.campus.model.dto.classduty.*;
import com.xcwlkj.campus.model.req.classduty.*;
import com.xcwlkj.campus.model.resp.classduty.*;
import com.xcwlkj.campus.model.vo.classduty.AddVO;
import com.xcwlkj.campus.model.vo.classduty.WeekTableVO;
import com.xcwlkj.campus.service.ZhxyBjzrService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * Classduty控制层
 * <AUTHOR>
 * @version $Id: ClassdutyController.java, v 0.1 2025年06月19日 13时16分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("ClassdutyManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class ClassdutyController extends BaseController {

    @Autowired
    private ZhxyBjzrService zhxyBjzrService;
	
   /**
    * 班级值日新增
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/campus/classDuty/add")
    public Wrapper<AddRespModel> add(@RequestBody AddReqModel reqModel) {
		log.info("收到请求开始：[班级值日新增][/manager/campus/classDuty/add]reqModel:"+reqModel.toString());
		AddDTO dto = new AddDTO();
        dto.setKkxnd(reqModel.getKkxnd());
        dto.setKkxqm(reqModel.getKkxqm());
        dto.setXq(reqModel.getXq());
        dto.setKszc(reqModel.getKszc());
        dto.setJszc(reqModel.getJszc());
        dto.setSfdsz(reqModel.getSfdsz());
        dto.setZrbbh(reqModel.getZrbbh());
        dto.setZrbmc(reqModel.getZrbmc());
        dto.setZrxsxm(reqModel.getZrxsxm());
        dto.setZrxmc(reqModel.getZrxmc());
        dto.setZrsxh(reqModel.getZrsxh());
        dto.setZrsxm(reqModel.getZrsxm());
        dto.setJsh(reqModel.getJsh());
        dto.setZrdd(reqModel.getZrdd());
        dto.setJsszxqh(reqModel.getJsszxqh());
        dto.setZrszzs(reqModel.getZrszzs());
        AddVO addVO = zhxyBjzrService.add(dto);
        AddRespModel respModel = new AddRespModel();
        respModel.setBhList(addVO.getBhList());
		log.info("处理请求结束：[班级值日新增][/manager/campus/classDuty/add]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 班级值日编辑
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/campus/classDuty/edit")
    public Wrapper<EditRespModel> edit(@RequestBody EditReqModel reqModel) {
		log.info("收到请求开始：[班级值日编辑][/manager/campus/classDuty/edit]reqModel:"+reqModel.toString());
		EditDTO dto = new EditDTO();
        dto.setKsxnd(reqModel.getKsxnd());
        dto.setKkxqm(reqModel.getKkxqm());
        dto.setXq(reqModel.getXq());
        dto.setKszc(reqModel.getKszc());
        dto.setJszc(reqModel.getJszc());
        dto.setSfdsz(reqModel.getSfdsz());
        dto.setZrbbh(reqModel.getZrbbh());
        dto.setZrbmc(reqModel.getZrbmc());
        dto.setZrxsxm(reqModel.getZrxsxm());
        dto.setZrxmc(reqModel.getZrxmc());
        dto.setZrsxh(reqModel.getZrsxh());
        dto.setZrsxm(reqModel.getZrsxm());
        dto.setJsh(reqModel.getJsh());
        dto.setZrdd(reqModel.getZrdd());
        dto.setJsszxqh(reqModel.getJsszxqh());
        dto.setZrszzs(reqModel.getZrszzs());
        dto.setBh(reqModel.getBh());
        zhxyBjzrService.edit(dto);
        EditRespModel respModel = new EditRespModel();

		log.info("处理请求结束：[班级值日编辑][/manager/campus/classDuty/edit]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 班级值日列表
    * @param reqModel
    * @return
    */
	@Permission("classDuty:weekTable")
    @PostMapping(value = "/manager/campus/classDuty/weekTable")
    public Wrapper<WeekTableRespModel> weekTable(@RequestBody WeekTableReqModel reqModel) {
		log.info("收到请求开始：[班级值日列表][/manager/campus/classDuty/weekTable]reqModel:"+reqModel.toString());
		WeekTableDTO dto = new WeekTableDTO();
        dto.setKkxnd(reqModel.getKkxnd());
        dto.setKkxqm(reqModel.getKkxqm());
        dto.setCxzc(reqModel.getCxzc());
        dto.setBah(reqModel.getBah());
        WeekTableVO result = zhxyBjzrService.weekTable(dto);
        WeekTableRespModel respModel = new WeekTableRespModel();
        respModel.setZzrbList(result.getZzrbList());
        respModel.setTotalRows(result.getTotalRows());
		log.info("处理请求结束：[班级值日列表][/manager/campus/classDuty/weekTable]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 班级值日批量删除
    * @param reqModel
    * @return
    */
	@Permission("classDuty:batchDelete")
    @PostMapping(value = "/manager/campus/classDuty/batchDelete")
    public Wrapper<BatchDeleteRespModel> batchDelete(@RequestBody BatchDeleteReqModel reqModel) {
		log.info("收到请求开始：[班级值日批量删除][/manager/campus/classDuty/batchDelete]reqModel:"+reqModel.toString());
		BatchDeleteDTO dto = new BatchDeleteDTO();
        dto.setBhList(reqModel.getBhList());
        zhxyBjzrService.batchDelete(dto);
        BatchDeleteRespModel respModel = new BatchDeleteRespModel();

		log.info("处理请求结束：[班级值日批量删除][/manager/campus/classDuty/batchDelete]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 班级值日下发
    * @param reqModel
    * @return
    */
	@Permission("classDuty:distribute")
    @PostMapping(value = "/manager/campus/classDuty/distribute")
    public Wrapper<DistributeRespModel> distribute(@RequestBody DistributeReqModel reqModel) {
		log.info("收到请求开始：[班级值日下发][/manager/campus/classDuty/distribute]reqModel:"+reqModel.toString());
		DistributeDTO dto = new DistributeDTO();
        dto.setKkxnd(reqModel.getKkxnd());
        dto.setKkxqm(reqModel.getKkxqm());
        dto.setJshList(reqModel.getJshList());
        zhxyBjzrService.distribute(dto);
        DistributeRespModel respModel = new DistributeRespModel();

		log.info("处理请求结束：[班级值日下发][/manager/campus/classDuty/distribute]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 班级值日导入
    * @param file
    * @return
    */
    @PostMapping(value = "/manager/campus/classDuty/importDuty")
    public Wrapper<Void> importDuty(MultipartFile file,String zrbbh,String zrbmc,String jsh,String jsmc) throws IOException, ParseException {
		log.info("收到请求开始：[班级值日导入][/manager/campus/classDuty/importDuty]");
        zhxyBjzrService.importDuty(file,zrbbh,zrbmc,jsh,jsmc);
		log.info("处理请求结束：[班级值日导入][/manager/campus/classDuty/importDuty]");
        return WrapMapper.ok();
    }

}