package com.xcwlkj.campus.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/**
 * 启用状态
 */
public enum EnableStatus
{
    DISABLED( 0,"未启用"),
    ENABLED(1, "启用"), ;

    private final Integer code;
    private final String desc;

    EnableStatus(Integer code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return desc;
    }

    public static EnableStatus get(Integer code){
        for (EnableStatus c : values()) {
            if (c.getCode().equals(code)){
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值：" + code);
    }
}
