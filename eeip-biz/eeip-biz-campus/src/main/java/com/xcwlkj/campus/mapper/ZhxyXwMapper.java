/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.campus.model.domain.ZhxyXw;
import com.xcwlkj.campus.model.dto.news.RemoteZhxyXwDetailsDTO;
import com.xcwlkj.campus.model.dto.news.RemoteZhxyXwListDTO;
import com.xcwlkj.campus.model.vo.news.RemoteZhxyXwDetailsVO;
import com.xcwlkj.campus.model.vo.news.RemoteXwItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 校园新闻表数据库操作
 * <AUTHOR>
 * @version $Id: InitZhxyXwMapper.java, v 0.1 2023年07月19日 14时28分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface ZhxyXwMapper extends MyMapper<ZhxyXw> {

    public int  insertZhxyXw(ZhxyXw zhxyXw);

    List<RemoteXwItemVO> remoteZhxyXwList(RemoteZhxyXwListDTO dto);

    RemoteZhxyXwDetailsVO remoteZhxyXwDetails(RemoteZhxyXwDetailsDTO dto);
}
