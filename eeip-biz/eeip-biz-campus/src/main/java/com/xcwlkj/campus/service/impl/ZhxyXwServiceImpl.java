/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.biz.core.model.InfoPubBodyParams;
import com.xcwlkj.biz.core.model.enums.BusinessType;
import com.xcwlkj.biz.core.model.enums.InfoPubActionType;
import com.xcwlkj.biz.core.service.BoardOperateService;
import com.xcwlkj.biz.core.util.SbxxUtils;
import com.xcwlkj.campus.mapper.ZhxyFbfwMapper;
import com.xcwlkj.campus.mapper.ZhxyXwMapper;
import com.xcwlkj.campus.model.domain.ZhxyXw;
import com.xcwlkj.campus.model.dos.UnPublishVO;
import com.xcwlkj.campus.model.dto.news.*;
import com.xcwlkj.campus.model.enums.PublishBizType;
import com.xcwlkj.campus.model.enums.PublishRangeType;
import com.xcwlkj.campus.model.enums.PublishStatus;
import com.xcwlkj.campus.model.vo.news.*;
import com.xcwlkj.campus.model.vo.notice.FbfwItemVO;
import com.xcwlkj.campus.service.ZhxyFbfwService;
import com.xcwlkj.campus.service.ZhxyXwService;
import com.xcwlkj.campus.util.GetFbfwLsit;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.sturegister.model.dto.bj.BjTreeDTO;
import com.xcwlkj.sturegister.model.vo.bj.BjTreeVO;
import com.xcwlkj.sturegister.service.BjFeignApi;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.common.Convert;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.utils.RequestUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 校园新闻表服务
 * <AUTHOR>
 * @version $Id: ZhxyXwServiceImpl.java, v 0.1 2023年07月19日 14时28分 xcwlkj.com Exp $
 */
@Service("zhxyXwService")
public class ZhxyXwServiceImpl  implements ZhxyXwService  {

    @Resource
    private ZhxyXwMapper modelMapper;
    @Resource
    private ZhxyFbfwService zhxyFbfwService;
    @Resource
    private ZhxyFbfwMapper zhxyFbfwMapper;
    @Resource
    private GetFbfwLsit getFbfwLsit;
    @Resource
    private SbxxUtils sbxxUtils;
    @Resource(name = "campusBoardOperateService")
    private BoardOperateService boardOperateService;
    @Resource
    private BjFeignApi bjFeignApi;

    @Override
    public SelectZhxyXwListVO selectZhxyXwList(SelectZhxyXwListDTO dto) {
        SelectZhxyXwListVO result = new SelectZhxyXwListVO();
        Example example = new Example(ZhxyXw.class);
        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("cjsj desc");

        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }

        //新加权限班级控制
        List<Integer> bhList = zhxyFbfwService.getBhByUserInfo(bizType.getCode());

        if (bhList.isEmpty()){
            return result;
        }
        criteria.andIn("bh",bhList);
        if (StringUtils.isNotBlank(dto.getBt())) {
            criteria.andLike("bt","%" + dto.getBt() + "%");
        }
        if (StringUtils.isNotBlank(dto.getSt())) {
            criteria.andGreaterThanOrEqualTo("fbsj",dto.getSt());
        }
        if (StringUtils.isNotBlank(dto.getEt())) {
            criteria.andLessThanOrEqualTo("fbsj",dto.getEt());
        }
        if (dto.getZt() != null) {
            criteria.andEqualTo("zt",dto.getZt());
        }
        criteria.andEqualTo("fbfwlx",dto.getFbfwlx());

        Page<ZhxyXw> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ZhxyXw> zhxyXwList = modelMapper.selectByExample(example);

        List<XwItemVO> collect = zhxyXwList.stream().map(zhxyXw -> {
            XwItemVO item = new XwItemVO();
            BeanUtils.copyProperties(zhxyXw, item);
            item.setFbsj(DateUtil.format(zhxyXw.getFbsj(), DateUtil.DEFAULT_DATE_TIME));

            return item;
        }).collect(Collectors.toList());


        result.setTotal((int)page.getTotal());
        result.setXwList(collect);
        return result;
    }

    @Override
    public SelectZhxyXwByBhVO selectZhxyXwByBh(SelectZhxyXwByBhDTO dto) {
        SelectZhxyXwByBhVO vo = new SelectZhxyXwByBhVO();
        ZhxyXw zhxyXw = modelMapper.selectByPrimaryKey(dto.getBh());

        BeanUtils.copyProperties(zhxyXw, vo);
        vo.setFbsj(DateUtil.format(zhxyXw.getFbsj(),DateUtil.DEFAULT_DATE_TIME));
        vo.setCjsj(DateUtil.format(zhxyXw.getCjsj(),DateUtil.DEFAULT_DATE_TIME));
        vo.setGxsj(DateUtil.format(zhxyXw.getGxsj(),DateUtil.DEFAULT_DATE_TIME));

        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(zhxyXw.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }

        //详情添加发布范围

        List<FbfwItemVO> fbfwItemVOList = getFbfwLsit.getFbfw(zhxyXw.getBh(), bizType.getCode());

        vo.setFbfwList(fbfwItemVOList);
        return vo;
    }

    @Override
    public void insertZhxyXw(InsertZhxyXwDTO dto) {

        // 若为班级发布，添加发布权限
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            String roleId = RequestUtil.getLoginUser().getCurrRoleId();
            // 如果用户是教师
            if ("001".equals(roleId)) {
                // 判断是否为班主任
                Wrapper<BjTreeVO> bjTreeVOWrapper = bjFeignApi.bjTree(new BjTreeDTO());
                if (bjTreeVOWrapper.getResult() == null){
                    throw new BusinessException("当前登录用户为普通教师，不允许进行班级发布");
                }
            }
        }

        ZhxyXw zhxyXw = new ZhxyXw();
        BeanUtils.copyProperties(dto, zhxyXw);
        if (StringUtils.isNotBlank(dto.getYxjsrq())){
            zhxyXw.setYxjsrq(DateUtil.parse(dto.getYxjsrq(),DateUtil.DEFAULT_DATE));
        }

        //默认展示模式为普通
        zhxyXw.setZsms(0);
        // 默认初始状态为未发布
        zhxyXw.setZt(PublishStatus.UNPUBLISHED.getValue());
        zhxyXw.setCjsj(new Date());
        zhxyXw.setGxsj(new Date());
        modelMapper.insertZhxyXw(zhxyXw);

        // 添加发布范围
        insertZhxyFbfw(dto.getBjjshList(), dto.getBjjsmcList(), dto.getBjjsbjList(),zhxyXw);
    }

    @Override
    public void updateZhxyXw(UpdateZhxyXwDTO dto) {
        ZhxyXw zhxyXw = modelMapper.selectByPrimaryKey(dto.getBh());
        if (zhxyXw.getZt().equals(PublishStatus.PUBLISHED.getValue())){
            throw new BusinessException("已发布的校园新闻不能修改");
        }
        BeanUtils.copyProperties(dto,zhxyXw);
        if (StringUtils.isNotBlank(dto.getYxjsrq())){
            zhxyXw.setYxjsrq(DateUtil.parse(dto.getYxjsrq(),DateUtil.DEFAULT_DATE));
        }

        /*XcSsoUser loginUser = RequestUtil.getLoginUser();
        zhxyXw.setFbr(loginUser.getRealName());
        zhxyXw.setFbrmc(loginUser.getUserName());
        zhxyXw.setFbsj(new Date());*/

        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }

        //删除原先发布数据，重新发布
        zhxyFbfwService.deleteZhxyFbfwByYwbh(bizType, (long)zhxyXw.getBh());
        insertZhxyFbfw(dto.getBjjshList(), dto.getBjjsmcList(), dto.getBjjsbjList(),zhxyXw);

        zhxyXw.setGxsj(new Date());
        modelMapper.updateByPrimaryKey(zhxyXw);
    }

    @Override
    public void publishZhxyXw(PublishZhxyXwDTO dto) {
        ZhxyXw zhxyXw  = modelMapper.selectByPrimaryKey(dto.getBh());
        zhxyXw.setZt(PublishStatus.PUBLISHED.getValue());
        XcSsoUser loginUser = RequestUtil.getLoginUser();
        zhxyXw.setFbr(loginUser.getUserName());
        zhxyXw.setFbrmc(loginUser.getRealName());
        zhxyXw.setFbsj(new Date());
        modelMapper.updateByPrimaryKey(zhxyXw);

        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(zhxyXw.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }

        //获取教室号列表
        List<String> jshList = zhxyFbfwService.selectJshList(bizType, (long)dto.getBh());
        // 组织入参
        InfoPubBodyParams pubBodyParams = InfoPubBodyParams.of(InfoPubActionType.pub.getValue(),
                bizType.getCode(), IdGenerateUtil.generateId(), (JSONObject)JSONObject.toJSON(zhxyXw));

        String ywlx = PublishRangeType.CLASS.getCode().equals(zhxyXw.getFbfwlx())? BusinessType.class_news.getType() : BusinessType.news.getType();

        boardOperateService.infoPublish(sbxxUtils.getDevicesSupplier(jshList,"4869"), ywlx, dto.getBh().toString(), pubBodyParams);
    }

    @Override
    public void unPublishZhxyXw(UnPublishZhxyXwDTO dto) {
        ZhxyXw zhxyXw  = modelMapper.selectByPrimaryKey(dto.getBh());
        zhxyXw.setZt(PublishStatus.UNPUBLISHED.getValue());
        zhxyXw.setFbr(null);
        zhxyXw.setFbrmc(null);
        zhxyXw.setFbsj(null);
        zhxyXw.setGxsj(new Date());
        modelMapper.updateByPrimaryKey(zhxyXw);

        UnPublishVO unPublishVO = new UnPublishVO();
        BeanUtils.copyProperties(zhxyXw,unPublishVO);

        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(zhxyXw.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }

        //获取教室号列表
        List<String> jshList = zhxyFbfwService.selectJshList(bizType, (long)dto.getBh());
        // 组织入参
        InfoPubBodyParams pubBodyParams = InfoPubBodyParams.of(InfoPubActionType.unpub.getValue(),
                bizType.getCode(), IdGenerateUtil.generateId(), (JSONObject)JSONObject.toJSON(unPublishVO));

        String ywlx = PublishRangeType.CLASS.getCode().equals(zhxyXw.getFbfwlx())? BusinessType.class_news.getType() : BusinessType.news.getType();

        boardOperateService.infoPublish(sbxxUtils.getDevicesSupplier(jshList,"4869"), ywlx, dto.getBh().toString(), pubBodyParams);
    }

    @Override
    public void deleteZhxyXws(DeleteZhxyXwsDTO dto) {
        Example example = new Example(ZhxyXw.class);
        example.createCriteria().andIn("bh",dto.getBhs());

        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }

        zhxyFbfwService.deleteZhxyFbfwByYwbhs(bizType, Convert.toStrArray(dto.getBhs()));
        modelMapper.deleteByExample(example);
    }

    /**
     * 新增校园发布范围信息
     *
     * @param zhxyXw 校园应用对象
     */
    public void insertZhxyFbfw(List<String> bjjshList, List<String> bjjsmcList, List<String> bjjsbjList, ZhxyXw zhxyXw)
    {
        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(zhxyXw.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }
        zhxyFbfwService.batchZhxyFbfw(bjjshList, bjjsmcList, bjjsbjList, bizType, zhxyXw.getBh());
    }

    @Override
    public RemoteZhxyXwListVO remoteZhxyXwList(RemoteZhxyXwListDTO dto) {
        RemoteZhxyXwListVO result = new RemoteZhxyXwListVO();

        PublishBizType bizType = PublishBizType.NEWS;
        if (PublishRangeType.CLASS.getCode().equals(dto.getFbfwlx())){
            bizType = PublishBizType.CLASS_NEWS;
        }

        //根据业务类型和教室号查询新闻编号列表
        List<Long> xwbhList = zhxyFbfwMapper.selectYwbhList(bizType.getCode(), dto.getBjjsh());
        if(xwbhList==null || xwbhList.size()<=0){
            result.setXwList(null);
            result.setTotalRows(0);
        }else{
            dto.setXwbhList(xwbhList);
            Page<RemoteXwItemVO> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            List<RemoteXwItemVO> zhxyXucList = modelMapper.remoteZhxyXwList(dto);
            result.setXwList(zhxyXucList);
            result.setTotalRows((int) page.getTotal());
        }
        return result;
    }

    @Override
    public RemoteZhxyXwDetailsVO remoteZhxyXwDetails(RemoteZhxyXwDetailsDTO dto) {
        return modelMapper.remoteZhxyXwDetails(dto);
    }
}