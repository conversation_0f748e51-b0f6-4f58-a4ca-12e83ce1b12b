/**
 * xcwlkj.com Inc.
 * Copyright (c) 2025-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


/**
 * 班级值日表
 * 
 * <AUTHOR>
 * @version $Id: ZhxyBjzr.java, v 0.1 2025年06月23日 10时30分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "zhxy_bjzr")
public class ZhxyBjzr implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 编号 */
    @Id
    @Column(name = "bh")
    private String            bh;
    /** 值日班班号，多个用逗号隔开 */
    @Column(name = "zrbbh")
    private String            zrbbh;
    /** 值日班名称，多个班逗号隔开 */
    @Column(name = "zrbmc")
    private String            zrbmc;
    /** 开课学年度 */
    @Column(name = "kkxnd")
    private String            kkxnd;
    /** 开课学期码 */
    @Column(name = "kkxqm")
    private String            kkxqm;
    /** 值日项属性码 */
    @Column(name = "zrxsxm")
    private String            zrxsxm;
    /** 值日项名称 */
    @Column(name = "zrxmc")
    private String            zrxmc;
    /** 星期 */
    @Column(name = "xq")
    private Integer            xq;
    /** 值日所在周数 */
    @Column(name = "zrszzs")
    private Integer            zrszzs;
    /** 教室号 */
    @Column(name = "jsh")
    private String            jsh;
    /** 值日地点 */
    @Column(name = "zrdd")
    private String            zrdd;
    /** 教室所在校区号 */
    @Column(name = "jsszxqh")
    private String            jsszxqh;
    /** 起始周 */
    @Column(name = "qsz")
    private Integer            qsz;
    /** 终止周 */
    @Column(name = "zzz")
    private Integer            zzz;
    /** 是否单双周  0：每周
            1：单周
            2：双周 */
    @Column(name = "sfdsz")
    private String            sfdsz;
    /** 值日生学号，多个用逗号隔开 */
    @Column(name = "zrsxh")
    private String            zrsxh;
    /** 值日生姓名，多个用逗号隔开 */
    @Column(name = "zrsxm")
    private String            zrsxm;
    /** 发布人 */
    @Column(name = "fbr")
    private String            fbr;
    /** 发布人名称 */
    @Column(name = "fbrmc")
    private String            fbrmc;
    /** 发布时间 */
    @Column(name = "fbsj")
    private Date            fbsj;
    /** 发布状态  0-未发布  1-已发布 */
    @Column(name = "zt")
    private Integer            zt;
    /** 创建时间 */
    @Column(name = "cjsj")
    private Date            cjsj;
    /** 修改时间 */
    @Column(name = "xgsj")
    private Date            xgsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


