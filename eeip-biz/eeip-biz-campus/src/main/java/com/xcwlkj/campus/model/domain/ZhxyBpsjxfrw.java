/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


/**
 * 班牌数据下发任务表
 * 
 * <AUTHOR>
 * @version $Id: ZhxyBpsjxfrw.java, v 0.1 2023年12月08日 14时42分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "zhxy_bpsjxfrw")
public class ZhxyBpsjxfrw implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 编号 */
    @Id
    @Column(name = "bh")
    private Integer            bh;
    /** 运行模式：EDU教学模式、EXAM考试模式 */
    @Column(name = "yxms")
    private String            yxms;
    /** 计划编号:考试模式时存[考试计划编号]，教学模式时存[学年+学期+周次] */
    @Column(name = "jhbh")
    private String            jhbh;
    /** 名称 */
    @Column(name = "mc")
    private String            mc;
    /** 内容 */
    @Column(name = "nr")
    private String            nr;
    /** 发送时间 */
    @Column(name = "fssj")
    private Date            fssj;
    /** 成功时间 */
    @Column(name = "cgsj")
    private Date            cgsj;
    /** 状态0待下发、1下发中、2下发成功、3下发失败 */
    @Column(name = "zt")
    private Integer            zt;
    /** 通知状态0待通知，1已通知 */
    @Column(name = "tzzt")
    private Integer            tzzt;
    /** 失败描述 */
    @Column(name = "sbms")
    private String            sbms;
    /** 创建时间 */
    @Column(name = "cjsj")
    private Date            cjsj;
    /** 更新时间 */
    @Column(name = "gxsj")
    private Date            gxsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


