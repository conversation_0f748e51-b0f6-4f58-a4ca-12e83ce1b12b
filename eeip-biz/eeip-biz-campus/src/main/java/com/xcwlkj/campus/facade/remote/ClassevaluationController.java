/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.facade.remote;

import com.xcwlkj.base.BaseController;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.campus.model.domain.ZhxyBjpjpz;
import com.xcwlkj.campus.model.vo.classevaluation.*;
import com.xcwlkj.campus.service.ZhxyBjpjpzService;
import com.xcwlkj.evaluation.model.dto.record.RecordQueryDTO;
import com.xcwlkj.evaluation.model.vo.record.RecorItemVO;
import com.xcwlkj.evaluation.model.vo.record.RecordQueryVO;
import com.xcwlkj.evaluation.service.RecordFeignApi;
import com.xcwlkj.sturegister.model.dto.bj.GetBjDetailsByJshDTO;
import com.xcwlkj.sturegister.model.vo.bj.GetBjDetailsByJshVO;
import com.xcwlkj.sturegister.service.BjFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xcwlkj.campus.model.dto.classevaluation.GetClassGradeDTO;
import com.xcwlkj.campus.model.resp.classevaluation.GetClassGradeRespModel;
import com.xcwlkj.campus.model.req.classevaluation.GetClassGradeReqModel;
import com.xcwlkj.campus.model.dto.classevaluation.GetClassRankingDTO;
import com.xcwlkj.campus.model.resp.classevaluation.GetClassRankingRespModel;
import com.xcwlkj.campus.model.req.classevaluation.GetClassRankingReqModel;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import com.xcwlkj.campus.model.dto.classevaluation.GetClassMbDTO;
import com.xcwlkj.campus.model.vo.classevaluation.GetClassMbVO;
import com.xcwlkj.campus.model.resp.classevaluation.GetClassMbRespModel;
import com.xcwlkj.campus.model.req.classevaluation.GetClassMbReqModel;
import java.util.List;

/**
 * Classevalution控制层
 * <AUTHOR>
 * @version $Id: ClassevalutionController.java, v 0.1 2025年05月19日 14时29分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("ClassevalutionRemoteController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class ClassevaluationController extends BaseController {
    @Resource
    private ZhxyBjpjpzService zhxyBjpjpzService;
    
    @Resource
    private RecordFeignApi recordFeignApi;
	@Resource
    private BjFeignApi bjFeignApi;
   /**
    * 班级评价-班级绑定
    * @param reqModel
    * @return
    */
//    @PostMapping(value = "/remote/campus/classEvalution/bind")
//    public Wrapper<BindRespModel> bind(@RequestBody BindReqModel reqModel) {
//		log.info("收到请求开始：[班级评价-班级绑定][/remote/campus/classEvalution/bind]reqModel:"+reqModel.toString());
//		BindDTO dto = new BindDTO();
//        dto.setClassBindList(reqModel.getClassBindList());
//        zhxyBjpjpzService.bind(dto);
//        BindRespModel respModel = new BindRespModel();
//
//		log.info("处理请求结束：[班级评价-班级绑定][/remote/campus/classEvalution/bind]reqModel:"+reqModel.toString()
//			+",respModel:"+respModel.toString());
//        return WrapMapper.ok(reqModel, respModel);
//    }
//   /**
//    * 班级评价-班级绑定详情
//    * @param reqModel
//    * @return
//    */
//    @PostMapping(value = "/remote/campus/classEvalution/bindXq")
//    public Wrapper<BindXqRespModel> bindXq(@RequestBody BindXqReqModel reqModel) {
//		log.info("收到请求开始：[班级评价-班级绑定详情][/remote/campus/classEvalution/bindXq]reqModel:"+reqModel.toString());
//		BindXqDTO dto = new BindXqDTO();
//        BindXqVO result = zhxyBjpjpzService.bindXq(dto);
//        BindXqRespModel respModel = new BindXqRespModel();
//        respModel.setBindXqList(result.getBindXqList());
//		log.info("处理请求结束：[班级评价-班级绑定详情][/remote/campus/classEvalution/bindXq]reqModel:"+reqModel.toString()
//			+",respModel:"+respModel.toString());
//        return WrapMapper.ok(reqModel, respModel);
//    }
   /**
    * 班级评价-班级评分
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/campus/classEvaluation/getClassGrade")
    public Wrapper<GetClassGradeRespModel> getClassGrade(@RequestBody GetClassGradeReqModel reqModel) {
		log.info("收到请求开始：[班级评价-班级评分][/remote/campus/classEvalution/getClassGrade]reqModel:"+reqModel.toString());
		GetClassGradeDTO dto = new GetClassGradeDTO();
        dto.setBjjsh(reqModel.getBjjsh());
        GetBjDetailsByJshDTO getBjDetailsByJshDTO = new GetBjDetailsByJshDTO();
        getBjDetailsByJshDTO.setJsh(reqModel.getBjjsh());
        Wrapper<GetBjDetailsByJshVO> getBjDetailsByJshVOWrapper = bjFeignApi.getBjDetailsByJsh(getBjDetailsByJshDTO);
        if (getBjDetailsByJshVOWrapper.getResult() == null){
            throw new BusinessException("无教室对应班级");
        }
        String bah = getBjDetailsByJshVOWrapper.getResult().getBahList().get(0);
        Example example = new Example(ZhxyBjpjpz.class);
        example.createCriteria().andEqualTo("bjh", bah).andEqualTo("isActive","1");
        ZhxyBjpjpz zhxyBjpjpz = zhxyBjpjpzService.selectOneByExample(example);
        // 调用评价记录查询接口获取班级评分
        RecordQueryDTO recordQueryDTO = new RecordQueryDTO();
        recordQueryDTO.setYwbh(String.valueOf(zhxyBjpjpz.getBh()));
        Wrapper<RecordQueryVO> recordQueryVOWrapper = recordFeignApi.recordQuery(recordQueryDTO);
        GetClassGradeRespModel respModel = new GetClassGradeRespModel();
        if (recordQueryVOWrapper == null || recordQueryVOWrapper.getResult() == null 
            || recordQueryVOWrapper.getResult().getRecorList() == null 
            || recordQueryVOWrapper.getResult().getRecorList().isEmpty()) {
            return WrapMapper.ok(reqModel, respModel);
        }
        // 计算所有记录的平均分
        List<RecorItemVO> recordList = recordQueryVOWrapper.getResult().getRecorList();
        double totalScore = 0;
        for (RecorItemVO record : recordList) {
            totalScore += Double.parseDouble(record.getZpf());
        }
        double averageScore = totalScore / recordList.size();
        
        respModel.setGrade(String.valueOf((int)Math.round(averageScore))); // 四舍五入取整
        
		log.info("处理请求结束：[班级评价-班级评分][/remote/campus/classEvalution/getClassGrade]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 班级评价-班级排名
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/campus/classEvaluation/getClassRanking")
    public Wrapper<GetClassRankingRespModel> getClassRanking(@RequestBody GetClassRankingReqModel reqModel) {
		log.info("收到请求开始：[班级评价-班级排名][/remote/campus/classEvalution/getClassRanking]reqModel:"+reqModel.toString());
		GetClassRankingDTO dto = new GetClassRankingDTO();
        dto.setMbbh(reqModel.getMbbh());
        
        // 根据模板编号查询班级评价配置
        Example example = new Example(ZhxyBjpjpz.class);
        example.createCriteria().andEqualTo("pjmb", reqModel.getMbbh()).andEqualTo("isActive","1");
        List<ZhxyBjpjpz> zhxyBjpjpzList = zhxyBjpjpzService.selectByExample(example);
        
        if (zhxyBjpjpzList == null || zhxyBjpjpzList.isEmpty()) {
            throw new BusinessException("未找到班级评价配置");
        }
        
        // 获取所有班级的评分
        List<ClassRankItemVO> classRankList = new ArrayList<>();
        for (ZhxyBjpjpz zhxyBjpjpz : zhxyBjpjpzList) {
            RecordQueryDTO recordQueryDTO = new RecordQueryDTO();
            recordQueryDTO.setPjmbh(dto.getMbbh());
            // 班级
//            recordQueryDTO.setYwbh(String.valueOf(zhxyBjpjpz.getBh()));
            recordQueryDTO.setPjdxlx("10");
//            recordQueryDTO.setPjdxbh(zhxyBjpjpz.getBjh());
//            recordQueryDTO.setPjdxmc();

            Wrapper<RecordQueryVO> recordQueryVOWrapper = recordFeignApi.recordQuery(recordQueryDTO);
            if (recordQueryVOWrapper != null && recordQueryVOWrapper.getResult() != null
                && recordQueryVOWrapper.getResult().getRecorList() != null
                && !recordQueryVOWrapper.getResult().getRecorList().isEmpty()) {
                
                // 计算该班级所有评价记录的平均分
                List<RecorItemVO> recordList = recordQueryVOWrapper.getResult().getRecorList();
                double totalScore = 0;
                for (RecorItemVO record : recordList) {
                    totalScore += Double.parseDouble(record.getZpf());
                }
                double averageScore = totalScore / recordList.size();
                
                // 创建排名项
                ClassRankItemVO rankItem = new ClassRankItemVO();
                rankItem.setBjh(zhxyBjpjpz.getBjh());
                rankItem.setBjmc(zhxyBjpjpz.getBjmc());
                rankItem.setBjpf(String.valueOf((int)Math.round(averageScore))); // 四舍五入取整
                classRankList.add(rankItem);
            }
        }
        
        // 按评分降序排序
        classRankList.sort((a, b) -> Integer.compare(
            Integer.parseInt(b.getBjpf()), 
            Integer.parseInt(a.getBjpf())
        ));
        
        GetClassRankingRespModel respModel = new GetClassRankingRespModel();
        respModel.setClassRankList(classRankList);
        
		log.info("处理请求结束：[班级评价-班级排名][/remote/campus/classEvalution/getClassRanking]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
//   /**
//    * 班级评价-班级评价列表
//    * @param reqModel
//    * @return
//    */
//    @PostMapping(value = "/remote/campus/classEvalution/list")
//    public Wrapper<ListRespModel> list(@RequestBody ListReqModel reqModel) {
//		log.info("收到请求开始：[班级评价-班级评价列表][/remote/campus/classEvalution/list]reqModel:"+reqModel.toString());
//		ListDTO dto = new ListDTO();
//        dto.setDwh(reqModel.getDwh());
//        dto.setSsnj(reqModel.getSsnj());
//        dto.setBjh(reqModel.getBjh());
//        dto.setIsSelfOnly(reqModel.getIsSelfOnly());
//        dto.setPageNum(reqModel.getPageNum());
//        dto.setPageSize(reqModel.getPageSize());
//
//        String userName = RequestUtil.getLoginUser().getUserName();
//        String roleId = RequestUtil.getLoginUser().getCurrRoleId();
//
//        // 如果用户是学生，返回空
//        if ("002".equals(roleId)) {
//            return WrapMapper.ok();
//        }
//
//        // 获取班级列表
//        ClasslistDTO classDTO = new ClasslistDTO();
//        classDTO.setDwh(reqModel.getDwh());
//        classDTO.setSsnj(reqModel.getSsnj());
//        classDTO.setPageNum(1);
//        classDTO.setPageSize(99999);
//        Wrapper<ClasslistVO> wrapper = bjFeignApi.classlist(classDTO);
//        if (wrapper == null || wrapper.getResult() == null || wrapper.getResult().getBjList() == null) {
//            return WrapMapper.ok();
//        }
//
//        List<ClassEvalutionItemVO> classEvalutionList = new ArrayList<>();
//
//        // 教师角色查询管理班级
//        if ("001".equals(roleId)) {
//            GetManagedClassDTO getManagedClassDTO = new GetManagedClassDTO();
//            getManagedClassDTO.setBzrgh(userName);
//            Wrapper<GetManagedClassVO> managedClass = bjFeignApi.getManagedClass(getManagedClassDTO);
//            if (managedClass != null && managedClass.getResult() != null && managedClass.getResult().getBjxxList() != null) {
//                // 获取管理班级的班级号列表
//                List<String> managedBjhs = managedClass.getResult().getBjxxList().stream()
//                    .map(BjxxItemVO::getBjh)
//                    .collect(Collectors.toList());
//
//                // 过滤出管理的班级
//                classEvalutionList = wrapper.getResult().getBjList().stream()
//                    .filter(bj -> managedBjhs.contains(bj.getBah()))
//                    .map(bj -> {
//                        ClassEvalutionItemVO item = new ClassEvalutionItemVO();
//                        item.setBjh(bj.getBah());
//                        item.setBjmc(bj.getBjmc());
//                        return item;
//                    })
//                    .collect(Collectors.toList());
//            }
//        } else {
//            // 其他角色返回所有班级
//            classEvalutionList = wrapper.getResult().getBjList().stream()
//                .map(bj -> {
//                    ClassEvalutionItemVO item = new ClassEvalutionItemVO();
//                    item.setBjh(bj.getBah());
//                    item.setBjmc(bj.getBjmc());
//                    return item;
//                })
//                .collect(Collectors.toList());
//        }
//
//        // 查询每个班级的评价配置和评分
//        for (ClassEvalutionItemVO item : classEvalutionList) {
//            // 查询班级评价配置
//            Example example = new Example(ZhxyBjpjpz.class);
//            example.createCriteria()
//                .andEqualTo("bjh", item.getBjh())
//                .andEqualTo("isActive", "1");
//            ZhxyBjpjpz bjpjpz = zhxyBjpjpzService.selectOneByExample(example);
//
//            if (bjpjpz != null) {
//                item.setMbbh(bjpjpz.getPjmb());
//                item.setMbmc(bjpjpz.getPjmbmc());
//                item.setBh(String.valueOf(bjpjpz.getBh()));
//
//                // 查询班级评分
//                EvaRecordQueryReqModel evaRecordQueryReqModel = new EvaRecordQueryReqModel();
//                evaRecordQueryReqModel.setPjmbbh(bjpjpz.getPjmb());
//                evaRecordQueryReqModel.setYwbh(String.valueOf(bjpjpz.getBh()));
//                evaRecordQueryReqModel.setYwlx("5");
//                evaRecordQueryReqModel.setPjdxlx("4");
//                evaRecordQueryReqModel.setPjdxbh(bjpjpz.getBjh());
//                evaRecordQueryReqModel.setPjdxmc(bjpjpz.getBjmc());
//
//                Wrapper<EvaRecordQueryRespModel> evaRecordQueryResult = evaRecordController.evaRecordQuery(evaRecordQueryReqModel);
//                if (evaRecordQueryResult != null && evaRecordQueryResult.getResult() != null
//                    && evaRecordQueryResult.getResult().getEvaRecordQueryRespList() != null
//                    && !evaRecordQueryResult.getResult().getEvaRecordQueryRespList().isEmpty()) {
//                    EvaRecordQueryRespItemVO latestRecord = evaRecordQueryResult.getResult().getEvaRecordQueryRespList().get(0);
//                    item.setBjpf(String.valueOf(latestRecord.getZpf()));
//                }
//            }
//        }
//
////        // 分页处理
////        int totalRows = classEvalutionList.size();
////        int start = (reqModel.getPageNum() - 1) * reqModel.getPageSize();
////        int end = Math.min(start + reqModel.getPageSize(), totalRows);
////        List<ClassEvalutionItemVO> pagedList = start < totalRows ?
////            classEvalutionList.subList(start, end) : new ArrayList<>();
////
//        ListRespModel respModel = new ListRespModel();
//        respModel.setClassEvalutionList(null);
////        respModel.setTotalRows(totalRows);
//
//		log.info("处理请求结束：[班级评价-班级评价列表][/remote/campus/classEvalution/list]reqModel:"+reqModel.toString()
//			+",respModel:"+respModel.toString());
//        return WrapMapper.ok(reqModel, respModel);
//    }
   /**
    * 班级评价-班级绑定模板
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/campus/classEvaluation/getClassMb")
    public Wrapper<GetClassMbRespModel> getClassMb(@RequestBody GetClassMbReqModel reqModel) {
		log.info("收到请求开始：[班级评价-班级绑定模板][/remote/campus/classEvaluation/getClassMb]reqModel:"+reqModel.toString());
		GetClassMbDTO dto = new GetClassMbDTO();
        dto.setBjjsh(reqModel.getBjjsh());
        GetClassMbVO result = zhxyBjpjpzService.getClassMb(dto);
        GetClassMbRespModel respModel = new GetClassMbRespModel();
        respModel.setMbbh(result.getMbbh());
        respModel.setMbmc(result.getMbmc());
        respModel.setBh(result.getBh());
		log.info("处理请求结束：[班级评价-班级绑定模板][/remote/campus/classEvaluation/getClassMb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

}