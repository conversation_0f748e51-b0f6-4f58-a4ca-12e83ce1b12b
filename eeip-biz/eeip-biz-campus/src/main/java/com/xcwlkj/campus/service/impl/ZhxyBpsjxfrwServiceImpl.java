/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.campus.mapper.ZhxyBpsjxfrwMapper;
import com.xcwlkj.campus.model.domain.ZhxyBpsjxfrw;
import com.xcwlkj.campus.model.dos.DistributeTaskContent;
import com.xcwlkj.campus.model.dos.EduDistributeTaskContent;
import com.xcwlkj.campus.model.dos.ExamDistributeTaskContent;
import com.xcwlkj.campus.model.dto.datadistribute.CreateTaskDTO;
import com.xcwlkj.campus.model.dto.datadistribute.DeleteTaskDTO;
import com.xcwlkj.campus.model.dto.datadistribute.DistributeTaskListDTO;
import com.xcwlkj.campus.model.dto.datadistribute.RestartTaskDTO;
import com.xcwlkj.campus.model.enums.DistributeStatus;
import com.xcwlkj.campus.model.enums.RunMode;
import com.xcwlkj.campus.model.vo.datadistribute.DistributeTaskItemVO;
import com.xcwlkj.campus.model.vo.datadistribute.DistributeTaskListVO;
import com.xcwlkj.campus.service.ZhxyBpsjxfrwService;
import com.xcwlkj.campus.task.DistributeTask;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 班牌数据下发任务表服务
 * <AUTHOR>
 * @version $Id: ZhxyBpsjxfrwServiceImpl.java, v 0.1 2023年07月20日 16时24分 xcwlkj.com Exp $
 */
@Service("zhxyBpsjxfrwService")
@Slf4j
public class ZhxyBpsjxfrwServiceImpl extends BaseServiceImpl<ZhxyBpsjxfrwMapper, ZhxyBpsjxfrw> implements ZhxyBpsjxfrwService  {

    @Resource
    private ZhxyBpsjxfrwMapper modelMapper;
    @Resource
	private DistributeTask distributeTask;
    

    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpsjxfrwService#distributeTaskList(com.xcwlkj.campus.model.dto.datadistribute.DistributeTaskListDTO)
     */
	@Override
	public DistributeTaskListVO distributeTaskList(DistributeTaskListDTO dto) {
		DistributeTaskListVO result = new DistributeTaskListVO();
		Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
		Example example = new Example(ZhxyBpsjxfrw.class);
		Example.Criteria cr = example.createCriteria();
		if(StringUtil.isNotBlank(dto.getYxms())) {
			cr.andEqualTo("yxms",dto.getYxms());
		}
		if(StringUtil.isNotBlank(dto.getRq())) {
			cr.andCondition("DATE_FORMAT(fssj,'%Y-%m-%d') = '"+ dto.getRq() + "'");
		}
		if(dto.getZt() != null ) {
			cr.andEqualTo("zt",dto.getZt());
		}
		example.orderBy("cjsj").desc();
		List<ZhxyBpsjxfrw> list = modelMapper.selectByExample(example);
		result.setTotalRows((int)page.getTotal());
		result.setDistributeTaskList(new ArrayList<>());
		for(ZhxyBpsjxfrw model : list) {
			DistributeTaskItemVO itemVO = new DistributeTaskItemVO();
			itemVO.setBh(model.getBh());
			itemVO.setYxms(model.getYxms());
			itemVO.setRwmc(model.getMc());
			itemVO.setRwnr(model.getNr());
			itemVO.setXfsj(DateUtil.formatDateTime(model.getFssj()));
			itemVO.setCgsj(DateUtil.formatDateTime(model.getCgsj()));
			itemVO.setZt(model.getZt());
			itemVO.setSbms(model.getSbms());
			result.getDistributeTaskList().add(itemVO);
		}
		return result;

	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpsjxfrwService#createTask(com.xcwlkj.campus.model.dto.datadistribute.CreateTaskDTO)
     */
	@Override
	public void createTask(CreateTaskDTO dto) {
		String classify = dto.getDistributeTaskContent().getClassify();
		DistributeTaskContent distributeTaskContent;
		switch (RunMode.get(classify)){
			case EDU:
				distributeTaskContent = BeanUtil.copyProperties(dto.getDistributeTaskContent(), EduDistributeTaskContent.class);
				break;
			case EXAM:
				distributeTaskContent = BeanUtil.copyProperties(dto.getDistributeTaskContent(), ExamDistributeTaskContent.class);
				break;
			default:
				throw new BusinessException("运行模式设置错误");
		}

		createTask(classify, dto.getRwmc(), distributeTaskContent);
	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpsjxfrwService#restartTask(com.xcwlkj.campus.model.dto.datadistribute.RestartTaskDTO)
     */
	@Override
	public void restartTask(RestartTaskDTO dto) {

		ZhxyBpsjxfrw bpsjxfrw = modelMapper.selectByPrimaryKey(dto.getRwbh());
		bpsjxfrw.setZt(DistributeStatus.INIT.getCode());
		bpsjxfrw.setCgsj(null);
		bpsjxfrw.setSbms(null);
		bpsjxfrw.setGxsj(new Date());
		modelMapper.updateByPrimaryKey(bpsjxfrw);

		distributeTask.startTask(bpsjxfrw.getBh());
	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpsjxfrwService#deleteTask(com.xcwlkj.campus.model.dto.datadistribute.DeleteTaskDTO)
     */
	@Override
	public void deleteTask(DeleteTaskDTO dto) {
		Example example = new Example(ZhxyBpsjxfrw.class);
		example.createCriteria()
				.andIn("bh", dto.getRwbhList());

		modelMapper.deleteByExample(example);
	}

	@Override
	public int updateByPrimaryKey(ZhxyBpsjxfrw record) {
		record.setGxsj(new Date());
		return modelMapper.updateByPrimaryKey(record);
	}

	@Override
	public void createTask(String classify, String taskName, DistributeTaskContent distributeTaskContent) {
		createTask(classify, taskName, distributeTaskContent, false);
	}

	@Override
	public void createTask(String classify, String taskName, DistributeTaskContent distributeTaskContent, boolean needCheckTaskExists) {

		if (needCheckTaskExists) {
			// 是否已经创建下发任务
			Example emTask = new Example(ZhxyBpsjxfrw.class);
			emTask.createCriteria()
					.andEqualTo("yxms", classify)
					.andEqualTo("nr", distributeTaskContent.toString())
					.andIn("zt", Arrays.asList(DistributeStatus.INIT.getCode(), DistributeStatus.DOING.getCode(), DistributeStatus.SUCCESS.getCode()));
			List<ZhxyBpsjxfrw> zhxyBpsjxfrws = modelMapper.selectByExample(emTask);
			if (zhxyBpsjxfrws.size() > 0) {
				log.info("下发任务已创建");
				return;
			}
		}

		// 下发任务初始化
		ZhxyBpsjxfrw bpsjxfrw = new ZhxyBpsjxfrw();
		bpsjxfrw.setYxms(classify);
		bpsjxfrw.setMc(taskName);

		switch (RunMode.get(classify)) {
			case EDU:
				EduDistributeTaskContent eduDistributeTaskContent = (EduDistributeTaskContent) distributeTaskContent;
				bpsjxfrw.setJhbh(eduDistributeTaskContent.getXn()+"_"+eduDistributeTaskContent.getXqm()+"_"+eduDistributeTaskContent.getZc());
				break;
			case EXAM:
				ExamDistributeTaskContent examDistributeTaskContent = (ExamDistributeTaskContent) distributeTaskContent;
				bpsjxfrw.setJhbh(examDistributeTaskContent.getKsjhbh());
				break;
			default:
				throw new BusinessException("运行模式设置错误");
		}

		bpsjxfrw.setNr(distributeTaskContent.toString());
		bpsjxfrw.setZt(DistributeStatus.INIT.getCode());
		bpsjxfrw.setCjsj(new Date());
		bpsjxfrw.setGxsj(new Date());
		modelMapper.insertSelective(bpsjxfrw);

		distributeTask.startTask(bpsjxfrw.getBh());
	}

	@Override
	public void updateDistributeResult(Integer taskId, Integer result, String desc) {
		ZhxyBpsjxfrw zhxyBpsjxfrw = selectByPrimaryKey(taskId);

		DistributeStatus distributeStatus = DistributeStatus.get(result);
		zhxyBpsjxfrw.setZt(distributeStatus.getCode());
		switch (distributeStatus){
			case SUCCESS:
				zhxyBpsjxfrw.setCgsj(new Date());
				zhxyBpsjxfrw.setSbms(null);
				break;
			case FAIL:
				zhxyBpsjxfrw.setCgsj(null);
				zhxyBpsjxfrw.setSbms(desc);
				break;
		}
		updateByPrimaryKey(zhxyBpsjxfrw);
	}
}