package com.xcwlkj.attendance.service.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.xcwlkj.attendance.mapper.KqDkjlMapper;
import com.xcwlkj.attendance.mapper.KqJskqMapper;
import com.xcwlkj.attendance.mapper.KqXskqMapper;
import com.xcwlkj.attendance.model.domain.KqDkjl;
import com.xcwlkj.attendance.model.domain.KqJskq;
import com.xcwlkj.attendance.model.domain.KqXskq;
import com.xcwlkj.attendance.taskcenter.model.StudentAttendanceNoticeModel;
import com.xcwlkj.attendance.taskcenter.model.TeacherAttendanceNoticeModel;
import com.xcwlkj.biz.unifyaccess.mqtt.service.MqttMsgBizHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class BizMqttMsgRevHandler implements MqttMsgBizHandler {

    @Resource
    private KqDkjlMapper kqDkjlMapper;
    @Resource
    private KqXskqMapper kqXskqMapper;

    @Resource
    private KqJskqMapper kqJskqMapper;

    public BizMqttMsgRevHandler() {
    }


    @Override
    public void handle(String topic, Object payload) {
        if (payload instanceof String) {
            String payloadStr = (String) payload;
            if (StringUtils.contains(topic, "on_student_attendance"))  {
                handleStudentAttendance(payloadStr);
            } else if (StringUtils.contains(topic, "on_teacher_attendance")) {
                handleTeacherAttendance(payloadStr);
            } else {
                log.info("topic[" + topic + "] 没有方法可以处理这个主题...");
            }
        }
    }

    private void handleStudentAttendance(String msg) {
        log.info("接收到学生考勤信息: {}", msg);

        String jsonString = msg.substring(msg.indexOf('=') + 1);
        JSONObject dataObject = new JSONObject(jsonString).getJSONObject("data");
        StudentAttendanceNoticeModel studentModel = JSON.parseObject(dataObject.toString(), StudentAttendanceNoticeModel.class);

        KqDkjl kqDkjl = new KqDkjl();
        kqDkjl.setRylb(1);
        kqDkjl.setXgh(studentModel.getStudyNumber());
        kqDkjl.setDkfs("2");

        try {
            kqDkjl.setDksj(new Date(Long.parseLong(studentModel.getTime())));
        } catch (Exception e) {
            log.error("Error parsing timestamp: {}", e.getMessage());
            return;
        }

        BeanUtils.copyProperties(studentModel, kqDkjl);
        kqDkjl.setCjsj(new Date());
        kqDkjl.setGxsj(new Date());
        kqDkjlMapper.insert(kqDkjl);

        int kqjg = 0;
        if (Long.parseLong(studentModel.getTime()) > studentModel.getPkkssj().getTime()) {
            kqjg = 2;
        }

        KqXskq kqXskq = new KqXskq();
        kqXskq.setPkbbh(studentModel.getPkbbh());
        kqXskq.setXh(studentModel.getStudyNumber());
        kqXskq = kqXskqMapper.selectOne(kqXskq);

        if (kqXskq != null) {
            BeanUtils.copyProperties(studentModel, kqXskq);
            kqXskq.setKqjg(kqjg);
            kqXskq.setKqsj(kqDkjl.getDksj());
            kqXskq.setGxsj(new Date());

            kqXskqMapper.updateByPrimaryKey(kqXskq);
        } else {
            kqXskq = new KqXskq();
            BeanUtils.copyProperties(studentModel, kqXskq);
            kqXskq.setKqjg(kqjg);
            kqXskq.setKqsj(kqDkjl.getDksj());
            kqXskq.setGxsj(new Date());
            kqXskq.setCjsj(new Date());
            kqXskq.setXh(studentModel.getStudyNumber());
            kqXskqMapper.insert(kqXskq);
        }
    }

    private void handleTeacherAttendance(String msg) {
        log.info("接收到教师考勤信息: {}", msg);

        String jsonString = msg.substring(msg.indexOf('=') + 1);
        JSONObject dataObject = new JSONObject(jsonString).getJSONObject("data");
        TeacherAttendanceNoticeModel teacherModel = JSON.parseObject(dataObject.toString(), TeacherAttendanceNoticeModel.class);

        KqDkjl kqDkjl = new KqDkjl();
        //教师0，学生1
        kqDkjl.setRylb(0);
        kqDkjl.setXgh(teacherModel.getJobNumber());
        kqDkjl.setDkfs("2");
        //时间戳字符串转datetime格式
        try {
            kqDkjl.setDksj(new Date(Long.parseLong(teacherModel.getTime())));
        } catch (Exception e) {
            log.error("时间戳解析错误: {}", e.getMessage());
            return;
        }
        BeanUtils.copyProperties(teacherModel, kqDkjl);
        kqDkjl.setCjsj(new Date());
        kqDkjl.setGxsj(new Date());
        kqDkjlMapper.insert(kqDkjl);

        int kqjg = 0;
        //打卡时间大于排课开始时间，考勤结果为迟到
        if (Long.parseLong(teacherModel.getTime())>teacherModel.getPkkssj().getTime()){
            kqjg = 2;
        }

        //学生考勤表修改
        KqJskq kqJskq = new KqJskq();
        kqJskq.setPkbbh(teacherModel.getPkbbh());
        kqJskq.setGh(teacherModel.getJobNumber());
        kqJskq = kqJskqMapper.selectOne(kqJskq);

        if (kqJskq != null) {
            // 如果已存在，则更新
            BeanUtils.copyProperties(teacherModel, kqJskq);
            kqJskq.setKqjg(kqjg);
            kqJskq.setKqsj(kqDkjl.getDksj());
            kqJskq.setGxsj(new Date());
            kqJskqMapper.updateByPrimaryKey(kqJskq);
        } else {
            // 如果不存在，则插入
            kqJskq = new KqJskq(); // 注意这里重新创建了kqXskq对象
            BeanUtils.copyProperties(teacherModel, kqJskq);
            kqJskq.setKqjg(kqjg);
            kqJskq.setKqsj(kqDkjl.getDksj());
            kqJskq.setGxsj(new Date());
            kqJskq.setCjsj(new Date());
            kqJskq.setGh(teacherModel.getJobNumber());
            kqJskqMapper.insert(kqJskq);
        }

    }

}
