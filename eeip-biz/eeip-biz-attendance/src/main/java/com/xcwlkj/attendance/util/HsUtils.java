package com.xcwlkj.attendance.util;

import cn.hutool.crypto.symmetric.SM4;
import com.xcwlkj.attendance.model.enums.ProductEnum;
import com.xcwlkj.attendance.model.enums.SubSysEnum;
import com.xcwlkj.util.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.ComputerSystem;
import oshi.hardware.HardwareAbstractionLayer;

@Slf4j
public class HsUtils {

    private final static String key = "7ed0a55691534aa8a9a692d373deeee8";

    private final static String  iv  = "6bSWAnEG/f2onZ7S";

    private static String productPrefix = ProductEnum.CENCGW100_S.getCode();

    private static String sm4CommonKey = "hisomeIVsfhy@123";

    private static  String   ComputerIdentifier = "";

    static {
        ComputerIdentifier = doGenerateComputerIdentifier();
    }

    public static String doGenerateComputerIdentifier() {
        SystemInfo systemInfo = new SystemInfo();
        HardwareAbstractionLayer hardwareAbstractionLayer = systemInfo.getHardware();
        CentralProcessor centralProcessor = hardwareAbstractionLayer.getProcessor();
        ComputerSystem computerSystem = hardwareAbstractionLayer.getComputerSystem();
        String macaddr = StringUtils.replace(hardwareAbstractionLayer.getNetworkIFs().get(0).getMacaddr(), ":", "");

        String processorSerialNumber = computerSystem.getSerialNumber();
        String processorIdentifier = centralProcessor.getProcessorIdentifier().getIdentifier();
        return ProductEnum.CENCGW100_S.getType() + String.format("%08x", macaddr.hashCode())
                + String.format("%08x", processorIdentifier.hashCode()) ;
    }

    public static String generateComputerIdentifier(){
        return ComputerIdentifier;
    }

    private static String generateCommonTopicPrefix(){
        return "/"+ productPrefix +"/"+ HsUtils.generateComputerIdentifier();
    }

    public static String generateTopicFunctionInvoke(){
        return generateCommonTopicPrefix()+"/function/invoke";
    }

    public static String generateTopicTokenGetReply(){
        return generateCommonTopicPrefix()+"/token/get/reply";
    }

    public static String generateTopicTokenGet(){
        return generateCommonTopicPrefix()+"/token/get";
    }

    public static String generateTopicOnline(){
        return generateCommonTopicPrefix()+"/online";
    }

    public static String generateTopicKDGWXZZT(){
        return generateCommonTopicPrefix()+"/event/KDGWXZZT";
    }

    public static String generateTopicFunctionInvokeReply(){
        return generateCommonTopicPrefix()+"/function/invoke/reply";
    }

    public static String generateSbCache(String key){
        return SubSysEnum.IDENTITYVERIFY.getCode()+":SBXX:"+key;
    }


    public static String hisomeUperPwdEncode(String pwd){
        return  AesUtil.encrypt(pwd,key,false,iv);
    }

    public static String generateSbmySecretStr(String plainText){
        String my = new SM4(sm4CommonKey.getBytes()).encryptHex(plainText);
        if(StringUtils.length(my)<16){
            my=my+sm4CommonKey;
        }
        my = StringUtils.substring(my, 0, 16);
        return my;
    }

    /**
     * 身份证号保密处理
     */
    public static String EncryptionSFZH(String sfzh) {
        if (StringUtils.isNotBlank(sfzh) && sfzh.length() == 18) {
            String start = sfzh.substring(0, 6);
            String end = sfzh.substring(sfzh.length() - 4);
            return start + "********" + end;
        } else {
            return sfzh;
        }
    }
}
