package com.xcwlkj.identityverify.facade.manager;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xcwlkj.identityverify.model.req.cxtj.KkwTjReqModel;
import com.xcwlkj.identityverify.model.req.cxtj.KkwXqReqModel;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 查询统计控制器测试
 * <AUTHOR>
 * @version $Id: CxtjControllerTest.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@WebMvcTest(CxtjController.class)
public class CxtjControllerTest {

    @Resource
    private MockMvc mockMvc;

    @MockBean
    private KsKkwMsgService ksKkwMsgService;

    private ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 测试空考位统计查询接口
     */
    @Test
    public void testGetEmptySeatsStatistics() throws Exception {
        // 准备测试数据
        KkwTjReqModel reqModel = new KkwTjReqModel();
        reqModel.setKsjhbh("TEST_KSJHBH_001");
        reqModel.setCcm("001");
        reqModel.setPageNum(1);
        reqModel.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/manager/identity/cxtj/kkwtj")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqModel)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试空考位统计查询接口 - 带上报状态筛选
     */
    @Test
    public void testGetEmptySeatsStatistics_WithReportStatus() throws Exception {
        // 准备测试数据
        KkwTjReqModel reqModel = new KkwTjReqModel();
        reqModel.setKsjhbh("TEST_KSJHBH_001");
        reqModel.setCcm("001");
        reqModel.setSbzt("1"); // 已上报
        reqModel.setPageNum(1);
        reqModel.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/manager/identity/cxtj/kkwtj")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqModel)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试空考位详情查询接口
     */
    @Test
    public void testGetEmptySeatsDetails() throws Exception {
        // 准备测试数据
        KkwXqReqModel reqModel = new KkwXqReqModel();
        reqModel.setKsjhbh("TEST_KSJHBH_001");
        reqModel.setCcm("001");
        reqModel.setPageNum(1);
        reqModel.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/manager/identity/cxtj/kkwxq")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqModel)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试空考位详情查询接口 - 带异常类型筛选
     */
    @Test
    public void testGetEmptySeatsDetails_WithExceptionType() throws Exception {
        // 准备测试数据
        KkwXqReqModel reqModel = new KkwXqReqModel();
        reqModel.setKsjhbh("TEST_KSJHBH_001");
        reqModel.setCcm("001");
        reqModel.setYclx("6"); // 缺考（空位）
        reqModel.setPageNum(1);
        reqModel.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/manager/identity/cxtj/kkwxq")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqModel)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试参数校验 - 考试计划编号为空
     */
    @Test
    public void testGetEmptySeatsStatistics_EmptyKsjhbh() throws Exception {
        // 准备测试数据
        KkwTjReqModel reqModel = new KkwTjReqModel();
        reqModel.setKsjhbh(""); // 空的考试计划编号
        reqModel.setPageNum(1);
        reqModel.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/manager/identity/cxtj/kkwtj")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqModel)))
                .andExpect(status().isBadRequest()); // 应该返回400错误
    }

    /**
     * 测试参数校验 - 页码为空
     */
    @Test
    public void testGetEmptySeatsStatistics_EmptyPageNum() throws Exception {
        // 准备测试数据
        KkwTjReqModel reqModel = new KkwTjReqModel();
        reqModel.setKsjhbh("TEST_KSJHBH_001");
        reqModel.setPageSize(10);
        // pageNum为空

        // 执行测试
        mockMvc.perform(post("/manager/identity/cxtj/kkwtj")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reqModel)))
                .andExpect(status().isBadRequest()); // 应该返回400错误
    }
}
