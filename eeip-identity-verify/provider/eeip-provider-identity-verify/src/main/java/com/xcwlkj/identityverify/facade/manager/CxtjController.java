/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.facade.manager;

import com.github.pagehelper.PageInfo;
import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.identityverify.model.dto.cxtj.*;
import com.xcwlkj.identityverify.model.req.cxtj.*;
import com.xcwlkj.identityverify.model.req.sbyw.KwzdxztjReqModel;
import com.xcwlkj.identityverify.model.resp.cxtj.*;
import com.xcwlkj.identityverify.model.resp.sbyw.KwzdxztjRespModel;
import com.xcwlkj.identityverify.model.resp.sbyw.KwzdxztjdcRespModel;
import com.xcwlkj.identityverify.model.vo.cxtj.*;
import org.springframework.beans.BeanUtils;
import com.xcwlkj.identityverify.service.KsJkryRcxxService;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import com.xcwlkj.identityverify.service.KsKsrcxxService;
import com.xcwlkj.identityverify.service.KsKssjDistributeStatusService;
import com.xcwlkj.identityverify.service.KsKwMessageService;
import com.xcwlkj.identityverify.service.KsProcessEventService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xcwlkj.identityverify.model.dto.cxtj.KwsbdcxDTO;
import com.xcwlkj.identityverify.model.vo.cxtj.KwsbdcxVO;
import com.xcwlkj.identityverify.model.resp.cxtj.KwsbdcxRespModel;
import com.xcwlkj.identityverify.model.req.cxtj.KwsbdcxReqModel;
import javax.annotation.Resource;

/**
 * Cxtj控制层
 * <AUTHOR>
 * @version $Id: CxtjController.java, v 0.1 2023年10月17日 15时49分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("CxtjManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class CxtjController extends BaseController {

    @Resource
    private KsKsrcxxService ksKsrcxxServiceService;
    @Resource
    private KsJkryRcxxService ksJkryRcxxService;
    @Resource
    private KsProcessEventService ksProcessEventService;
    @Resource
    private KsKssjDistributeStatusService ksKssjDistributeStatusService;
    @Resource
    private KsKwMessageService ksKwMessageService;
    @Resource
    private KsKkwMsgService ksKkwMsgService;

   /**
    * 考生入场缺考情况查询
    * @param reqModel
    * @return
    */
	@Permission("cxtj:ksrcqkxqcx")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrcqkxqcx")
    public Wrapper<KsrcqkxqcxRespModel> ksrcqkxqcx(@RequestBody KsrcqkxqcxReqModel reqModel) {
		log.info("收到请求开始：[考生入场缺考情况查询][/manager/identityverify/cxtj/ksrcqkxqcx]reqModel:"+reqModel.toString());
		KsrcqkxqcxDTO dto = new KsrcqkxqcxDTO();
        dto.setOrderCol(reqModel.getOrderCol());
        dto.setOrderType(reqModel.getOrderType());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setCsbh(reqModel.getCsbh());
        dto.setSfqk(reqModel.getSfqk());
        dto.setKcbh(reqModel.getKcbh());
        dto.setZkzh(reqModel.getZkzh());
        dto.setKsxm(reqModel.getKsxm());
        dto.setZwh(reqModel.getZwh());
        dto.setSfzh(reqModel.getSfzh());
        dto.setBzhkdid(reqModel.getBzhkdid());
        dto.setSbfs(reqModel.getSbfs());
        dto.setKsh(reqModel.getKsh());
        dto.setCslx(reqModel.getCslx());
//        throw new BusinessException("请完成控制器对应的方法");
		KsrcqkxqcxVO result = ksKsrcxxServiceService.ksrcqkxqcx(dto);
        KsrcqkxqcxRespModel respModel = new KsrcqkxqcxRespModel();
        respModel.setDatas(result.getDatas());
        respModel.setTotalRows(result.getTotalRows());
		log.info("处理请求结束：[考生入场缺考情况查询][/manager/identityverify/cxtj/ksrcqkxqcx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
	@Permission("cxtj:ksrcqkxqdc")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrcqkxqdc")
    public Wrapper<KsrcqkxqdcRespModel> ksrcqkxqdc(@RequestBody KsrcqkxqdcReqModel reqModel) {
        log.info("收到请求开始：[考生入场缺考情况导出][/manager/identityverify/cxtj/ksrcqkxqdc]reqModel:"+reqModel.toString());
        KsrcqkxqdcDTO dto = new KsrcqkxqdcDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setCsbh(reqModel.getCsbh());
        dto.setSfqk(reqModel.getSfqk());
        dto.setKcbh(reqModel.getKcbh());
        dto.setZkzh(reqModel.getZkzh());
        dto.setKsxm(reqModel.getKsxm());
        dto.setZwh(reqModel.getZwh());
        dto.setSfzh(reqModel.getSfzh());
        dto.setBzhkdid(reqModel.getBzhkdid());
        dto.setSbfs(reqModel.getSbfs());
        dto.setKsh(reqModel.getKsh());
        dto.setCslx(reqModel.getCslx());
        dto.setOrderCol(reqModel.getOrderCol());
        dto.setOrderType(reqModel.getOrderType());

        KsrcqkxqdcVO result = ksKsrcxxServiceService.ksrcqkxqdc(dto);
        KsrcqkxqdcRespModel respModel = new KsrcqkxqdcRespModel();
        respModel.setDclj(result.getDclj());
        log.info("处理请求结束：[考生入场缺考情况导出][/manager/identityverify/cxtj/ksrcqkxqdc]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考生入场统计
    * @param reqModel
    * @return
    */
	@Permission("cxtj:ksrctj")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrctj")
    public Wrapper<KsrctjRespModel> ksrctj(@RequestBody KsrctjReqModel reqModel) {
		log.info("收到请求开始：[考生入场统计][/manager/identityverify/cxtj/ksrctj]reqModel:"+reqModel.toString());
		KsrctjDTO dto = new KsrctjDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setCsbh(reqModel.getCsbh());
//        throw new BusinessException("请完成控制器对应的方法");
		KsrctjVO result = ksKsrcxxServiceService.ksrctj(dto);
        KsrctjRespModel respModel = new KsrctjRespModel();
        respModel.setDatas(result.getDatas());
		log.info("处理请求结束：[考生入场统计][/manager/identityverify/cxtj/ksrctj]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 监考上报情况查询
    * @param reqModel
    * @return
    */
	@Permission("cxtj:jksbqkcx")
    @PostMapping(value = "/manager/identityverify/cxtj/jksbqkcx")
    public Wrapper<JksbqkcxRespModel> jksbqkcx(@RequestBody JksbqkcxReqModel reqModel) {
		log.info("收到请求开始：[监考上报情况查询][/manager/identityverify/cxtj/jksbqkcx]reqModel:"+reqModel.toString());
		JksbqkcxDTO dto = new JksbqkcxDTO();
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        dto.setOrderCol(reqModel.getOrderCol());
        dto.setOrderType(reqModel.getOrderType());
        dto.setCslx(reqModel.getCslx());
        dto.setCsbh(reqModel.getCsbh());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setKcbh(reqModel.getKcbh());
        dto.setSbzt(reqModel.getSbzt());
        dto.setSbfs(reqModel.getSbfs());
//        throw new BusinessException("请完成控制器对应的方法");
		JksbqkcxVO result = ksJkryRcxxService.jksbqkcx(dto);
        JksbqkcxRespModel respModel = new JksbqkcxRespModel();
        respModel.setTotalRows(result.getTotalRows());
        respModel.setDatas(result.getDatas());
		log.info("处理请求结束：[监考上报情况查询][/manager/identityverify/cxtj/jksbqkcx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 数据同步记录查询
    * @param reqModel
    * @return
    */
	@Permission("cxtj:sjtbjlCx")
    @PostMapping(value = "/manager/identityverify/cxtj/sjtbjlCx")
    public Wrapper<SjtbjlCxRespModel> sjtbjlCx(@RequestBody SjtbjlCxReqModel reqModel) {
		log.info("收到请求开始：[数据同步记录查询][/manager/identityverify/cxtj/sjtbjlCx]reqModel:"+reqModel.toString());
		SjtbjlCxDTO dto = new SjtbjlCxDTO();
        dto.setSjlx(reqModel.getSjlx());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setTbjg(reqModel.getTbjg());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
		SjtbjlCxVO result = ksProcessEventService.sjtbjlCx(dto);
        SjtbjlCxRespModel respModel = new SjtbjlCxRespModel();
        respModel.setTbjlList(result.getTbjlList());
        respModel.setTotalRow(result.getTotalRow());
		log.info("处理请求结束：[数据同步记录查询][/manager/identityverify/cxtj/sjtbjlCx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 考生入场数据上传列表
    * @param reqModel
    * @return
    */
	@Permission("cxtj:ksrcsjsclb")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrcsjsclb")
    public Wrapper<KsrcsjsclbRespModel> ksrcsjsclb(@RequestBody KsrcsjsclbReqModel reqModel) {
        log.info("收到请求开始：[考生入场数据上传列表][/manager/identityverify/cxtj/ksrcsjsclb]reqModel:" + reqModel.toString());
        KsrcsjsclbDTO dto = new KsrcsjsclbDTO();
        dto.setKsjh(reqModel.getKsjh());
        dto.setKscc(reqModel.getKscc());
        dto.setSbzt(reqModel.getSbzt());
        dto.setXm(reqModel.getXm());
        dto.setZjhm(reqModel.getZjhm());
        KsrcsjsclbVO result = ksKsrcxxServiceService.ksrcsjsclb(dto);
        KsrcsjsclbRespModel respModel = new KsrcsjsclbRespModel();
        respModel.setKsrcsjscList(result.getKsrcsjscList());
        log.info("处理请求结束：[考生入场数据上传列表][/manager/identityverify/cxtj/ksrcsjsclb]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    /**
     * 考务终端下载统计
     * @param reqModel
     * @return
     */
	@Permission("cxtj:kwzdxztj")
    @PostMapping(value = "/manager/identityverify/cxtj/kwzdxztj")
    public Wrapper<KwzdxztjRespModel> kwzdxztj(@RequestBody KwzdxztjReqModel reqModel) {
        logger.info("收到请求开始：[考务终端下载统计][/manager/identityverify/cxtj/kwzdxztj]reqModel:"+reqModel.toString());
        KwzdxztjRespModel respModel = ksKssjDistributeStatusService.kwzdxztj(reqModel);
        logger.info("处理请求结束：[考务终端下载统计][/manager/identityverify/cxtj/kwzdxztj]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    /**
     * 考务终端下载统计导出
     */
    @PostMapping(value = "/manager/identityverify/cxtj/kwzdxztjdc")
    public Wrapper<KwzdxztjdcRespModel> kwzdxztjdc(@RequestBody KwzdxztjReqModel reqModel) {
        logger.info("收到请求开始：[考务终端下载统计导出][/manager/identityverify/cxtj/kwzdxztjdc]reqModel:"+reqModel.toString());
        KwzdxztjdcRespModel respModel = ksKssjDistributeStatusService.kwzdxztjdc(reqModel);
        logger.info("处理请求结束：[考务终端下载统计导出][/manager/identityverify/cxtj/kwzdxztjdc]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 考场零考生入场查询
     * @param reqModel
     * @return
     */
	@Permission("cxtj:ksrcZeroLb")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrcZeroLb")
    public Wrapper<KsrcZeroLbRespModel> ksrcZeroLb(@RequestBody KsrcZeroLbReqModel reqModel) {
        log.info("收到请求开始：[考场零考生入场查询][/manager/identityverify/cxtj/ksrcZeroLb]reqModel:"+reqModel.toString());
        KsrcZeroLbDTO dto = new KsrcZeroLbDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setCsmc(reqModel.getCsmc());
        dto.setWgzt(reqModel.getWgzt());
        dto.setYdzdzt(reqModel.getYdzdzt());
        dto.setErrorType(reqModel.getErrorType());
        dto.setType(reqModel.getType());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        KsrcZeroLbVO result = ksKsrcxxServiceService.ksrcZeroLb(dto);
        KsrcZeroLbRespModel respModel = new KsrcZeroLbRespModel();
        respModel.setZeroKcList(result.getZeroKcList());
        respModel.setTotalRows(result.getTotalRows());
        log.info("处理请求结束：[考场零考生入场查询][/manager/identityverify/cxtj/ksrcZeroLb]reqModel:"+reqModel.toString()
        +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    /**
     * 考场零考生入场通知
     * @param reqModel
     * @return
     */
	@Permission("cxtj:ksrcZeroNotice")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrcZeroNotice")
    public Wrapper<Void> ksrcZeroNotice(@RequestBody KsrcZeroNoticeReqModel reqModel) {
        log.info("收到请求开始：[考场零考生入场通知][/manager/identityverify/cxti/ksrcZeroNotice]reqModel:"+reqModel.toString());
        KsrcZeroNoticeDTO dto = new KsrcZeroNoticeDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setKcidList(reqModel.getKcidList());

        ksKsrcxxServiceService.ksrcZeroNotice(dto);
         log.info("处理请求结束：[考场零考生入场通知][/manager/identityverify/cxti/ksrcZeroNotice]reqModel:"+reqModel.toString());
         return WrapMapper.ok();
    }
    /**
     * 考场入场未报数据列表
     * @param reqModel
     * @return
     */
	@Permission("cxtj:rcwbsjLb")
    @PostMapping(value = "/manager/identityverify/cxtj/rcwbsjLb")
    public Wrapper<RcwbsjLbRespModel> rcwbsjLb(@RequestBody RcwbsjLbReqModel reqModel) {
        log.info("收到请求开始：[考场入场未报数据列表][/manager/identityverify/cxti/rcwbsjLb]reqModel:"+reqModel.toString());
        RcwbsjLbDTO dto = new RcwbsjLbDTO();
        dto.setCcm(reqModel.getCcm());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setSbzt(reqModel.getSbzt());
        dto.setErrorType(reqModel.getErrorType());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        RcwbsjLbVO result = ksKsrcxxServiceService.rcwbsjLb(dto);

        RcwbsjLbRespModel respModel = new RcwbsjLbRespModel();
        respModel.setRcwbsjList(result.getRcwbsjList());
        respModel.setTotalRows(result.getTotalRows());
        log.info("处理请求结束：[考场入场未报数据列表][/manager/identityverify/cxti/rcwbsjLb]reqModel:"+reqModel.toString() +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    /**
     * 标准化考场通知移动终端上报总数
     * @param reqModel
     * @return
     */
	@Permission("cxtj:ydzdsbzsNotice")
    @PostMapping(value = "/manager/identityverify/cxtj/ydzdsbzsNotice")
    public Wrapper<Void> ydzdsbzsNotice(@RequestBody YdzdsbzsNoticeReqModel reqModel) {
        log.info("收到请求开始：[标准化考场通知移动终端上报总数][/manager/identityverify/cxti/ydzdsbzsNotice]reqModel:"+reqModel.toString());
        YdzdsbzsNoticeDTO dto = new YdzdsbzsNoticeDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setSbList(reqModel.getSbList());

        ksKsrcxxServiceService.ydzdsbzsNotice(dto);
        log.info("处理请求结束：[标准化考场通知移动终端上报总数][/manager/identityverify/cxti/ydzdsbzsNotice]reqModel:"+reqModel.toString());
        return WrapMapper.ok();
    }

    /**
     * 考生入场统计逻辑考场
     * @param reqModel
     * @return
     */
	@Permission("cxtj:ksrctjljkc")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrctjljkc")
    public Wrapper<KsrctjljkcRespModel> ksrctjljkc(@RequestBody KsrctjljkcReqModel reqModel) {
        log.info("收到请求开始：[考生入场统计累计考场][/manager/identityverify/cxtj/ksrctjljkc]reqModel:"+reqModel.toString());
        KsrctjljkcDTO dto = new KsrctjljkcDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setCsbh(reqModel.getCsbh());
        KsrctjljkcVO result = ksKsrcxxServiceService.ksrctjljkc(dto);
        KsrctjljkcRespModel respModel = new KsrctjljkcRespModel();
        respModel.setDatas(result.getDatas());
        log.info("处理请求结束：[考生入场统计累计考场][/manager/identityverify/cxtj/ksrctjljkc]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
	@Permission("cxtj:ksrcqkxqcxxq")
    @PostMapping(value = "/manager/identityverify/cxtj/ksrcqkxqcxxq")
    public Wrapper<KsrcqkxqcxxqRespModel> ksrcqkxqcxxq(@RequestBody KsrcqkxqcxxqReqModel reqModel) {
        log.info("收到请求开始：[考生入场缺考详情查询][/manager/identityverify/cxtj/ksrcqkxqcxxq]reqModel:"+reqModel.toString());
        KsrcqkxqcxxqDTO dto = new KsrcqkxqcxxqDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setKsh(reqModel.getKsh());
        KsrcqkxqcxxqVO result = ksKsrcxxServiceService.ksrcqkxqcxxq(dto);

        KsrcqkxqcxxqRespModel respModel = new KsrcqkxqcxxqRespModel();
        respModel.setCjzp(result.getCjzp());
        respModel.setXczp(result.getXczp());
        respModel.setSfzzp(result.getSfzzp());

        log.info("处理请求结束：[考生入场缺考详情查询][/manager/identityverify/cxtj/ksrcqkxqcxxq]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 考务消息查询
     * @param reqModel
     * @return
     */
//    @Permission("cxtj:kwxxcx")
    @PostMapping(value = "/manager/identityverify/cxtj/kwxxcx")
    public Wrapper<KwxxcxRespModel> kwxxcx(@RequestBody KwxxCxReqModel reqModel) {
        log.info("收到请求开始：[考务消息查询][/manager/identityverify/cxtj/kwxxcx]reqModel:" + reqModel.toString());

        // 请求模型转换为DTO
        KwxxCxDTO kwxxCxDTO = new KwxxCxDTO();
        kwxxCxDTO.setKsjhbh(reqModel.getKsjhbh());
        kwxxCxDTO.setFbksrq(reqModel.getFbksrq());
        kwxxCxDTO.setFbjsrq(reqModel.getFbjsrq());
        kwxxCxDTO.setPageNum(reqModel.getPageNum());
        kwxxCxDTO.setPageSize(reqModel.getPageSize());

        // 调用服务层
        PageInfo<KwxxCxVO> pageResult = ksKwMessageService.kwxxcx(kwxxCxDTO);

        // 构建分页响应模型
        KwxxcxRespModel pageRespModel = new KwxxcxRespModel();
        pageRespModel.setData(pageResult.getList());
        pageRespModel.setTotalRows(pageResult.getTotal());

        log.info("处理请求结束：[考务消息查询][/manager/identityverify/cxtj/kwxxcx]reqModel:" + reqModel.toString()
                + ",respModel:" + pageRespModel.toString());
        return WrapMapper.ok(reqModel, pageRespModel);
    }

    /**
     * 已读详情查询
     * @param reqModel
     * @return
     */
//    @Permission("cxtj:kwxxydxq")
    @PostMapping(value = "/manager/identityverify/cxtj/kwxxydxq")
    public Wrapper<KwxxydxqRespModel> kwxxydxq(@RequestBody KwxxydxqReqModel reqModel) {
        log.info("收到请求开始：[已读详情查询][/manager/identityverify/cxtj/kwxxydxq]reqModel:" + reqModel.toString());

        // 请求模型转换为DTO
        KwxxydxqDTO ydxqDTO = new KwxxydxqDTO();
        ydxqDTO.setKsjhbh(reqModel.getKsjhbh());
        ydxqDTO.setMsgId(reqModel.getMsgId());
        ydxqDTO.setZt(reqModel.getZt());
        ydxqDTO.setPageNum(reqModel.getPageNum());
        ydxqDTO.setPageSize(reqModel.getPageSize());

        // 调用服务层
        PageInfo<KwxxydxqVO> pageResult = ksKwMessageService.kwxxydxq(ydxqDTO);

        // 构建分页响应模型
        KwxxydxqRespModel pageRespModel = new KwxxydxqRespModel();
        pageRespModel.setData(pageResult.getList());
        pageRespModel.setTotalRows(pageResult.getTotal());

        log.info("处理请求结束：[已读详情查询][/manager/identityverify/cxtj/kwxxydxq]reqModel:" + reqModel.toString()
                + ",respModel:" + pageRespModel.toString());
        return WrapMapper.ok(reqModel, pageRespModel);
    }

    /**
     * 紧急呼叫查询
     * @param reqModel
     * @return
     */
//    @Permission("cxtj:jjhjcx")
    @PostMapping(value = "/manager/identityverify/cxtj/jjhjcx")
    public Wrapper<JjhjCxResp> jjhjcx(@RequestBody JjhjCxReq reqModel) {
        log.info("收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:" + reqModel.toString());

        // 请求模型转换为DTO
        JjhjCxDTO dto = new JjhjCxDTO();
        BeanUtils.copyProperties(reqModel, dto);

        // 调用服务层
        PageInfo<JjhjCxVO> pageInfo = ksKwMessageService.queryJjhjList(dto);

        // 构建响应模型
        JjhjCxResp resp = new JjhjCxResp();
        resp.setData(pageInfo.getList());
        resp.setTotalRows(pageInfo.getTotal());
        resp.setPageNum(pageInfo.getPageNum());
        resp.setPageSize(pageInfo.getPageSize());

        log.info("处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:" + reqModel.toString()
                + ",respModel:" + resp.toString());
        return WrapMapper.ok(resp);
    }

   /**
    * 考务室报到查询
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/cxtj/kwsbdcx")
    public Wrapper<KwsbdcxRespModel> kwsbdcx(@RequestBody KwsbdcxReqModel reqModel) {
		log.info("收到请求开始：[考务室报到查询][/manager/identityverify/cxtj/kwsbdcx]reqModel:"+reqModel.toString());
		KwsbdcxDTO dto = new KwsbdcxDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setJkxm(reqModel.getJkxm());
        dto.setZt(reqModel.getZt());
        dto.setGwzzm(reqModel.getGwzzm());
		KwsbdcxVO result = ksJkryRcxxService.kwsbdcx(dto);
        KwsbdcxRespModel respModel = new KwsbdcxRespModel();
        respModel.setKwsbdcxList(result.getKwsbdcxList());
		log.info("处理请求结束：[考务室报到查询][/manager/identityverify/cxtj/kwsbdcx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 空考位统计查询
     * @param reqModel
     * @return
     */
//    @Permission("cxtj:kkwtj")
    @PostMapping(value = "/manager/identity/cxtj/kkwtj")
    public Wrapper<KkwTjRespModel> kkwtj(@RequestBody @Validated KkwTjReqModel reqModel) {
        log.info("收到请求开始：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:" + reqModel.toString());

        // 请求模型转换为DTO
        KkwTjDTO dto = new KkwTjDTO();
        BeanUtils.copyProperties(reqModel, dto);

        // 调用服务层
        KkwTjRespModel result = ksKkwMsgService.getKkwTj(dto);

        log.info("处理请求结束：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:" + reqModel.toString()
                + ",respModel:" + result.toString());
        return WrapMapper.ok(result);
    }

    /**
     * 空考位详情页面查询
     * @param reqModel
     * @return
     */
//    @Permission("cxtj:kkwxq")
    @PostMapping(value = "/manager/identity/cxtj/kkwxq")
    public Wrapper<KkwXqRespModel> kkwxq(@RequestBody KkwXqReqModel reqModel) {
        log.info("收到请求开始：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:" + reqModel.toString());

        // 请求模型转换为DTO
        KkwXqDTO dto = new KkwXqDTO();
        BeanUtils.copyProperties(reqModel, dto);

        // 调用服务层
        KkwXqRespModel result = ksKkwMsgService.getKkwTjXq(dto);

        log.info("处理请求结束：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:" + reqModel.toString()
                + ",respModel:" + result.toString());
        return WrapMapper.ok(result);
    }

    /**
     * 空考位详情导出
     * @param reqModel
     * @return
     */
//    @Permission("cxtj:kkwxqdc")
    @PostMapping(value = "/manager/identity/cxtj/kkwxqdc")
    public Wrapper<KkwXqdcRespModel> kkwxqdc(@RequestBody KkwXqReqModel reqModel) {
        log.info("收到请求开始：[空考位详情导出][/manager/identity/cxtj/kkwxqdc]reqModel:" + reqModel.toString());

        // 请求模型转换为DTO
        KkwXqDTO dto = new KkwXqDTO();
        BeanUtils.copyProperties(reqModel, dto);

        // 调用服务层导出Excel
        String filePath = ksKkwMsgService.exportKkwXqExcel(dto);

        // 构建响应模型
        KkwXqdcRespModel respModel = new KkwXqdcRespModel();
        respModel.setDclj(filePath);

        log.info("处理请求结束：[空考位详情导出][/manager/identity/cxtj/kkwxqdc]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(respModel);
    }

}