package com.xcwlkj.identityverify.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.cache.MbCacheOperateService;
import com.xcwlkj.identityverify.mapper.*;
import com.xcwlkj.identityverify.model.domain.*;
import com.xcwlkj.identityverify.model.dos.ImportFileInitDO;
import com.xcwlkj.identityverify.model.dos.JkryxxExcelSheetDO;

import com.xcwlkj.identityverify.model.dos.KsxxExcelSheetDO;
import com.xcwlkj.identityverify.model.dto.ksgl.XxJkxxdrDTO;
import com.xcwlkj.identityverify.model.dto.ksgl.XxKsxxdrDTO;
import com.xcwlkj.identityverify.model.enums.*;
import com.xcwlkj.identityverify.service.XxdrService;
import com.xcwlkj.identityverify.util.CompressUtil;
import com.xcwlkj.identityverify.util.ExcelFormatUtil;
import com.xcwlkj.identityverify.util.ImportColumnUtils;
import com.xcwlkj.identityverify.util.ExcelListener;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学校文件导入
 */
@Slf4j
@Service("XxwjdrService")
public class XxwjdrServiceImpl extends XxdrService {
    @Resource
    private KsJkryJbxxMapper jkryJbxxMapper;
    @Resource
    private KsJkryBpxxMapper jkryBpxxMapper;
    @Resource
    private KsJkryRcxxMapper jkryRcxxMapper;
    @Resource
    private KsKcxxMapper ksKcxxMapper;
    @Resource
    private KsKdxxMapper ksKdxxMapper;
    @Resource
    private KsBmxxMapper ksBmxxMapper;
    @Resource
    private KsBmxxKstzzMapper ksBmxxKstzzMapper;
    @Resource
    private KsBpxxMapper ksBpxxMapper;
    @Resource
    private KsKsrcxxMapper ksKsrcxxMapper;
    @Resource
    private KsKsccMapper ksKsccMapper;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Autowired
    private MbCacheOperateService mbCacheOperateService;
    @Autowired
    private RedisUtil redisUtil;

    private final String gxRygwzzmb = "jy_rygwzzmb";

//    public KsdrqhyVO ksdrqhy(File zipFile) {
//        KsdrqhyVO result = new KsdrqhyVO();
//        File[] unzip = new File[0];
//        try {
//            unzip = CompressUtil.unzip(zipFile, zipFile.getParent(), null);
//        } catch (ZipException e) {
//            e.printStackTrace();
//        }
//
//        Map<String, String> zpNameAndIdMap = new HashMap<>();
//        File excelFile = null;
//        for (File file : unzip) {
//            if (ExcelFormatUtil.isExcel2007(file.getName()) || ExcelFormatUtil.isExcel2003(file.getName())){
//                excelFile = file;
//            }else {
//                UploadVO uploadVO = XcDfsClient.uploadStream(file.getAbsolutePath());
//                String fileName = file.getName().toUpperCase();
//                zpNameAndIdMap.put(fileName, uploadVO.getList().get(0).getId());
//            }
//        }
//
//        List<KsxxExcelSheetDO> ksxxList = EasyExcel.read(excelFile)
//                .head(KsxxExcelSheetDO.class) //对应导入的实体类
//                .sheet() //导入数据的sheet页编号，0代表第一个sheet页，如果不填，则会导入所有sheet页的数据
//                .headRowNumber(2) //列表头行数，1代表列表头有1行，第二行开始为数据行
//                .doReadSync(); //开始读Excel，返回一个List<T>集合，继续后续入库操作
//        for (KsxxExcelSheetDO sheetDO : ksxxList) {
//            if (StringUtils.isBlank(sheetDO.getKmmc())){
//                result.setHyjg("0");
//                return result;
//            }
//        }
//        result.setHyjg("1");
//        return result;
//    }

    @AllArgsConstructor
    public class XxwjdrThread implements Runnable {

        private File zipFile;

        private String ksjhbh;

        private String drlx;

        private String sjlx;

        private String mrkm;

        @Override
        public void run() {
            if ("jkry".equals(sjlx)) {
                jkryExcelImport(zipFile, ksjhbh, drlx);
            }else {
                ksxxExcelImport(zipFile, ksjhbh, drlx, mrkm);
            }
        }
    }

    public void xxwjdr(File zipFile, String ksjhbh, String drlx, String sjlx, String mrkm){
        XxwjdrThread runnable = new XxwjdrThread(zipFile, ksjhbh, drlx, sjlx, mrkm);
        Thread thread = new Thread(runnable);
        thread.start();
    }

    /**
     * 监考信息导入
     * @param zipFile 压缩文件
     * @param ksjhbh 考试计划编号
     */
    public XxJkxxdrDTO jkryExcelImport(File zipFile, String ksjhbh, String drlx) {
        XxJkxxdrDTO result = new XxJkxxdrDTO();
        if(zipFile==null){
            throw new IdentityVerifyException("zip文件为空");
        }else{
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    XcDfsClient.getToken();
                    String redisKey = SubSysEnum.IDENTITYVERIFY.getCode() + ":JKDR:" + ksjhbh;
                    redisUtil.set(redisKey, "text running...", 60*60);
                    // 校级不存在别的考点 直接用考试计划编号获取考点信息
                    KsKdxx ksKdxx = new KsKdxx();
                    Example kdExam = new Example(KsKdxx.class);
                    kdExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                            .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                    List<KsKdxx> kdxxList = ksKdxxMapper.selectByExample(kdExam);
                    if (kdxxList != null && kdxxList.size() != 0){
                        ksKdxx = kdxxList.get(0);
                    }
                    List<ImportTypeEnum> importTypeEnums = new ArrayList<>();
                    importTypeEnums.add(ImportTypeEnum.JKRY);
                    importTypeEnums.add(ImportTypeEnum.JKBP);
                    importTypeEnums.add(ImportTypeEnum.JKZP);
                    // 初始化导入状态
                    KsKssjImportStatus importStatus = importStatusInit(ksjhbh, ImportCatalogEnum.XXWJDR.getCode(), ksKdxx.getBzhkdid(), ksKdxx.getBzhkdmc());
                    // 初始化导入任务信息
                    int tConf = KssjImportTaskTConfEnum.JKRYBP.getValue() + KssjImportTaskTConfEnum.JKRYJB.getValue() +
                            KssjImportTaskTConfEnum.JKRYZP.getValue();
                    KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.XXWJDR.getCode(), tConf);
                    // 初始化导入文件信息
                    for (ImportTypeEnum importTypeEnum : importTypeEnums) {
                        ImportFileInitDO fileInitDO = new ImportFileInitDO();
                        fileInitDO.setFileName(zipFile.getName());
                        fileInitDO.setFilePath(zipFile.getAbsolutePath());
                        fileInitDO.setKsjhbh(ksjhbh);
                        fileInitDO.setImportType(importTypeEnum);
                        fileInitDO.setImportCatalog(ImportCatalogEnum.XXWJDR.getCode());
                        fileInitDO.setBzhkdid(ksKdxx.getBzhkdid());
                        fileInitDO.setBzhkdmc(ksKdxx.getBzhkdmc());
                        KsKssjImportFile importFile = importFileInit(fileInitDO);
                        switch (importTypeEnum){
                            case JKRY:
                                importStatus.setJkryjbsjbQk(SjbQkEnum.FINISH.getValue());
                                importStatus.setFileJkryjbsjbId(importFile.getId());
                                break;
                            case JKBP:
                                importStatus.setJkrybpsjbQk(SjbQkEnum.FINISH.getValue());
                                importStatus.setFileJkrybpsjbId(importFile.getId());
                                break;
                            case JKZP:
                                importStatus.setJkryzpsjbQk(SjbQkEnum.FINISH.getValue());
                                importStatus.setFileJkryzpsjbId(importFile.getId());
                                break;
                        }
                    }
                    importStatusUpdate(importStatus);
                    KsProcessEvent event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.BJJKXX);
                    event.setBzhkdid(ksKdxx.getBzhkdid());
                    event.setBzhkdmc(ksKdxx.getBzhkdmc());
                    event.setParam3(importTask.getId());
                    try {
                        File[] unzip = CompressUtil.unzip(zipFile, zipFile.getParent(), null);

                        Map<String, String> zpNameAndIdMap = new HashMap<>();
                        File excelFile = null;
                        for (File file : unzip) {
                            if (ExcelFormatUtil.isExcel2007(file.getName()) || ExcelFormatUtil.isExcel2003(file.getName())){
                                excelFile = file;
                            }else {
                                UploadVO uploadVO = XcDfsClient.uploadStream(file.getAbsolutePath());
                                String fileName = file.getName().toUpperCase();
                                zpNameAndIdMap.put(fileName, uploadVO.getList().get(0).getId());
                            }
                        }
                        List<Map<Object, Object>> rygwzzmbList = mbCacheOperateService.queryMbsjList(gxRygwzzmb);
                        Map<String,String> rygwzzmbMap = Maps.newHashMap();
                        rygwzzmbList.forEach(entity->{
                            rygwzzmbMap.put(entity.get("MC").toString(), entity.get("DM").toString());
                        });
                        Example kskcxxExam = new Example(KsKcxx.class);
                        kskcxxExam.createCriteria().andEqualTo("ksjhbh",ksjhbh).andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
                        List<KsKcxx> kcList = new ArrayList<>();
                        if ("0".equals(drlx)) {
                            kcList = ksKcxxMapper.selectByExample(kskcxxExam);
                        } else{
                            ksKcxxMapper.deleteByExample(kskcxxExam);
                        }

                        Map<String ,KsJkryJbxx> bsmAndJbxxMap = new HashMap<>();
                        List<KsJkryBpxx> bpxxList = Lists.newArrayList();
                        List<KsJkryRcxx> rcxxList = Lists.newArrayList();
                        List<String> jkryzjhm = Lists.newArrayList();

                        ExcelListener listener = new ExcelListener();
                        EasyExcel.read(excelFile, JkryxxExcelSheetDO.class, listener).sheet().headRowNumber(2).doRead();
                        Map<Integer, String> headMap = listener.getHeadMap();
                        String[] columns = ImportColumnUtils.jkxxExcelColumns;
                        for (Map.Entry<Integer, String> value : headMap.entrySet()) {
                            if (!columns[value.getKey()].equals(value.getValue())){
                                throw new IdentityVerifyException("Excel表头字段不匹配。。");
                            }
                        }
                        List<ReadSheet> readSheets = EasyExcel.read(excelFile)
                                .build()
                                .excelExecutor()
                                .sheetList();
                        Integer ccsl = 0;
                        Integer jksl = 0;
                        for (ReadSheet readSheet : readSheets) {
                            List<JkryxxExcelSheetDO> jkrylist = EasyExcel.read(excelFile)
                                    .head(JkryxxExcelSheetDO.class) //对应导入的实体类
                                    .sheet(readSheet.getSheetNo()) //导入数据的sheet页编号，0代表第一个sheet页，如果不填，则会导入所有sheet页的数据
                                    .headRowNumber(2) //列表头行数，1代表列表头有1行，第二行开始为数据行
                                    .doReadSync(); //开始读Excel，返回一个List<T>集合，继续后续入库操作
                            String ccm = readSheet.getSheetName().replaceAll(" ", "");
                            Example ksccEx = new Example(KsKscc.class);
                            ksccEx.createCriteria().andEqualTo("ccm", ccm)
                                            .andEqualTo("ksjhbh",ksjhbh)
                                                    .andEqualTo("scztw",ScztEnum.NOTDEL.getCode());
                            List<KsKscc> ksccList = ksKsccMapper.selectByExample(ksccEx);
                            if (ksccList == null || ksccList.size() ==0){
                                throw new IdentityVerifyException(ccm + " : 考试场次码对应场次不存在");
                            }
                            log.debug("监考人员信息为[{}]",jkrylist.toString());
                            for (JkryxxExcelSheetDO entity : jkrylist) {
                                if (jkryzjhm.contains(entity.getZjhm())){
                                    throw new IdentityVerifyException("场次-" + ccm + " 身份证号-" + entity.getZjhm() + " : 身份证号重复存在。");
                                }
                                if (StringUtils.isNotBlank(entity.getZp())) {
                                    String zpId = zpNameAndIdMap.get(entity.getZp().replaceAll(" ", "").toUpperCase());
                                    entity.setZp(zpId);
                                }
                                // 去除空格
                                entity.setJkxm(entity.getJkxm().replaceAll(" ", ""));
                                if (StringUtils.isNotBlank(entity.getGw())) {
                                    entity.setGw(entity.getGw().replaceAll(" ", ""));
                                } else {
                                    throw new IdentityVerifyException(entity.getJkxm() + "：缺少岗位。");
                                }
                                if (StringUtils.isBlank(rygwzzmbMap.get(entity.getGw()))){
                                    throw new IdentityVerifyException(entity.getJkxm() + "：该岗位不存在。");
                                }
                                if (StringUtils.isNotBlank(entity.getKcbh())) {
                                    entity.setKcbh(entity.getKcbh().replaceAll(" ", ""));
                                } else if(rygwzzmbMap.get(entity.getGw()).equals("2001")){
                                    throw new IdentityVerifyException(entity.getJkxm() + "：缺少考场编号。");
                                }
                                // 复合考场编号获取
                                List<String> kcbhs = new ArrayList<>();
                                if (StringUtils.isNotBlank(entity.getKcbh()) && entity.getKcbh().contains(",")){
                                    String[] split = entity.getKcbh().split(",");
                                    for (String s : split) {
                                        kcbhs.add(s);
                                    }
                                } else {
                                    kcbhs.add(entity.getKcbh());
                                }
                                entity.setZjhm(entity.getZjhm().replaceAll(" ", "").toUpperCase());
                                if (entity.getZjhm().length() != 18){
                                    throw new IdentityVerifyException(entity.getJkxm() + "：身份证格式错误。");
                                }
                                KsJkryJbxx jbxx = formatJkJbxx(entity, ksjhbh, ksKdxx.getKdbh());
                                bsmAndJbxxMap.put(jbxx.getZjh(), jbxx);
                                for (String kcbh : kcbhs) {
                                    entity.setKcbh(kcbh);
                                    bpxxList.add(formatJkBpxx(ksjhbh,entity,kcList,rygwzzmbMap,ksKdxx,ccm));
                                    rcxxList.add(formatJkRcxx(ksjhbh,entity,kcList,rygwzzmbMap,ksKdxx,ccm));
                                }
                                jkryzjhm.add(entity.getZjhm());
                                jksl++;
                            }
                            ccsl++;
                            // 导入类型 1-全量覆盖 0-追加修改 不填默认全量
                            if ("0".equals(drlx)) {
                                jkryzjhm = jkryzjhm.stream().distinct().collect(Collectors.toList());
                                Example exampleBp = new Example(KsJkryBpxx.class);
                                exampleBp.createCriteria().andEqualTo("ksjhdm", ksjhbh).andIn("grbsm", jkryzjhm).andEqualTo("ccm", ccm);
                                jkryBpxxMapper.deleteByExample(exampleBp);

                                Example exampleRc = new Example(KsJkryRcxx.class);
                                exampleRc.createCriteria().andEqualTo("ksjhdm", ksjhbh).andIn("grbsm", jkryzjhm).andEqualTo("ccm", ccm);
                                jkryRcxxMapper.deleteByExample(exampleRc);
                                jkryzjhm.clear();
                            }else {
                                Example exampleBp = new Example(KsJkryBpxx.class);
                                exampleBp.createCriteria().andEqualTo("ksjhdm", ksjhbh).andEqualTo("ccm", ccm);
                                jkryBpxxMapper.deleteByExample(exampleBp);

                                Example exampleRc = new Example(KsJkryRcxx.class);
                                exampleRc.createCriteria().andEqualTo("ksjhdm", ksjhbh).andEqualTo("ccm", ccm);
                                jkryRcxxMapper.deleteByExample(exampleRc);
                                jkryzjhm.clear();
                            }
                        }

                        result.setCcdrsl(ccsl);
                        result.setJkdrsl(jksl);
                        log.info(bpxxList.toString());

                        Example example = new Example(KsJkryJbxx.class);
                        Example.Criteria criteria = example.createCriteria().andEqualTo("ksjhdm", ksjhbh);
                        if ("0".equals(drlx)) {
                            criteria.andIn("zjh", bsmAndJbxxMap.keySet());
                        }
                        jkryJbxxMapper.deleteByExample(example);

                        // 删除临时文件
                        FileUtils.deleteQuietly(unzip[0].getParentFile());

                        jkryJbxxMapper.insertListSelective(new ArrayList<>(bsmAndJbxxMap.values()));
                        jkryBpxxMapper.insertListSelective(bpxxList);
                        jkryRcxxMapper.insertListSelective(rcxxList);

                        importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                        importTask.setCompleteTime(new Date());

                        event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                        event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                        event.setEventDesc(DataSynchTypeEnum.BJJKXX.getTblxmc() + "导入成功");
                        event.setEventDescDetail(DataSynchTypeEnum.BJJKXX.getTblxmc() + "导入成功");
                        event.setParam4(String.valueOf(jksl));
                        event.setEventHandleTime(new Date());
                    } catch (ZipException e) {
                        importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                        importTask.setCompleteTime(new Date());
                        kssjImportException(ksjhbh,e,importTask.getId(),importTask.getTConf());
                        event.setEventType(EventTypeSjtbEnum.LQSB.getCode());
                        event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                        event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                        event.setEventDescDetail(EventTypeSjtbEnum.LQSB.getDesc()+":"+e.getMessage());
                        e.printStackTrace();
                    } catch (IdentityVerifyException e) {
                        importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                        importTask.setCompleteTime(new Date());
                        kssjImportException(ksjhbh,e,importTask.getId(),importTask.getTConf());
                        event.setEventType(EventTypeSjtbEnum.JYCW.getCode());
                        event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                        event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                        event.setEventDescDetail(EventTypeSjtbEnum.JYCW.getDesc()+":"+e.getMessage());
                        e.printStackTrace();
                    } catch (Exception e){
                        importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                        importTask.setCompleteTime(new Date());
                        kssjImportException(ksjhbh,e,importTask.getId(),importTask.getTConf());
                        event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                        event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                        event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                        event.setEventDescDetail(EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                        e.printStackTrace();
                    }finally {
                        importTaskUpdate(importTask);
                        updateEvent(event);
                        redisUtil.del(redisKey);
                    }
                    initPkgStatus(ksjhbh, "jk");
                    initDistributeStatus(ksjhbh);
                }
            });
            //特征值暂时不处理
//            updateJkzpxx(zpDir,ksjhbh);
        }
        return result;
    }

    /**
     * 考生信息导入
     * @param zipFile 压缩文件
     * @param ksjhbh 考试计划编号
     */
    public XxKsxxdrDTO ksxxExcelImport(File zipFile, String ksjhbh, String drlx, String mrkm) {
        XxKsxxdrDTO result = new XxKsxxdrDTO();
        if(zipFile==null){
            throw new IdentityVerifyException("zip文件为空");
        }else{
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    XcDfsClient.getToken();
                    String redisKey = SubSysEnum.IDENTITYVERIFY.getCode() + ":KSDR:" + ksjhbh;
                    redisUtil.set(redisKey, "text running...", 60*60);
                    String kszpPath = zipFile.getParent() + File.separator + "kszp";
                    // 获取考试计划
                    KsKsjh ksKsjh = ksKsjhMapper.selectByPrimaryKey(ksjhbh);
                    // 校级不存在别的考点 直接用考试计划编号获取考点信息
                    KsKdxx ksKdxx = new KsKdxx();
                    Example kdExam = new Example(KsKdxx.class);
                    kdExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                            .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                    List<KsKdxx> kdxxList = ksKdxxMapper.selectByExample(kdExam);
                    if (kdxxList != null && kdxxList.size() != 0){
                        ksKdxx = kdxxList.get(0);
                    }
                    List<ImportTypeEnum> importTypeEnums = new ArrayList<>();
                    importTypeEnums.add(ImportTypeEnum.KS);
                    importTypeEnums.add(ImportTypeEnum.KSZP);
                    // 初始化导入状态
                    KsKssjImportStatus importStatus = importStatusInit(ksjhbh, ImportCatalogEnum.XXWJDR.getCode(), ksKdxx.getBzhkdid(), ksKdxx.getBzhkdmc());
                    // 初始化导入任务信息
                    int tConf = KssjImportTaskTConfEnum.KS.getValue() + KssjImportTaskTConfEnum.KSZP.getValue();
                    KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.XXWJDR.getCode(), tConf);
                    // 初始化导入文件信息
                    for (ImportTypeEnum importTypeEnum : importTypeEnums) {
                        ImportFileInitDO fileInitDO = new ImportFileInitDO();
                        fileInitDO.setFileName(zipFile.getName());
                        fileInitDO.setFilePath(zipFile.getAbsolutePath());
                        fileInitDO.setKsjhbh(ksjhbh);
                        fileInitDO.setImportType(importTypeEnum);
                        fileInitDO.setImportCatalog(ImportCatalogEnum.XXWJDR.getCode());
                        fileInitDO.setBzhkdid(ksKdxx.getBzhkdid());
                        fileInitDO.setBzhkdmc(ksKdxx.getBzhkdmc());
                        KsKssjImportFile importFile = importFileInit(fileInitDO);
                        switch (importTypeEnum){
                            case KS:
                                importStatus.setKssjbQk(SjbQkEnum.FINISH.getValue());
                                importStatus.setFileKssjbId(importFile.getId());
                                break;
                            case KSZP:
                                importStatus.setKszpsjbQk(SjbQkEnum.FINISH.getValue());
                                importStatus.setFileKszpsjbId(importFile.getId());
                                break;
                        }
                    }
                    importStatusUpdate(importStatus);
                    KsProcessEvent event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.BJKSXX);
                    event.setBzhkdid(ksKdxx.getBzhkdid());
                    event.setBzhkdmc(ksKdxx.getBzhkdmc());
                    event.setParam3(importTask.getId());
                    try {
                        // 上传照片到文件服务器
                        File[] unzip = CompressUtil.unzip(zipFile, zipFile.getParent(), null);
                        File kszpDir = new File(kszpPath);
                        if (kszpDir.exists()){
                            FileUtils.deleteDirectory(kszpDir);
                        } else {
                            kszpDir.mkdirs();
                        }
                        Map<String, String> zpNameAndIdMap = new HashMap<>();
                        File excelFile = null;
                        for (File file : unzip) {
                            if (ExcelFormatUtil.isExcel2007(file.getName()) || ExcelFormatUtil.isExcel2003(file.getName())){
                                excelFile = file;
                            }else {
                                File destKszp = new File(kszpPath + File.separator + file.getName());
                                UploadVO uploadVO = XcDfsClient.uploadStream(file.getAbsolutePath());
                                String fileName = file.getName().toUpperCase();
                                zpNameAndIdMap.put(fileName, uploadVO.getList().get(0).getId());
                                FileUtils.copyFile(file, destKszp);
                            }
                        }
                        Example kskcxxExam = new Example(KsKcxx.class);
                        kskcxxExam.createCriteria().andEqualTo("ksjhbh",ksjhbh).andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
                        List<KsKcxx> kcList = new ArrayList<>();
                        if ("0".equals(drlx)) {
                            kcList = ksKcxxMapper.selectByExample(kskcxxExam);
                        } else{
                            ksKcxxMapper.deleteByExample(kskcxxExam);
                        }

                        List<KsBpxx> bpxxList = Lists.newArrayList();
                        List<KsKsrcxx> rcxxList = Lists.newArrayList();
                        List<String> ksZjhmList = Lists.newArrayList();
                        Map<String, KsBmxxKstzz> kstzzMap = new HashMap<>();
                        Map<String, String> sfzhTzzidMap = new HashMap<>();
                        Map<String, KsBmxx> kshAndBmxxMap = new HashMap<>();

                        ExcelListener listener = new ExcelListener();
                        EasyExcel.read(excelFile, KsxxExcelSheetDO.class, listener).sheet().headRowNumber(2).doRead();
                        Map<Integer, String> headMap = listener.getHeadMap();
                        String[] columns = ImportColumnUtils.ksxxExcelColumns;
                        for (Map.Entry<Integer, String> value : headMap.entrySet()) {
                            if (!columns[value.getKey()].equals(value.getValue())){
                                throw new IdentityVerifyException("Excel表头字段不匹配。。");
                            }
                        }
                        // 特征值启用时上传特征值
                        if ((ksKsjh.getKzqy() & KzqyEnum.TZZQY.getValue()) != 0) {
                            String tzzResultPath = sckstzz(kszpPath);
                            String tzzFilePath = zipFile.getParent() + File.separator + ksjhbh + File.separator + ksKdxx.getKdbh() + File.separator + DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT) + File.separator;
                            File tzzFile = new File(tzzFilePath);
                            tzzFile.mkdirs();
                            if (tzzResultPath != null) {
                                insertKstzzCreatorException(importTask, tzzResultPath + "exception" + File.separator + zpName, ksjhbh, ksKdxx);
                                FileUtils.copyDirectory(new File(tzzResultPath + "feature" + File.separator + zpName), tzzFile);
                            }
                            File[] tzzFiles = tzzFile.listFiles();
                            if (tzzFiles != null) {
                                for (File file : tzzFiles) {
                                    UploadVO vo = XcDfsClient.uploadStream(file.getAbsolutePath());
                                    sfzhTzzidMap.put(file.getName().substring(0, file.getName().lastIndexOf(".")), vo.getList().get(0).getId());
                                }
                            }
                            if (tzzFile.exists()){
                                FileUtils.deleteDirectory(tzzFile);
                            }
                            if (kszpDir.exists()){
                                FileUtils.deleteDirectory(kszpDir);
                            }
                        }
                        List<ReadSheet> readSheets = EasyExcel.read(excelFile)
                                .build()
                                .excelExecutor()
                                .sheetList();
                        Integer ccsl = 0;
                        Integer kssl = 0;
                        for (ReadSheet readSheet : readSheets) {
                            List<KsxxExcelSheetDO> ksxxList = EasyExcel.read(excelFile)
                                    .head(KsxxExcelSheetDO.class) //对应导入的实体类
                                    .sheet(readSheet.getSheetNo()) //导入数据的sheet页编号，0代表第一个sheet页，如果不填，则会导入所有sheet页的数据
                                    .headRowNumber(2) //列表头行数，1代表列表头有1行，第二行开始为数据行
                                    .doReadSync(); //开始读Excel，返回一个List<T>集合，继续后续入库操作
                            log.debug("考生信息为[{}]",ksxxList.toString());
                            String ccm = readSheet.getSheetName().replaceAll(" ", "");

                            Example ksccEx = new Example(KsKscc.class);
                            ksccEx.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                                    .andEqualTo("ccm", ccm)
                                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                            ksccEx.orderBy("kssj").asc();
                            // 场次科目标志 true-单场次单科目 false-单场次多科目
                            boolean cckmFlag = true;
                            List<KsKscc> ksccList = ksKsccMapper.selectByExample(ksccEx);
                            if (ksccList == null || ksccList.size() ==0){
                                throw new IdentityVerifyException(ccm + " : 考试场次码对应场次不存在");
                            }

                            Map<String, String> kmmcAndKmmMap = ksccList.stream().collect(Collectors.toMap(KsKscc::getKmmc, KsKscc::getKmm));

                            // 单场次多科目
                            if (ksccList.size() > 1){
                                cckmFlag = false;
                            }
                            for(KsxxExcelSheetDO entity : ksxxList){
                                if (StringUtils.isBlank(entity.getKmmc())){
                                    if (cckmFlag || "1".equals(mrkm)){
                                        KsKscc kscc = ksccList.get(0);
                                        entity.setKmmc(kscc.getKmmc());
                                    } else {
                                        IdentityVerifyException exception = new IdentityVerifyException(ccm + " ：当前场次有多个科目，科目名称不能为空。");
                                        event.setEventType(EventTypeSjtbEnum.KMQS.getCode());
                                        event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                                        event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                                        event.setEventDescDetail(EventTypeSjtbEnum.KMQS.getDesc()+":"+exception.getMessage());
                                        throw exception;
                                    }
                                }
                                if (StringUtils.isNotBlank(entity.getZp())) {
                                    String zpId = zpNameAndIdMap.get(entity.getZp().replaceAll(" ", "").toUpperCase());
                                    entity.setZp(zpId);
                                }
                                // 去空格
                                entity.setKsxm(entity.getKsxm().replaceAll(" ", ""));
                                if (StringUtils.isNotBlank(entity.getZkzh())) {
                                    entity.setZkzh(entity.getZkzh().replaceAll(" ", ""));
                                }else {
                                    throw new IdentityVerifyException(entity.getKsxm() + "：缺少准考证号。");
                                }
                                if (StringUtils.isNotBlank(entity.getZwh())) {
                                    entity.setZwh(entity.getZwh().replaceAll(" ", ""));
                                }else {
                                    throw new IdentityVerifyException(entity.getKsxm() + "：缺少座位号。");
                                }
                                if (StringUtils.isNotBlank(entity.getKcbh())) {
                                    entity.setKcbh(entity.getKcbh().replaceAll(" ", ""));
                                }else {
                                    throw new IdentityVerifyException(entity.getKsxm() + "：缺少考场编号。");
                                }
                                entity.setZjhm(entity.getZjhm().replaceAll(" ", "").toUpperCase());
                                if (entity.getZjhm().length() != 18){
                                    throw new IdentityVerifyException(entity.getKsxm() + "：身份证格式错误。");
                                }
                                KsBmxx bmxx = formatKsBmxx(entity, ksjhbh);
                                kshAndBmxxMap.put(bmxx.getKsh(), bmxx);
                                if ((ksKsjh.getKzqy() & KzqyEnum.TZZQY.getValue()) != 0) {
                                    kstzzMap.put(entity.getZjhm(), formatKsBmxxTzz(entity, ksjhbh, sfzhTzzidMap.get(entity.getZjhm())));
                                }
                                bpxxList.add(formatKsBpxx(ksjhbh, entity, kcList, ksKdxx, ccm, kmmcAndKmmMap));
                                rcxxList.add(formatKsRcxx(ksjhbh, entity, kcList, ksKdxx, ccm, kmmcAndKmmMap));
                                ksZjhmList.add(entity.getZjhm());
                                kssl++;
                            }
                            ccsl++;
                            // 导入类型 1-全量覆盖 0-追加修改 不填默认全量
                            if ("0".equals(drlx)) {
                                ksZjhmList = ksZjhmList.stream().distinct().collect(Collectors.toList());
                                Example exampleBp = new Example(KsBpxx.class);
                                exampleBp.createCriteria().andEqualTo("ksjhbh", ksjhbh).andIn("sfzjhm", ksZjhmList).andEqualTo("ccm", ccm);
                                ksBpxxMapper.deleteByExample(exampleBp);

                                Example exampleRc = new Example(KsKsrcxx.class);
                                exampleRc.createCriteria().andEqualTo("ksjhbh", ksjhbh).andIn("sfzh", ksZjhmList).andEqualTo("ccm", ccm);
                                ksKsrcxxMapper.deleteByExample(exampleRc);
                                ksZjhmList.clear();
                            } else {
                                Example exampleBp = new Example(KsBpxx.class);
                                exampleBp.createCriteria().andEqualTo("ksjhbh", ksjhbh).andEqualTo("ccm", ccm);
                                ksBpxxMapper.deleteByExample(exampleBp);

                                Example exampleRc = new Example(KsKsrcxx.class);
                                exampleRc.createCriteria().andEqualTo("ksjhbh", ksjhbh).andEqualTo("ccm", ccm);
                                ksKsrcxxMapper.deleteByExample(exampleRc);
                            }
                        }
                        log.info(bpxxList.toString());

                        Example example = new Example(KsBmxx.class);
                        Example.Criteria criteria = example.createCriteria().andEqualTo("ksjhid", ksjhbh);
                        if ("0".equals(drlx)){
                            criteria.andIn("ksh", kshAndBmxxMap.keySet());
                        }
                        Example tzzExample = new Example(KsBmxxKstzz.class);
                        Example.Criteria criteria1 = tzzExample.createCriteria().andEqualTo("ksjhbh", ksjhbh);
                        if ("0".equals(drlx)){
                            criteria1.andIn("ksh", kshAndBmxxMap.keySet());
                        }
                        ksBmxxMapper.deleteByExample(example);
                        if (!kstzzMap.isEmpty()) {
                            ksBmxxKstzzMapper.deleteByExample(tzzExample);
                            ksBmxxKstzzMapper.insertListSelective(new ArrayList<>(kstzzMap.values()));
                        }
                        ksBmxxMapper.insertListSelective(new ArrayList<>(kshAndBmxxMap.values()));
                        ksBpxxMapper.insertListSelective(bpxxList);
                        ksKsrcxxMapper.insertListSelective(rcxxList);

                        result.setKsdrsl(kssl);
                        result.setCcdrsl(ccsl);

                        // 删除临时文件
                        FileUtils.deleteQuietly(unzip[0].getParentFile());

                        importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                        importTask.setCompleteTime(new Date());

                        event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                        event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                        event.setEventDesc(DataSynchTypeEnum.BJKSXX.getTblxmc() + "导入成功");
                        event.setEventDescDetail(DataSynchTypeEnum.BJKSXX.getTblxmc() + "导入成功");
                        event.setParam4(String.valueOf(kssl));
                        event.setEventHandleTime(new Date());
                    } catch (ZipException e) {
                        importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                        importTask.setCompleteTime(new Date());
                        kssjImportException(ksjhbh,e,importTask.getId(),importTask.getTConf());
                        event.setEventType(EventTypeSjtbEnum.LQSB.getCode());
                        event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                        event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                        event.setEventDescDetail(EventTypeSjtbEnum.LQSB.getDesc()+":"+e.getMessage());
                        e.printStackTrace();
                    } catch (IdentityVerifyException e) {
                        importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                        importTask.setCompleteTime(new Date());
                        kssjImportException(ksjhbh,e,importTask.getId(),importTask.getTConf());
                        if (EventHandleEnum.DEFAULT.getCode().equals(event.getEventHandle())){
                            event.setEventType(EventTypeSjtbEnum.JYCW.getCode());
                            event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                            event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                            event.setEventDescDetail(EventTypeSjtbEnum.JYCW.getDesc() + ":" + e.getMessage());
                        }
                        e.printStackTrace();
                    }  catch (Exception e){
                        importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                        importTask.setCompleteTime(new Date());
                        kssjImportException(ksjhbh,e,importTask.getId(),importTask.getTConf());
                        event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                        event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                        event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                        event.setEventDescDetail(EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                        e.printStackTrace();
                    }finally {
                        importTaskUpdate(importTask);
                        updateEvent(event);
                        redisUtil.del(redisKey);
                    }
                    initPkgStatus(ksjhbh, "ks");
                    initDistributeStatus(ksjhbh);
                }
            });
            //特征值暂时不处理
//            updateJkzpxx(zpDir,ksjhbh);
        }
        return  result;
    }

    private KsKsrcxx formatKsRcxx(String ksjhbh, KsxxExcelSheetDO entity, List<KsKcxx> kcList, KsKdxx kdxx, String ccm, Map<String, String> kmmcAndKmmMap) {
        List<KsKcxx> collect = kcList.stream().filter(s -> StringUtils.equals(s.getKcbh(), entity.getKcbh()) && StringUtils.equals(s.getCcm(), ccm)).collect(Collectors.toList());
        KsKcxx kcxx = collect.get(0);
        KsKsrcxx ksrcxx = new KsKsrcxx();
        Date date = new Date();
        ksrcxx.setKsrcbh(IdGenerateUtil.generateId());
        ksrcxx.setKsjhbh(ksjhbh);
        ksrcxx.setCcm(ccm);
        ksrcxx.setZkzh(entity.getZkzh());
        ksrcxx.setSfrc("9");
        ksrcxx.setBzhkdid(kdxx.getBzhkdid());
        ksrcxx.setBzhkcid(kcxx.getBzhkcid());
        ksrcxx.setScztw(ScztEnum.NOTDEL.getCode());
        ksrcxx.setCreateTime(date);
        ksrcxx.setUpdateTime(date);
        ksrcxx.setKdbh(kdxx.getKdbh());
        ksrcxx.setKdmc(kdxx.getKdmc());
        ksrcxx.setKcbh(kcxx.getKcbh());
        ksrcxx.setKcmc(kcxx.getKcmc());
        ksrcxx.setKsxm(entity.getKsxm());
        ksrcxx.setZwh(entity.getZwh());
        ksrcxx.setZwhPx(Integer.valueOf(entity.getZwh()));
        ksrcxx.setKmmc(entity.getKmmc());
        ksrcxx.setKmdm(kmmcAndKmmMap.get(entity.getKmmc()));
        ksrcxx.setSfzh(entity.getZjhm());
        ksrcxx.setKsh(entity.getZjhm());
        if (StringUtils.isNotBlank(kcxx.getLjkcbh())) {
            ksrcxx.setLjkcbh(kcxx.getLjkcbh());
        }else {
            ksrcxx.setLjkcbh(kcxx.getKcbh());
        }
        return ksrcxx;
    }

    private KsBpxx formatKsBpxx(String ksjhbh, KsxxExcelSheetDO entity, List<KsKcxx> kcList, KsKdxx kdxx, String ccm, Map<String, String> kmmcAndKmmMap) {
        List<KsKcxx> collect = kcList.stream().filter(s -> StringUtils.equals(s.getKcbh(), entity.getKcbh()) && StringUtils.equals(s.getCcm(), ccm)).collect(Collectors.toList());
        if (collect.isEmpty()){
            String kcbh = entity.getKcbh();
            KsKcxx kcxx = new KsKcxx();
            kcxx.setBzhkdid(kdxx.getBzhkdid());
            kcxx.setKdbh(kdxx.getKdbh());
            kcxx.setKcmc(kcbh);
            kcxx.setKcbh(kcbh);
            kcxx.setId(IdGenerateUtil.generateId());
            kcxx.setSczt(ScztEnum.NOTDEL.getCode());
            kcxx.setKsjhbh(ksjhbh);
            kcxx.setLjkcbh(entity.getKcbh());
            kcxx.setCcm(ccm);
            kcxx.setUpdateTime(DateUtil.getCurrentDT());
            kcxx.setCreateTime(DateUtil.getCurrentDT());
            kcxx.setSfwbykc(CslxEnum.KC.getCode());
            ksKcxxMapper.insert(kcxx);
            collect.add(kcxx);
            kcList.add(kcxx);
        }
        KsKcxx kcxx = collect.get(0);
        KsBpxx ksBpxx = new KsBpxx();
        Date date = new Date();
        ksBpxx.setKsbpbh(IdGenerateUtil.generateId());
        ksBpxx.setKsjhbh(ksjhbh);
        ksBpxx.setCcm(ccm);
        if (StringUtils.isNotBlank(kmmcAndKmmMap.get(entity.getKmmc()))) {
            ksBpxx.setKmm(kmmcAndKmmMap.get(entity.getKmmc()));
        }else {
            throw new IdentityVerifyException(entity.getKmmc() + " : 科目不存在");
        }
        ksBpxx.setKmmc(entity.getKmmc());
        ksBpxx.setKsh(entity.getZjhm());
        ksBpxx.setZkzh(entity.getZkzh());
        ksBpxx.setKdbh(kdxx.getKdbh());
        ksBpxx.setKcbh(kcxx.getKcbh());
        ksBpxx.setZwh(entity.getZwh());
        ksBpxx.setZwhPx(Integer.valueOf(entity.getZwh()));
        ksBpxx.setBzhkdbid(kdxx.getBzhkdid());
        ksBpxx.setBzhkcbid(kcxx.getBzhkcid());
        ksBpxx.setScztw(ScztEnum.NOTDEL.getCode());
        ksBpxx.setCreateTime(date);
        ksBpxx.setUpdateTime(date);
        if (StringUtils.isNotBlank(kcxx.getLjkcbh())) {
            ksBpxx.setLjkcbh(kcxx.getLjkcbh());
        }else {
            ksBpxx.setLjkcbh(kcxx.getKcbh());
        }
        ksBpxx.setSfzjhm(entity.getZjhm());
        return ksBpxx;
    }

    private KsBmxx formatKsBmxx(KsxxExcelSheetDO entity, String ksjhbh) {
        KsBmxx ksBmxx = new KsBmxx();
        Date date = new Date();
        ksBmxx.setKsid(IdGenerateUtil.generateId());
        ksBmxx.setKsjhid(ksjhbh);
        ksBmxx.setKsh(entity.getZjhm());
        ksBmxx.setXm(entity.getKsxm());
        ksBmxx.setXb(getXbByZjh(entity.getZjhm()));
        ksBmxx.setXbm(XbEnum.getKey(getXbByZjh(entity.getZjhm())));
        ksBmxx.setSfzjlxm("1");
        ksBmxx.setSfzjlx("居民身份证");
        ksBmxx.setSfzjhm(entity.getZjhm());
        if (StringUtils.isNotBlank(entity.getZp())) {
            ksBmxx.setZp(entity.getZp());
        }
        ksBmxx.setScztw(ScztEnum.NOTDEL.getCode());
        ksBmxx.setCreateTime(date);
        ksBmxx.setUpdateTime(date);
        return ksBmxx;
    }

    private KsBmxxKstzz formatKsBmxxTzz(KsxxExcelSheetDO entity, String ksjhbh, String tzzid) {
        KsBmxxKstzz ksBmxxKstzz = new KsBmxxKstzz();

        ksBmxxKstzz.setKsid(IdGenerateUtil.generateId());
        ksBmxxKstzz.setKsjhbh(ksjhbh);
        ksBmxxKstzz.setKsh(entity.getZjhm());
        ksBmxxKstzz.setSfzjhm(entity.getZjhm());
        ksBmxxKstzz.setKszpid(entity.getZp());
        if (StringUtils.isNotBlank(tzzid)) {
            ksBmxxKstzz.setTzzzt("1");
            ksBmxxKstzz.setTzzid(tzzid);
        } else {
            ksBmxxKstzz.setTzzzt("0");
        }
        //特征值方式：1-身份证验证、2-人脸验证、3-指纹验证、4-虹膜验证、5-手部静脉纹验证、9-其他生物特征验证
        ksBmxxKstzz.setTzzfs("2");
        ksBmxxKstzz.setCreateTime(new Date());
        ksBmxxKstzz.setUpdateTime(new Date());
        return ksBmxxKstzz;
    }

    private KsJkryRcxx formatJkRcxx(String ksjhbh, JkryxxExcelSheetDO entity, List<KsKcxx> kcxxList, Map<String, String> rygwzzmbMap, KsKdxx kdxx, String ccm) {
        List<KsKcxx> collect = kcxxList.stream().filter(s -> StringUtils.equals(entity.getKcbh(), s.getKcbh()) && StringUtils.equals(s.getCcm(), ccm)).collect(Collectors.toList());
        KsKcxx kcxx = collect.get(0);
        KsJkryRcxx rcxx = new KsJkryRcxx();
        Date date = new Date();
        rcxx.setJkryrcid(IdGenerateUtil.generateId());
        rcxx.setKsjhdm(ksjhbh);
        rcxx.setGrbsm(entity.getZjhm());
        rcxx.setGwzzm(rygwzzmbMap.get(entity.getGw()));
        rcxx.setKdbh(kdxx.getKdbh());
        rcxx.setCcm(ccm);
        if (rygwzzmbMap.get(entity.getGw()).equals("2001")){
            rcxx.setKcbh(kcxx.getKcbh());
        } else {
            rcxx.setKcbh(null);
        }
        rcxx.setBzhkcid(kcxx.getBzhkcid());
        rcxx.setBzhkdid(kdxx.getBzhkdid());
        rcxx.setCreateTime(date);
        rcxx.setUpdateTime(date);
        rcxx.setZjh(entity.getZjhm());
        rcxx.setXm(entity.getJkxm());
        rcxx.setXb(getXbByZjh(entity.getZjhm()));
        rcxx.setSfrc("9");
        return rcxx;
    }

    private KsJkryBpxx formatJkBpxx(String ksjhbh, JkryxxExcelSheetDO entity, List<KsKcxx> kcxxList, Map<String, String> rygwzzmbMap, KsKdxx ksKdxx, String ccm) {
        List<KsKcxx> collect = kcxxList.stream().filter(s -> StringUtils.equals(entity.getKcbh(), s.getKcbh()) && StringUtils.equals(s.getCcm(), ccm)).collect(Collectors.toList());
        if (collect.size() == 0 || collect == null){
            String kcbh = entity.getKcbh();
            KsKcxx kcxx = new KsKcxx();
            kcxx.setBzhkdid(ksKdxx.getBzhkdid());
            kcxx.setKdbh(ksKdxx.getKdbh());
            kcxx.setKcmc(kcbh);
            kcxx.setKcbh(kcbh);
            kcxx.setId(IdGenerateUtil.generateId());
            kcxx.setKsjhbh(ksjhbh);
            kcxx.setSczt(ScztEnum.NOTDEL.getCode());
            kcxx.setCcm(ccm);
            kcxx.setLjkcbh(entity.getKcbh());
            kcxx.setUpdateTime(DateUtil.getCurrentDT());
            kcxx.setCreateTime(DateUtil.getCurrentDT());
            kcxx.setSfwbykc(CslxEnum.KC.getCode());
            if (rygwzzmbMap.get(entity.getGw()).equals("2001")) {
                ksKcxxMapper.insert(kcxx);
            }
            collect.add(kcxx);
            kcxxList.add(kcxx);
        }
        KsKcxx kcxx = collect.get(0);
        KsJkryBpxx bpxx = new KsJkryBpxx();
        Date date = new Date();
        bpxx.setJkrybpbzid(IdGenerateUtil.generateId());
        bpxx.setKsjhdm(ksjhbh);
        bpxx.setGrbsm(entity.getZjhm());
        bpxx.setGwzzm(rygwzzmbMap.get(entity.getGw()));
        bpxx.setCcm(ccm);
        if (rygwzzmbMap.get(entity.getGw()).equals("2001")){
            bpxx.setKcbh(kcxx.getKcbh());
        } else {
            bpxx.setKcbh(null);
        }
        bpxx.setBzhkcid(kcxx.getBzhkcid());
        bpxx.setKdbh(ksKdxx.getKdbh());
        bpxx.setBzhkdid(ksKdxx.getBzhkdid());
        bpxx.setCreateTime(date);
        bpxx.setUpdateTime(date);
        return bpxx;
    }

    private KsJkryJbxx formatJkJbxx(JkryxxExcelSheetDO entity, String ksjhbh, String kdbh) {
        KsJkryJbxx jbxx = new KsJkryJbxx();
        Date date = new Date();
        jbxx.setJkrybzid(IdGenerateUtil.generateId());
        jbxx.setGrbsm(entity.getZjhm());
        jbxx.setZjh(entity.getZjhm());
        jbxx.setXb(getXbByZjh(entity.getZjhm()));
        jbxx.setXm(entity.getJkxm());
        jbxx.setXbm(XbEnum.getKey(getXbByZjh(entity.getZjhm())));
        jbxx.setGwlxm("01");
        jbxx.setGwmc("考点工作人员");
        jbxx.setKsjhdm(ksjhbh);
        if (StringUtils.isNotBlank(entity.getZp())) {
            jbxx.setZp(entity.getZp());
        }
        jbxx.setCreateTime(date);
        jbxx.setUpdateTime(date);
        jbxx.setKddm(kdbh);
        return jbxx;
    }

    private boolean ccmHy(String ccm){
        for (int i = ccm.length();--i>=0;){
            if (!Character.isDigit(ccm.charAt(i))){
                return false;
            }
        }
        return true;
    }

    private String getXbByZjh(String zjh){
        String xb = "";
        char c = zjh.charAt(16);
        if (c % 2 == 0){
            xb = "女";
        }else {
            xb = "男";
        }
        return xb;
    }

}
