package com.xcwlkj.identityverify.service.impl.scheduleTask;

import com.xcwlkj.identityverify.service.ITaskService;
import com.xcwlkj.identityverify.service.SbSbxxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 设备固件版本更新
 */
@Service("deviceVersionUpdateTaskService")
public class DeviceVersionUpdateTaskServiceImpl implements ITaskService {

    @Resource
    private SbSbxxService sbSbxxService;

    @Override
    public void run(String params) {
        sbSbxxService.deviceVersionUpdate();
    }
}
