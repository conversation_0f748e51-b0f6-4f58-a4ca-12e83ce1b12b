package com.xcwlkj.identityverify.service.impl.mqttHandler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xcwlkj.identityverify.service.KsKssjDistributeTaskService;
import com.xcwlkj.identityverify.service.impl.SjptdrServiceImpl;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttFuncInvokeEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttUnifyAccessEventHandlerEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.FunctionInvokeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
@Slf4j
public class MqttKssjdrEventHandler extends MqttBaseEventHandler {

    @Resource
    private SjptdrServiceImpl sjptdrService;
    @Resource
    private KsKssjDistributeTaskService kssjDistributeTaskService;

    @Override
    public void handleFuncInvoke(String productId, String deviceId, MqttFuncInvokeEnum funcInvokeEnum, String msg) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            log.info("开始导入上级平台下发数据[{}]",msg);
            long st = System.currentTimeMillis();
            FunctionInvokeVO functionInvokeVO = objectMapper.readValue(msg, FunctionInvokeVO.class);
            sjptdrService.ksjhdr(functionInvokeVO);
            long cost = System.currentTimeMillis()-st;
            log.info("完成导入上级平台下发数据,耗时[{}]ms",cost);
            sjptdrService.sbkdxzzt(functionInvokeVO);
            log.info("数据同步-数据下发任务新建");
            long start = System.currentTimeMillis();
            String ksbh = functionInvokeVO.getInputs().get(0).getValue().getKsbh();
            kssjDistributeTaskService.sjxfrwxj(ksbh);
            long time = System.currentTimeMillis()-start;
            log.info("完成数据下发任务新建,耗时[{}]ms",time);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
    }

    @Override
    protected HanleResult doHanleEvent(String productId, Object req, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr) {
        return null;
    }

    @Override
    protected MqttUnifyAccessEventHandlerEnum getEventHandlerType() {
        return MqttUnifyAccessEventHandlerEnum.KSSJDR_EVENT_HANDLER;
    }
}
