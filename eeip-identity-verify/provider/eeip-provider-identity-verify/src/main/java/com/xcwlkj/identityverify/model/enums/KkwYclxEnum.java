package com.xcwlkj.identityverify.model.enums;

/**
 * 空考位异常描述 1-误识别 ，2-坐错他人位置 ，3-实际未参加考试， 4-他人坐错位置， 5-人工核验， 6-缺考（空位），7-无编排
 */
public enum KkwYclxEnum {
    WSB("1", "误识别"),
    ZCTRWZ("2", "坐错他人位置"),
    SJWCKS("3", "实际未参加考试"),
    TZWZ("4", "他人坐错位置"),
    RGHY("5", "人工核验"),
    QK("6", "缺考（空位）"),
    WBP("7", "无编排");

    private final String code;
    private final String description;

    KkwYclxEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
    public static String getDescriptionByCode(String code) {
        for (KkwYclxEnum value : KkwYclxEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDescription();
            }
        }
        return null;
    }
}
