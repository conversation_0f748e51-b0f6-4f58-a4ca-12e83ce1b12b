package com.xcwlkj.identityverify.model.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "biz_fsgj_task")
public class BizFsgjTask implements Serializable {

    /**  */
    @Id
    @Column(name = "id")
    private String            id;
    /** 任务名称 */
    @Column(name = "name")
    private String            name;
    /** 任务类型 1-移动终端APP */
    @Column(name = "t_type")
    private String            type;
    /** 进度 */
    @Column(name = "t_progress")
    private String            progress;
    /** 完成状态 -1-失败 0-开始 1-进行中 2-完成 */
    @Column(name = "complete")
    private Integer            complete;
    /** 任务描述 */
    @Column(name = "t_progress_desc")
    private String            progressDesc;
    /** 任务参数 */
    @Column(name = "t_param")
    private String param;
}
