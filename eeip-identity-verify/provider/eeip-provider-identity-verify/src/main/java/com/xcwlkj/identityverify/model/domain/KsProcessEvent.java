/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 考试事件记录
 * 
 * <AUTHOR>
 * @version $Id: KsProcessEvent.java, v 0.1 2023年09月19日 17时18分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "ks_process_event")
public class KsProcessEvent implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** ID */
    @Id
    @Column(name = "id")
    private String            id;
    /** 考试计划编号 */
    @Column(name = "ksjhbh")
    private String            ksjhbh;
    /** 场次码 */
    @Column(name = "ccm")
    private String            ccm;
    /** 标准化考点id */
    @Column(name = "bzhkdid")
    private String            bzhkdid;
    /** 标准化考点名称 */
    @Column(name = "bzhkdmc")
    private String            bzhkdmc;
    /** 1-成功 -11xx-数据同步错误  -12xx-数据打包错误 -13xx-数据下发错误 -14xx数据上报错误 */
    @Column(name = "event_type")
    private String            eventType;
    /** SBTB-数据同步 SJDB-数据打包 SJXF-数据下发 KS_{ccm}-考试 */
    @Column(name = "event_period")
    private String            eventPeriod;
    /** 事件描述 */
    @Column(name = "event_desc")
    private String            eventDesc;
    /** 处理  0-默认状态 1-未处理  2-已处理 */
    @Column(name = "event_handle")
    private String            eventHandle;
    /** SBTB-数据同步 SJDB-数据打包 SJXF 数据下发 SBZXJC-设备在线检测 SBWSB-设备未上报 */
    @Column(name = "event_catalog")
    private String            eventCatalog;
    /** 标准化考场ID */
    @Column(name = "bzhkcid")
    private String            bzhkcid;
    /** 考场号 */
    @Column(name = "kch")
    private String            kch;
    /** 考场名称 */
    @Column(name = "kcmc")
    private String            kcmc;
    /** 标准化考场名称 */
    @Column(name = "bzhkcmc")
    private String            bzhkcmc;
    /** 教室号 */
    @Column(name = "jsh")
    private String            jsh;
    /** 事件描述详情 */
    @Column(name = "event_desc_detail")
    private String            eventDescDetail;
    /** 事件处理完成时间 */
    @Column(name = "event_handle_time")
    private Date            eventHandleTime;
    /** 同步数据类型 */
    @Column(name = "tbsjlx")
    private String            tbsjlx;
    /** 参数1 */
    @Column(name = "param1")
    private String            param1;
    /** 参数2 */
    @Column(name = "param2")
    private String            param2;
    /** 参数3 */
    @Column(name = "param3")
    private String            param3;
    /** 参数4 */
    @Column(name = "param4")
    private String            param4;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;
    /** 删除状态 0-正常  1-删除 */
    @Column(name = "sczt")
    private String            sczt;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


