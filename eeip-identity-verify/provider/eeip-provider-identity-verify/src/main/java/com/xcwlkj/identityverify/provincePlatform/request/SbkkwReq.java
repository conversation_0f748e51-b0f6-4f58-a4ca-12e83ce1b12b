package com.xcwlkj.identityverify.provincePlatform.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 空考位上报请求类
 */
@Data
public class EmptySeatReportReq extends BaseRequest implements Serializable {

    /**
     * 加密的JSON字符串，使用固定秘钥hssfhy@2025~$#@!
     * 解密后的JSON结构包含：
     * - dataArray: 数据数组
     *   - examPlanCode: 考试编号
     *   - orgCode: 机构编号
     *   - sn: 设备序列号
     *   - devType: 设备类型（172）
     *   - ccm: 场次码
     *   - bzhkcid: 标准化考场id
     *   - zcqswzm: 座次起始位置码
     *   - zwbjfsm: 座位布局方式码
     *   - zwplfsm: 座位排列方式码
     *   - kwlb: 考位列表
     * - czsj: 操作时间yyyy-MM-dd HH:mm:ss
     * - timestamp: 时间戳
     */
    private String encrptJson;

    /**
     * 设备序列号
     */
    private String sbxlh;
}
