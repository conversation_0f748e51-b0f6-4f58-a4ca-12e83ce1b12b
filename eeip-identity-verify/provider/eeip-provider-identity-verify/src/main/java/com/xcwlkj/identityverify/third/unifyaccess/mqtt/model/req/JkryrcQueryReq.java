package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class JkryrcQueryReq extends BaseOperationReq<JkryrcQueryReq> {

    //考试计划
    private String ksjh;
    //考试场次
    private String kscc;
    //设备序列号,上报的具体设备序列号
    private String sbxlh;
    //设备类型
    private String sblx;
    //岗位职责码,具体见码表,填空查询全部
    private String gwzzm;
    //1-已报到 9-未报到 ,填空查询全部
    private String rczt;
    //1-手工 0-非手工 ,填空查询全部
    private String rgzt;
    //搜索参数身份证号码或考场号
    private String sscs;
}
