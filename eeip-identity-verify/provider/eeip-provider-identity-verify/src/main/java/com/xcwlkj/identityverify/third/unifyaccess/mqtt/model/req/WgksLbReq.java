package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WgksLbReq extends BaseOperationReq<WgksLbReq> {

    /**
     * 考试计划
     */
    private String ksjh;
    /**
     * 考试场次 可以不填，不填就查该考试下所有
     */
    private String kscc;
    /**
     * 逻辑考场编号(考点内唯一)，可以不填，不填就查该考试下所有，可以多个用逗号间隔
     */
    private String ljkcbh;
    /**
     * 处理状态 0-未处理 1-已处理 可以不填，不填就查该考试下所有
     */
    private String clzt;
    /**
     * 上报状态 0-未上报 1-已上报 可以不填，不填就查该考试下所有
     */
    private String sbzt;
    /**
     * 设备序列号 上报的具体设备序列号
     */
    private String sbxlh;
    /**
     * 设备类型
     */
    private String sblx;

}
