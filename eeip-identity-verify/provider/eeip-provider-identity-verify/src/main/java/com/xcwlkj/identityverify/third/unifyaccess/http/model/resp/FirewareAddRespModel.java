package com.xcwlkj.identityverify.third.unifyaccess.http.model.resp;

import lombok.Data;

import java.util.List;

@Data
public class FirewareAddRespModel {

    private String id;
    //产品ID
    private String productId;
    //产品名称
    private String productName;
    //固件名称
    private String name;
    //版本号
    private String version;
    //版本序号
    private Integer versionOrder;
    //固件文件地址
    private String url;
    //固件文件签名
    private String sign;
    //固件文件签名方式,如:MD5,SHA256
    private String signMethod;
    //固件文件大小
    private Integer size;
    //创建时间(只读)
    private String createTime;
    //其他拓展信息
    private List<Propertie> properties;

    @Data
    static class Propertie {
        private String id;
        private String name;
        private String value;
    }
}
