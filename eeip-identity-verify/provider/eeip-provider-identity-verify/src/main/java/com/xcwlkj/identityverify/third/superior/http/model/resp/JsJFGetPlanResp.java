package com.xcwlkj.identityverify.third.superior.http.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class JsJFGetPlanResp extends JsJFBaseDataResp {
    /**
     * 考试编号
     */
    @JsonProperty("KSBH")
    private String KSBH;

    /**
     * 考试名称
     */
    @JsonProperty("KSMC")
    private String KSMC;

    /**
     * 开始日期
     */
    @JsonProperty("KSKSRQ")
    private String KSKSRQ;

    /**
     * 结束日期
     */
    @JsonProperty("KSJSRQ")
    private String KSJSRQ;

    @JsonProperty("CC")
    private List<JsJFCcResp> CC;
}
