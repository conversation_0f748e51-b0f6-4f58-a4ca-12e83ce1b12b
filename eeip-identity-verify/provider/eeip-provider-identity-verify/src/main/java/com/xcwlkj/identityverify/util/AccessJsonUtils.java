package com.xcwlkj.identityverify.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 接入 Json 生成工具
 */
public class AccessJsonUtils {

    public static JSONObject generateAccessJson(String functionId, Object dataJson){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("functionId", functionId);
        jsonObject.put("messageType", "INVOKE_FUNCTION");

        JSONArray inputs = new JSONArray();
        JSONObject data = new JSONObject();
        data.put("name", "Data");
        if(dataJson!=null){
            data.put("value", JSONObject.parseObject(JSON.toJSONString(dataJson, SerializerFeature.WriteNullStringAsEmpty)));
        }else{
            data.put("value",new JSONObject());
        }
        inputs.add(data);
        jsonObject.put("inputs", inputs);
        return jsonObject;
    }

    public static JSONObject generateAccessJsonV2(String functionId, JSONObject dataJson){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("OPERATION", functionId);
        jsonObject.put("TIMESTAMP", System.currentTimeMillis()/1000);
        jsonObject.put("Data", dataJson);
        return jsonObject;
    }
}
