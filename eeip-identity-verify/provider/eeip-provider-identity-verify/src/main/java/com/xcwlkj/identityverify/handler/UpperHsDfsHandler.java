package com.xcwlkj.identityverify.handler;

import com.xcwlkj.dfs.exceptions.DfsSdkBusiException;
import com.xcwlkj.dfs.model.vo.*;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.util.UpperHsDfsClient;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;

/**
 * 上级平台DFS文件处理器
 * 负责处理与上级平台DFS相关的文件操作
 */
@Slf4j
@Component
public class UpperHsDfsHandler {

    @Resource
    private JySysDictService jySysDictService;
    @Value("${xc.xcDfs.prefixUrl}")
    private String dfsServer;
    @Value("${xc.temp.generatePath}")
    private String tempGeneratePath;
    @PostConstruct
    public void init() {
        log.info("开始检查平台类型和初始化上级平台DFS客户端...");

        // 检查平台类型
        try {
            JySysDict defaultPlatDict = jySysDictService.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT);
            String platType = defaultPlatDict != null ? defaultPlatDict.getTValue() : null;

            if (StringUtil.isBlank(platType)) {
                log.warn("未配置默认平台类型，跳过上级平台DFS客户端初始化");
                return;
            }

            if (!SuperiorPlatEnum.HISOME.getCode().equals(platType)) {
                log.info("当前平台类型为：{}，非HISOME平台，跳过上级平台DFS客户端初始化", platType);
                return;
            }

            log.info("当前平台类型为：{}，开始初始化上级平台DFS客户端", platType);

            // 只有HISOME平台才进行DFS初始化
            boolean success = initializeUpperHsDfsClient();
            if (success) {
                log.info("上级平台DFS客户端自动初始化完成");
            } else {
                log.error("上级平台DFS客户端自动初始化失败");
            }

        } catch (Exception e) {
            log.error("检查平台类型或初始化上级平台DFS客户端时发生异常", e);
        }
    }

    /**
     * 初始化上级平台DFS客户端
     * @return 是否成功
     */
    public boolean initializeUpperHsDfsClient() {
        try {
            // 读取DFS配置
            JySysDict fileServerUrlDict = jySysDictService.queryConfigValueByKey("HISOME_fileServerUrl");
            JySysDict fileServerChannelDict = jySysDictService.queryConfigValueByKey("HISOME_fileServerChannel");
            JySysDict fileServiceAppIdDict = jySysDictService.queryConfigValueByKey("HISOME_fileServiceAppId");
            JySysDict fileServiceAppSecretDict = jySysDictService.queryConfigValueByKey("HISOME_fileServiceAppSecret");

            // 提取配置值
            String serverUrl = fileServerUrlDict != null ? fileServerUrlDict.getTValue() : null;
            String channel = fileServerChannelDict != null ? fileServerChannelDict.getTValue() : null;
            String appId = fileServiceAppIdDict != null ? fileServiceAppIdDict.getTValue() : null;
            String appSecret = fileServiceAppSecretDict != null ? fileServiceAppSecretDict.getTValue() : null;

            // 验证配置完整性
            if (StringUtil.isBlank(serverUrl) || StringUtil.isBlank(channel) ||
                StringUtil.isBlank(appId) || StringUtil.isBlank(appSecret)) {
                log.error("上级平台DFS配置不完整：serverUrl={}, channel={}, appId={}, appSecret={}",
                        serverUrl, channel, appId, StringUtil.isBlank(appSecret) ? "空" : "已配置");
                return false;
            }

            // 初始化UpperHsDfsClient
            UpperHsDfsClient.init(serverUrl, channel, appId, appSecret);

            // 预先获取token
            String token = UpperHsDfsClient.getToken();
            if (StringUtil.isBlank(token)) {
                log.error("获取上级平台DFS token失败");
                return false;
            }

            log.info("上级平台DFS客户端初始化成功");
            return true;

        } catch (Exception e) {
            log.error("初始化上级平台DFS客户端异常", e);
            return false;
        }
    }

    /**
     * 上传单个照片到上级平台
     * @param localFileId 本地DFS文件ID
     * @param fileName 文件名
     * @return upperFileId 上级平台文件ID
     */
    public String uploadPhotoToUpperPlatform(String localFileId, String fileName) {
        String tempFilePath = null;
        try {
            // 1. 从本地DFS下载文件
            TimePathVO timePathVO = XcDfsClient.timePath(20L, 0, localFileId);
            if (timePathVO == null || timePathVO.getFileList() == null || timePathVO.getFileList().isEmpty()) {
                log.error("获取本地DFS文件临时路径失败，文件ID：{}", localFileId);
                return null;
            }

            FileItemVO fileItemVO = timePathVO.getFileList().get(0);
            String fileUrl = fileItemVO.getUrl();

            // 2. 下载文件到本地临时目录
            tempFilePath = downloadFileToTemp(fileUrl, fileName, localFileId);
            if (StringUtil.isBlank(tempFilePath)) {
                log.error("下载文件到本地临时目录失败，文件ID：{}，URL：{}", localFileId, fileUrl);
                return null;
            }

            // 3. 上传文件到上级平台DFS
            return uploadFileWithAutoTokenRefresh(localFileId, fileName, tempFilePath);

        } catch (Exception e) {
            log.error("上传照片到上级平台异常，文件ID：{}", localFileId, e);
            return null;
        } finally {
            // 5. 确保删除临时文件
            cleanupTempFile(tempFilePath);
        }
    }

    /**
     * 下载文件到临时目录
     * @param fileUrl 文件URL
     * @param fileName 文件名
     * @param localFileId 本地文件ID
     * @return 临时文件路径
     */
    private String downloadFileToTemp(String fileUrl, String fileName, String localFileId) {
        try {
            // 创建临时目录
            String tempFileName = "temp_" + localFileId + "_" + System.currentTimeMillis() + "_" + fileName;
            String tempFilePath = tempGeneratePath + File.separator + tempFileName;

            File tempFile = new File(tempFilePath);

            // 确保父目录存在
            tempFile.getParentFile().mkdirs();

            // 下载文件
            DownloadVO result = XcDfsClient.download(fileUrl);
            FileUtils.writeByteArrayToFile(tempFile, result.getContent());

            log.debug("文件下载到临时目录成功，文件ID：{}，临时路径：{}", localFileId, tempFilePath);
            return tempFilePath;

        } catch (Exception e) {
            log.error("下载文件到临时目录失败，文件ID：{}，URL：{}", localFileId, fileUrl, e);
            return null;
        }
    }

    /**
     * 带自动token刷新的文件上传方法
     * @param localFileId 本地文件ID
     * @param fileName 文件名
     * @param tempFilePath 临时文件路径
     * @return 上级平台文件ID
     */
    private String uploadFileWithAutoTokenRefresh(String localFileId, String fileName, String tempFilePath) {
        try {
            // 上传文件 - 使用FileInputStream读取临时文件
            UploadVO uploadResult;
            try (FileInputStream fileInputStream = new FileInputStream(tempFilePath)) {
                uploadResult = UpperHsDfsClient.uploadStream(fileName, fileInputStream, 0, "private");
            }

            if (uploadResult != null && uploadResult.getList() != null && !uploadResult.getList().isEmpty()) {
                UploadItemVO uploadItem = uploadResult.getList().get(0);
                String upperFileId = uploadItem.getId();
                log.debug("照片上传成功，本地文件ID：{}，上级平台文件ID：{}", localFileId, upperFileId);
                return upperFileId;
            } else {
                log.error("上传照片到上级平台失败，响应为空，文件ID：{}", localFileId);
                return null;
            }

        } catch (DfsSdkBusiException e) {
            String errorMsg = e.getMsg() != null ? e.getMsg() : e.getMessage();

            // 检查是否为403错误（token过期）
            if (e.getCode() == 403) {
                log.warn("上传文件时token已过期，自动刷新token并重试，文件ID：{}，错误信息：{}", localFileId, errorMsg);

                try {
                    // 刷新token
                    initializeUpperHsDfsClient();
                    String newToken = UpperHsDfsClient.getToken();
                    if (StringUtil.isBlank(newToken)) {
                        log.error("刷新token失败，文件ID：{}", localFileId);
                        return null;
                    }

                    log.info("token刷新成功，重新上传文件，文件ID：{}", localFileId);

                    // 使用新token重新上传 - 同样使用FileInputStream读取临时文件
                    UploadVO retryResult;
                    try (FileInputStream fileInputStream = new FileInputStream(tempFilePath)) {
                        retryResult = UpperHsDfsClient.uploadStream(fileName, fileInputStream, 0, "private");
                    }

                    if (retryResult != null && retryResult.getList() != null && !retryResult.getList().isEmpty()) {
                        UploadItemVO uploadItem = retryResult.getList().get(0);
                        String upperFileId = uploadItem.getId();
                        log.debug("照片重试上传成功，本地文件ID：{}，上级平台文件ID：{}", localFileId, upperFileId);
                        return upperFileId;
                    } else {
                        log.error("重试上传照片到上级平台失败，响应为空，文件ID：{}", localFileId);
                        return null;
                    }

                } catch (Exception retryException) {
                    log.error("重试上传照片到上级平台异常，文件ID：{}", localFileId, retryException);
                    return null;
                }
            } else {
                log.error("上传照片到上级平台失败，错误码：{}，错误信息：{}，文件ID：{}", e.getCode(), errorMsg, localFileId);
                return null;
            }

        } catch (Exception e) {
            log.error("上传照片到上级平台异常，文件ID：{}", localFileId, e);
            return null;
        }
    }

    /**
     * 清理临时文件
     * @param tempFilePath 临时文件路径
     */
    private void cleanupTempFile(String tempFilePath) {
        if (StringUtil.isBlank(tempFilePath)) {
            return;
        }

        try {
            File tempFile = new File(tempFilePath);
            if (tempFile.exists()) {
                if (tempFile.delete()) {
                    log.debug("临时文件删除成功：{}", tempFilePath);
                } else {
                    log.warn("临时文件删除失败：{}", tempFilePath);
                }
            }
        } catch (Exception e) {
            log.error("删除临时文件异常：{}", tempFilePath, e);
        }
    }

}
