package com.xcwlkj.identityverify.third.unifyaccess.http.service;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.JetlinksWrapper;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.req.*;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.resp.*;
import com.xcwlkj.msgque.model.domain.RespBaseModel;
import com.xcwlkj.util.wrapper.Wrapper;

import java.io.File;
import java.util.List;

/**
 * 对接统一接入客户端
 * <AUTHOR>
 *
 */
public interface UnifyAccessService {
	
	/**
	 * 平台登录
	 * @param login
	 * @return
	 */
	Wrapper<PlatLoginRespModel> platLogin(PlatLoginReqModel login);
	/**
	 * 设备token验证
	 * @param deviceValidate
	 * @return
	 */
	Wrapper<RemoteRespBaseModel> deviceValidate(DeviceValidateReqModel deviceValidate);
	/**
	 * 命令下发
	 * @param reqModel
	 * @return
	 */
	Wrapper<RespBaseModel> sendCommand(SendCommandReqModel reqModel);
	/**
	 * 批次命令下发
	 * @param reqModel
	 * @return
	 */
	Wrapper<RemoteRespBaseModel> batchCommand(BatchCommandReqModel reqModel);
	/**
	 * 批次命令下发结果查询
	 * @param msgId
	 * @return
	 */
	Wrapper<BatchCommandResultRespModel> queryBatchCommandResult(String msgId);
	/**
	 * 根据设备序列号数组查询设备状态
	 * @param devNames
	 * @return
	 */
	Wrapper<DeviceStatusRespModel> queryDeviceStatus(String... devNames);
	/**
	 * 设备令牌验证
	 * @param token
	 * @return
	 */
	boolean deviceValidate(String token);
	
	/**
	 * 命令下发
	 * @param sn 设备序列号
	 * @param data 下发内容
	 * @return
	 */
	Wrapper<RespBaseModel> sendCommand(String sn, String data);


	JetlinksWrapper<JetlinksLoginRespModel> jetlinksLogin(JetlinksLoginReqModel reqModel);

	JetlinksWrapper<JetlinksPageRespModel<JetlinksDeviceFirewareRespModel>> jetlinksQueryPageDeviceFireware(JetlinksDeviceFirewareReqModel reqModel);

	/**
	 * 固件升级状态查询
	 * @param taskIds
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	JetlinksWrapper<JetlinksPageRespModel<DeviceUpGradeStatusRespModel>> jetlinksQueryDeviceFirewareUpgrade(List<String> taskIds, Integer pageNum, Integer pageSize);

	JetlinksWrapper<JetlinksPageRespModel<DeviceProductRespModel>> queryPageDeviceProduct(DeviceProductReqModel reqModel);

	JetlinksWrapper<JetlinksPageRespModel<DeviceFirewareRespModel>> queryPageDeviceFireware(DeviceFirewareReqModel reqModel);

	JetlinksWrapper<FirmwareUpgradeTaskRespModel> createFirmwareUpgradeTask(FirmwareUpgradeTaskReqModel reqModel);

	JetlinksWrapper<Object> publishUpgradeTaskToDevice(PublishUpgradeTaskToDeviceReqModel reqModel, String taskId);

	JetlinksWrapper<Object> pushUpgradeByTaskId(String taskId);

	JetlinksWrapper<JetlinksDeviceInfoRespModel> getDeviceInstance(String sn);

	/**
	 *Emqx消息桥接添加订阅
	 * @param topic
	 * @return
	 */
	Wrapper<EmqxSubscribersAddRespModel> emqxSubscribersAdd(String... topic);
	/**
	 *Emqx消息桥接删除订阅
	 * @param topic
	 * @return
	 */
	Wrapper<EmqxSubscribersDelRespModel> emqxSubscribersDel(String... topic);
	/**
	 *Emqx消息桥接查询订阅
	 * @return
	 */
	Wrapper<EmqxSubscribersListRespModel> emqxSubscribersList();

//	/**
//	 *Emqx消息桥接添加订阅
//	 * @param topic
//	 * @return
//	 */
//	Wrapper<EmqxSubscribersAddRespModel> emqxSubscribersAddByDevSn(String... topic);


	JetlinksWrapper<List<DeviceCategoryRespModel>> deviceCategory();

	JetlinksWrapper<String> fileUpload(File file);

	JetlinksWrapper<FirewareAddRespModel> firewareAdd(FirewareAddReqModel reqModel);
}
