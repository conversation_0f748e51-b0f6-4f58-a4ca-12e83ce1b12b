/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.mapper.KsKssjPkgTaskCsMapper;
import com.xcwlkj.identityverify.model.domain.KsKssjPkgTask;
import com.xcwlkj.identityverify.model.domain.KsKssjPkgTaskCs;
import com.xcwlkj.identityverify.model.dto.sjdb.DataExportFileDownLoadDTO;
import com.xcwlkj.identityverify.model.dto.sjdb.DataExportLbDTO;
import com.xcwlkj.identityverify.model.dto.sjdb.DataExportScDTO;
import com.xcwlkj.identityverify.model.dto.sjdb.DataExportXqDTO;
import com.xcwlkj.identityverify.model.enums.PkgTaskTypeEnum;
import com.xcwlkj.identityverify.model.enums.PkgTaskCompleteEnum;
import com.xcwlkj.identityverify.model.vo.sjdb.DataExportFileDownLoadVO;
import com.xcwlkj.identityverify.model.vo.sjdb.DataExportLbVO;
import com.xcwlkj.identityverify.model.vo.sjdb.DataExportXqVO;
import com.xcwlkj.identityverify.model.vo.sjdb.ExportTaskItemVO;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.IdGenerateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.mapper.KsKssjPkgTaskMapper;
import com.xcwlkj.identityverify.service.KsKssjPkgTaskService;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 考试数据打包任务服务
 * <AUTHOR>
 * @version $Id: KsKssjPkgTaskServiceImpl.java, v 0.1 2023年09月19日 17时17分 xcwlkj.com Exp $
 */
@Service("ksKssjPkgTaskService")
public class KsKssjPkgTaskServiceImpl extends BaseServiceImpl<KsKssjPkgTaskMapper, KsKssjPkgTask> implements KsKssjPkgTaskService  {

    @Resource
    private KsKssjPkgTaskMapper modelMapper;
    @Resource
    private KsKssjPkgTaskCsMapper ksKssjPkgTaskCsMapper;
    @Value("${xc.xcDfs.serviceAddress}")
    private String xcServiceAddress;


    @Override
    public DataExportLbVO dataExportLb(DataExportLbDTO dto) {
        DataExportLbVO result = new DataExportLbVO();
        Example example = new Example(KsKssjPkgTask.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        if(StringUtils.isNotBlank(dto.getKsjhbh())) {
            criteria.andEqualTo("ksjhbh", dto.getKsjhbh());
        }
        example.orderBy("startTime").desc();

        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KsKssjPkgTask> pkgTaskList = modelMapper.selectByExample(example);

        List<ExportTaskItemVO> exportTaskItemVOS = new ArrayList<>();
        for (KsKssjPkgTask ksKssjPkgTask : pkgTaskList) {
            ExportTaskItemVO taskItemVO = new ExportTaskItemVO();
            taskItemVO.setId(ksKssjPkgTask.getId());
            taskItemVO.setProgress(ksKssjPkgTask.getTProgress());
            taskItemVO.setTaskName(ksKssjPkgTask.getName());
            if (ksKssjPkgTask.getComplete() != null){
                taskItemVO.setStatus(PkgTaskCompleteEnum.get(ksKssjPkgTask.getComplete()).getDescription());
                if (ksKssjPkgTask.getComplete() == PkgTaskCompleteEnum.Done.getCode()){
                    taskItemVO.setResult("成功");
                }else if (ksKssjPkgTask.getComplete() == PkgTaskCompleteEnum.Fail.getCode()){
                    taskItemVO.setResult("失败");
                }
            }

            exportTaskItemVOS.add(taskItemVO);
        }

        result.setExportTaskList(exportTaskItemVOS);
        result.setTotalRows((int)page.getTotal());
        return result;
    }

    @Override
    public KsKssjPkgTask initTask(KsKssjPkgTask task) {
        task.setId(IdGenerateUtil.generateId());
        task.setComplete(PkgTaskCompleteEnum.Begin.getCode());
        task.setTProgress("0");
        task.setStartTime(new Date());
        task.setSczt(ScztEnum.NOTDEL.getCode());
        if (StringUtils.isBlank(task.getPkgCatalog())){
            task.setPkgCatalog(PkgTaskTypeEnum.KD.getMc());
        }

        baseMapper.insert(task);

        return task;
    }

    @Override
    public void updateByPrimaryKey(KsKssjPkgTask ksKsxxExportTask) {
        modelMapper.updateByPrimaryKey(ksKsxxExportTask);
    }

    @Override
    public void updateByPrimaryKeySelective(KsKssjPkgTask completeTask) {
        modelMapper.updateByPrimaryKeySelective(completeTask);
    }

    @Override
    public void dataExportSc(DataExportScDTO dto) {
        Example taskExam = new Example(KsKssjPkgTask.class);
        taskExam.createCriteria().andIn("id", dto.getIdList());
        modelMapper.deleteByExample(taskExam);

        Example csExam = new Example(KsKssjPkgTaskCs.class);
        csExam.createCriteria().andIn("taskId", dto.getIdList());
        ksKssjPkgTaskCsMapper.deleteByExample(csExam);

    }

    @Override
    public DataExportXqVO dataExportXq(DataExportXqDTO dto) {
        DataExportXqVO result = new DataExportXqVO();
        KsKssjPkgTask task = modelMapper.selectByPrimaryKey(dto.getId());
        result.setTaskType(task.getPkgCatalog());
        result.setTaskConf(task.getTConf());
        result.setTaskName(task.getName());

        return result;
    }

    @Override
    public DataExportFileDownLoadVO dataExportFileDownLoad(DataExportFileDownLoadDTO dto) {
        DataExportFileDownLoadVO result = new DataExportFileDownLoadVO();

        KsKssjPkgTask task = modelMapper.selectByPrimaryKey(dto.getId());

        if (task.getComplete() != PkgTaskCompleteEnum.Done.getCode() && task.getComplete() != PkgTaskCompleteEnum.Fail.getCode()){
            throw new IdentityVerifyException("任务未完成");
        }
        if (StringUtils.isNotBlank(task.getLogDfsFileObjUri())){
            result.setUrl(xcServiceAddress + "/remote/dfs/" + task.getLogDfsFileObjUri());
        }else {
            throw new IdentityVerifyException("日志文件不存在");
        }

        return result;
    }


}