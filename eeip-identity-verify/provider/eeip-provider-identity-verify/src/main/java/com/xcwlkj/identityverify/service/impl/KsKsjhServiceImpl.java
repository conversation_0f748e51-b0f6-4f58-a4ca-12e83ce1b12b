/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.mapper.*;
import com.xcwlkj.identityverify.model.domain.*;
import com.xcwlkj.identityverify.model.dto.ksgl.*;

import com.xcwlkj.identityverify.model.dto.sjdb.QlbztDTO;
import com.xcwlkj.identityverify.model.enums.*;
import com.xcwlkj.identityverify.model.vo.ksgl.*;
import com.xcwlkj.identityverify.model.vo.sjdb.QlbztVO;
import com.xcwlkj.identityverify.model.vo.sjxf.ExamPlanVO;
import com.xcwlkj.identityverify.model.vo.sjxf.ExamSeqVO;
import com.xcwlkj.identityverify.service.KsKssjDistributeTaskService;
import com.xcwlkj.identityverify.service.KsKssjPkgStatusService;
import com.xcwlkj.identityverify.service.KsKssjPkgTaskService;
import com.xcwlkj.identityverify.taskcenter.pkgTask.PkgParam;
import com.xcwlkj.identityverify.taskcenter.pkgTask.PkgTaskProcessor;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.service.KsKsjhService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;


/**
 * 考试计划服务
 * <AUTHOR>
 * @version $Id: KsKsjhServiceImpl.java, v 0.1 2023年09月19日 17时16分 xcwlkj.com Exp $
 */
@Slf4j
@Service("ksKsjhService")
public class KsKsjhServiceImpl extends BaseServiceImpl<KsKsjhMapper, KsKsjh> implements KsKsjhService  {

    @Resource
    private KsKsjhMapper modelMapper;
    @Resource
    private CsXxjbxxMapper xxjbxxMapper;
    @Resource
    private KsKsccMapper ksKsccMapper;
    @Resource
    private KsBmxxMapper ksBmxxMapper;
    @Resource
    private KsBmxxKstzzMapper ksBmxxKstzzMapper;
    @Resource
    private KsBpxxMapper ksBpxxMapper;
    @Resource
    private KsJkryBpxxMapper ksJkryBpxxMapper;
    @Resource
    private KsJkryJbxxMapper ksJkryJbxxMapper;
    @Resource
    private KsJkryJbxxTzzMapper ksJkryJbxxTzzMapper;
    @Resource
    private KsJkryRcxxMapper ksJkryRcxxMapper;
    @Resource
    private KsKcxxMapper ksKcxxMapper;
    @Resource
    private KsKdxxMapper ksKdxxMapper;
    @Resource
    private KsKsrcxxMapper ksKsrcxxMapper;
    @Resource
    private KsKssjDistributeStatusMapper ksKssjDistributeStatusMapper;
    @Resource
    private KsKssjDistributeTaskMapper ksKssjDistributeTaskMapper;
    @Resource
    private KsKssjDistributeTaskLogMapper ksKssjDistributeTaskLogMapper;
    @Resource
    private KsKssjImportExceptionMapper ksKssjImportExceptionMapper;
    @Resource
    private KsKssjImportFileMapper ksKssjImportFileMapper;
    @Resource
    private KsKssjImportStatusMapper ksKssjImportStatusMapper;
    @Resource
    private KsKssjImportTaskMapper ksKssjImportTaskMapper;
    @Resource
    private KsKssjPkgStatusMapper ksKssjPkgStatusMapper;
    @Resource
    private KsKssjPkgFileMapper ksKssjPkgFileMapper;
    @Resource
    private KsKssjPkgTaskMapper ksKssjPkgTaskMapper;
    @Resource
    private KsKssjPkgExceptionMapper ksKssjPkgExceptionMapper;
    @Resource
    private KsProcessEventMapper ksProcessEventMapper;
    @Resource
    private KsProcessPeriodMapper ksProcessPeriodMapper;
    @Resource
    private KsKswjWjwgmdMapper ksKswjWjwgmdMapper;
    @Resource
    private KsKwMessageMapper ksKwMessageMapper;
    @Resource
    private KsKssjPkgTaskService ksKssjPkgTaskService;
    @Resource
    private KsKssjPkgStatusService ksKssjPkgStatusService;
    @Resource
    private KsKssjDistributeTaskService ksKssjDistributeTaskService;
    @Resource
    private PkgTaskProcessor taskProcessor;

    private String creatKsjhbh(){
        String ksjhbh = IdGenerateUtil.generateId();
        return ksjhbh;
    }

    @Override
    public void ksjhxz(KsjhxzDTO dto) {
        KsKsjh ksKsjh = new KsKsjh();
        int kzqy = 0;

        ksKsjh.setMc(dto.getMc());
        ksKsjh.setXn(dto.getXn());
        ksKsjh.setXq(dto.getXq());
        String ksjhbh = creatKsjhbh();
        ksKsjh.setKsjhbh(ksjhbh);
        ksKsjh.setKssj(DateUtil.parse(dto.getKssj()));
        ksKsjh.setJssj(DateUtil.parse(dto.getJssj()));
        ksKsjh.setCjsj(DateUtil.getCurrentDT());
        ksKsjh.setXgsj(DateUtil.getCurrentDT());
        ksKsjh.setScztw(ScztEnum.NOTDEL.getCode());
        ksKsjh.setKszt(KsjhztEnum.PROCESSING.getCode());
        ksKsjh.setKslx("1001");
        ksKsjh.setWifiqy(0);
        ksKsjh.setQysbbmd(0);
        if (StringUtils.isNotBlank(dto.getDbms())) {
            ksKsjh.setDbms(Integer.valueOf(dto.getDbms()));
        }
        if (StringUtils.isNotBlank(dto.getJkqdrs())) {
            ksKsjh.setJkqdrs(Integer.valueOf(dto.getJkqdrs()));
        }
        if (dto.getTzzqy()){
            kzqy += KzqyEnum.TZZQY.getValue();
        }
        if (dto.getJkryjbsj()){
            kzqy += KzqyEnum.JKRYQY.getValue();
        }
        if (dto.getJkrybpsj()){
            kzqy += KzqyEnum.JKRYBPQY.getValue();
        }
        if (dto.getJkqdqy()){
            kzqy += KzqyEnum.JKRYQDQY.getValue();
        }
        if (dto.getKszpscqy()){
            kzqy += KzqyEnum.KSZPSCQY.getValue();
        }
        if(dto.getKszpqy()){
            kzqy += KzqyEnum.KSZPQY.getValue();
        }
        ksKsjh.setKzqy(kzqy);
        modelMapper.insert(ksKsjh);

        List<CsXxjbxx> xxjbxxes = xxjbxxMapper.selectAll();
        CsXxjbxx xxjbxx = xxjbxxes.get(0);
        KsKdxx kdxx = new KsKdxx();
        kdxx.setId(IdGenerateUtil.generateId());
        kdxx.setKsjhbh(ksjhbh);
        kdxx.setKqbh(xxjbxx.getXzqhm());
        kdxx.setKdbh(xxjbxx.getXxdm());
        kdxx.setKdmc(xxjbxx.getXxmc());
        kdxx.setBzhkdid(xxjbxx.getZzjgm());
        kdxx.setBzhkdmc(xxjbxx.getXxmc());
        kdxx.setScztw(ScztEnum.NOTDEL.getCode());
        kdxx.setCreateTime(DateUtil.getCurrentDT());
        kdxx.setUpdateTime(DateUtil.getCurrentDT());

        ksKdxxMapper.insert(kdxx);
    }

    @Override
    public void ksjhgx(KsjhgxDTO dto) {
        KsKsjh entity = new KsKsjh();
        int kzqy = 0;

        entity.setKsjhbh(dto.getKsjhbh());
        entity.setMc(dto.getMc());
        entity.setXn(dto.getXn());
        entity.setXq(dto.getXq());
        entity.setKssj(DateUtil.parse(dto.getKssj()));
        entity.setJssj(DateUtil.parse(dto.getJssj()));

        entity.setXgsj(DateUtil.getCurrentDT());
        Example example = new Example(KsKsjh.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhbh", entity.getKsjhbh());
        criteria.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());

        if (StringUtils.isNotBlank(dto.getDbms())) {
            entity.setDbms(Integer.valueOf(dto.getDbms()));
        }
        if (StringUtils.isNotBlank(dto.getJkqdrs())) {
            entity.setJkqdrs(Integer.valueOf(dto.getJkqdrs()));
        }
        if (dto.getTzzqy()){
            kzqy += KzqyEnum.TZZQY.getValue();
        }
        if (dto.getJkryjbsj()){
            kzqy += KzqyEnum.JKRYQY.getValue();
        }
        if (dto.getJkrybpsj()){
            kzqy += KzqyEnum.JKRYBPQY.getValue();
        }
        if (dto.getJkqdqy()){
            kzqy += KzqyEnum.JKRYQDQY.getValue();
        }
        if (dto.getKszpscqy()){
            kzqy += KzqyEnum.KSZPSCQY.getValue();
        }
        if(dto.getKszpqy()){
            kzqy+= KzqyEnum.KSZPQY.getValue();
        }
        entity.setKzqy(kzqy);

        modelMapper.updateByExampleSelective(entity,example);
    }

    @Override
    public int deleteKsjhByKsjhbh(List<String> ksjhbhList) {
        // 删除考试计划关联数据
        deleteRelationData(ksjhbhList);
        //逻辑删除考试计划
        Example exampleKsjh = new Example(KsKsjh.class);
        Example.Criteria criteriaKsjh = exampleKsjh.createCriteria();
        criteriaKsjh.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        criteriaKsjh.andIn("ksjhbh", ksjhbhList);
        KsKsjh ksjhEntity = new KsKsjh();
        ksjhEntity.setScztw(ScztEnum.DEL.getCode());
        ksjhEntity.setXgsj(DateUtil.getCurrentDT());
        int i = modelMapper.updateByExampleSelective(ksjhEntity, exampleKsjh);
        return i;
    }

    /**
     * 删除考试计划关联数据
     * @param ksjhbhList
     */
    private void deleteRelationData(List<String> ksjhbhList){
        // 删除考生报名信息
        Example ksBmxxEx = new Example(KsBmxx.class);
        ksBmxxEx.createCriteria().andIn("ksjhid", ksjhbhList)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        KsBmxx ksBmxxDel = new KsBmxx();
        ksBmxxDel.setScztw(ScztEnum.DEL.getCode());
        ksBmxxDel.setUpdateTime(DateUtil.getCurrentDT());
        ksBmxxMapper.updateByExampleSelective(ksBmxxDel, ksBmxxEx);
        // 删除考生编排信息
        Example ksBpxxEx = new Example(KsBpxx.class);
        ksBpxxEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        KsBpxx ksBpxxDel = new KsBpxx();
        ksBpxxDel.setScztw(ScztEnum.DEL.getCode());
        ksBpxxDel.setUpdateTime(DateUtil.getCurrentDT());
        ksBpxxMapper.updateByExampleSelective(ksBpxxDel, ksBpxxEx);
        // 考生报名信息特征值

        // 监考编排
        Example jkryBpEx = new Example(KsJkryBpxx.class);
        jkryBpEx.createCriteria().andIn("ksjhdm", ksjhbhList);
        ksJkryBpxxMapper.deleteByExample(jkryBpEx);
        // 监考基本
        Example jkryJbEx = new Example(KsJkryJbxx.class);
        jkryJbEx.createCriteria().andIn("ksjhdm", ksjhbhList);
        ksJkryJbxxMapper.deleteByExample(jkryJbEx);
        // 监考基本特征值
        // 监考入场
        Example jkryRcEx = new Example(KsJkryRcxx.class);
        jkryRcEx.createCriteria().andIn("ksjhdm", ksjhbhList);
        ksJkryRcxxMapper.deleteByExample(jkryRcEx);
        // 考场
        Example kcxxEx = new Example(KsKcxx.class);
        kcxxEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKcxx kcxxDel = new KsKcxx();
        kcxxDel.setSczt(ScztEnum.DEL.getCode());
        kcxxDel.setUpdateTime(DateUtil.getCurrentDT());
        ksKcxxMapper.updateByExampleSelective(kcxxDel, kcxxEx);
        // 考点
        Example kdxxEx = new Example(KsKdxx.class);
        kdxxEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        KsKdxx kdxxDel = new KsKdxx();
        kdxxDel.setScztw(ScztEnum.DEL.getCode());
        kdxxDel.setUpdateTime(DateUtil.getCurrentDT());
        ksKdxxMapper.updateByExampleSelective(kdxxDel, kdxxEx);
        // 考生入场
        Example ksrcEx = new Example(KsKsrcxx.class);
        ksrcEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        KsKsrcxx ksrcDel = new KsKsrcxx();
        ksrcDel.setScztw(ScztEnum.DEL.getCode());
        ksrcDel.setUpdateTime(DateUtil.getCurrentDT());
        ksKsrcxxMapper.updateByExampleSelective(ksrcDel, ksrcEx);
        // 下发状态
        Example xfztEx = new Example(KsKssjDistributeStatus.class);
        xfztEx.createCriteria().andIn("ksjhbh", ksjhbhList);
        ksKssjDistributeStatusMapper.deleteByExample(xfztEx);
        // 下发任务
        Example xfrwEx = new Example(KsKssjDistributeTask.class);
        xfrwEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKssjDistributeTask xfrwDel = new KsKssjDistributeTask();
        xfrwDel.setSczt(ScztEnum.DEL.getCode());
        ksKssjDistributeTaskMapper.updateByExampleSelective(xfrwDel, xfrwEx);
        // 下发任务日志
        Example xfrwLogEx = new Example(KsKssjDistributeTaskLog.class);
        xfrwLogEx.createCriteria().andIn("ksjhbh", ksjhbhList);
        ksKssjDistributeTaskLogMapper.deleteByExample(xfrwLogEx);
        // 导入异常
        Example drycEx = new Example(KsKssjImportException.class);
        drycEx.createCriteria().andIn("ksjhbh", ksjhbhList);
        ksKssjImportExceptionMapper.deleteByExample(drycEx);
        // 导入文件
        Example drwjEx = new Example(KsKssjImportFile.class);
        drwjEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKssjImportFile drwjDel = new KsKssjImportFile();
        drwjDel.setSczt(ScztEnum.DEL.getCode());
        drwjDel.setUpdateTime(DateUtil.getCurrentDT());
        ksKssjImportFileMapper.updateByExampleSelective(drwjDel, drwjEx);
        // 导入状态
        Example drztEx = new Example(KsKssjImportStatus.class);
        drztEx.createCriteria().andIn("ksjhbh", ksjhbhList);
        ksKssjImportStatusMapper.deleteByExample(drztEx);
        // 导入任务
        Example drrwEx = new Example(KsKssjImportTask.class);
        drrwEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKssjImportTask drrwDel = new KsKssjImportTask();
        drrwDel.setSczt(ScztEnum.DEL.getCode());
        ksKssjImportTaskMapper.updateByExampleSelective(drrwDel, drrwEx);
        // 打包异常
        Example dbycEx = new Example(KsKssjPkgException.class);
        dbycEx.createCriteria().andIn("ksjhbh", ksjhbhList);
        ksKssjPkgExceptionMapper.deleteByExample(dbycEx);
        // 打包文件
        Example dbwjEx = new Example(KsKssjPkgFile.class);
        dbwjEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKssjPkgFile dbwjDel = new KsKssjPkgFile();
        dbwjDel.setSczt(ScztEnum.DEL.getCode());
        dbwjDel.setUpdateTime(DateUtil.getCurrentDT());
        ksKssjPkgFileMapper.updateByExampleSelective(dbwjDel, dbwjEx);
        // 打包状态
        Example dbztEx = new Example(KsKssjPkgStatus.class);
        dbztEx.createCriteria().andIn("ksjhbh", ksjhbhList);
        ksKssjPkgStatusMapper.deleteByExample(dbztEx);
        // 打包任务
        Example dbrwEx = new Example(KsKssjPkgTask.class);
        dbrwEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKssjPkgTask dbrwDel = new KsKssjPkgTask();
        dbrwDel.setSczt(ScztEnum.DEL.getCode());
        ksKssjPkgTaskMapper.updateByExampleSelective(dbrwDel, dbrwEx);
        // 事件记录
        Example eventEx = new Example(KsProcessEvent.class);
        eventEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsProcessEvent eventDel = new KsProcessEvent();
        eventDel.setSczt(ScztEnum.DEL.getCode());
        eventDel.setUpdateTime(DateUtil.getCurrentDT());
        ksProcessEventMapper.updateByExampleSelective(eventDel, eventEx);
        // 事件阶段
        Example periodEx = new Example(KsProcessPeriod.class);
        periodEx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsProcessPeriod periodDel = new KsProcessPeriod();
        periodDel.setSczt(ScztEnum.DEL.getCode());
        periodDel.setUpdateTime(DateUtil.getCurrentDT());
        ksProcessPeriodMapper.updateByExampleSelective(periodDel, periodEx);
        //逻辑删除考试场次
        Example exampleKscc = new Example(KsKscc.class);
        Example.Criteria criteriaKscc = exampleKscc.createCriteria();
        criteriaKscc.andIn("ksjhbh", ksjhbhList);
        criteriaKscc.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        KsKscc kscc = new KsKscc();
        kscc.setScztw(ScztEnum.DEL.getCode());
        kscc.setXgsj(DateUtil.getCurrentDT());
        ksKsccMapper.updateByExampleSelective(kscc, exampleKscc);
        //违纪违规
        Example exampleWjwg = new Example(KsKswjWjwgmd.class);
        exampleWjwg.createCriteria().andIn("ksjh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKswjWjwgmd wjwgmd = new KsKswjWjwgmd();
        wjwgmd.setSczt(ScztEnum.DEL.getCode());
        wjwgmd.setUpdateTime(DateUtil.getCurrentDT());
        ksKswjWjwgmdMapper.updateByExampleSelective(wjwgmd, exampleWjwg);
        // 考务消息
        Example exampleKwxx = new Example(KsKwMessage.class);
        exampleKwxx.createCriteria().andIn("ksjhbh", ksjhbhList)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        KsKwMessage kwMessage = new KsKwMessage();
        kwMessage.setSczt(ScztEnum.DEL.getCode());
        ksKwMessageMapper.updateByExampleSelective(kwMessage, exampleKwxx);
    }

    @Override
    public KsjhlbVO ksjhlb(KsjhlbDTO dto) {
        String xn = dto.getXn();
        String xq = dto.getXq();
        String kszt = dto.getKszt();
        KsjhlbVO result = new KsjhlbVO();

        //设置查询条件
        Example example = new Example(KsKsjh.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        if (StringUtils.isNotBlank(xn)){
            criteria.andEqualTo("xn",xn);
        }
        if (StringUtils.isNotBlank(xq)){
            criteria.andEqualTo("xq",xq);
        }
        if (StringUtils.isNotBlank(kszt)){
            criteria.andEqualTo("kszt",kszt);
        }
        example.orderBy("cjsj").desc();

        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KsKsjh> ksKsjhList = modelMapper.selectByExample(example);
        PageInfo<KsKsjh> page = new PageInfo<>(ksKsjhList);

        //返回值赋值
        List<KsjhItemVO> ksjhlbDOList = new LinkedList<>();
        if (ksKsjhList != null && ksKsjhList.size() != 0){
            for (KsKsjh ksKsjh : ksKsjhList) {
                KsjhItemVO ksjhlbDO = new KsjhItemVO();
                ksjhlbDO.setKsjhbh(ksKsjh.getKsjhbh());
                ksjhlbDO.setKssj(DateUtil.formatDateTime(ksKsjh.getKssj()));
                ksjhlbDO.setJssj(DateUtil.formatDateTime(ksKsjh.getJssj()));
                ksjhlbDO.setMc(ksKsjh.getMc());
                ksjhlbDO.setXn(ksKsjh.getXn());
                ksjhlbDO.setXq(ksKsjh.getXq());
                ksjhlbDO.setKszt(ksKsjh.getKszt());
                ksjhlbDO.setSfmr(ksKsjh.getSfmr());
                if (ksKsjh.getJkqdrs() != null) {
                    ksjhlbDO.setJkqdrs(String.valueOf(ksKsjh.getJkqdrs()));
                }
                if (ksKsjh.getDbms() != null) {
                    ksjhlbDO.setDbms(String.valueOf(ksKsjh.getDbms()));
                }
                Integer kzqy = ksKsjh.getKzqy();

                if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) != 0){
                    ksjhlbDO.setJkrybpsj(Boolean.TRUE);
                } else {
                    ksjhlbDO.setJkrybpsj(Boolean.FALSE);
                }
                if ((kzqy & KzqyEnum.JKRYQY.getValue()) != 0){
                    ksjhlbDO.setJkryjbsj(Boolean.TRUE);
                } else {
                    ksjhlbDO.setJkryjbsj(Boolean.FALSE);
                }
                if ((kzqy & KzqyEnum.JKRYQDQY.getValue()) != 0){
                    ksjhlbDO.setJkqdqy(Boolean.TRUE);
                } else {
                    ksjhlbDO.setJkqdqy(Boolean.FALSE);
                }
                if ((kzqy & KzqyEnum.KSZPSCQY.getValue()) != 0){
                    ksjhlbDO.setKszpscqy(Boolean.TRUE);
                } else {
                    ksjhlbDO.setKszpscqy(Boolean.FALSE);
                }
                if ((kzqy & KzqyEnum.TZZQY.getValue()) != 0){
                    ksjhlbDO.setTzzqy(Boolean.TRUE);
                } else {
                    ksjhlbDO.setTzzqy(Boolean.FALSE);
                }
                if ((kzqy & KzqyEnum.KSZPQY.getValue()) != 0) {
                    ksjhlbDO.setKszpqy(Boolean.TRUE);
                } else {
                    ksjhlbDO.setKszpqy(Boolean.FALSE);
                }

                ksjhlbDOList.add(ksjhlbDO);
            }
        }
        result.setKsjhList(ksjhlbDOList);
        result.setTotalRows(page.getTotal() + "");

        return result;
    }

    @Override
    public void ggkszt(GgksztDTO dto) {
        KsKsjh ksKsjh = new KsKsjh();
        ksKsjh.setKsjhbh(dto.getKsjhbh());
        ksKsjh.setKszt(dto.getKszt());
        ksKsjh.setXgsj(DateUtil.getCurrentDT());
        modelMapper.updateByPrimaryKeySelective(ksKsjh);
    }

    @Override
    public List<ExamPlanVO> getExamPlanV1(String ksjhbh, String kssj, String jssj) {
        List<ExamPlanVO> planVOList = Lists.newArrayList();
        List<KsKsjh> list = Lists.newArrayList();
        Example example = new Example(KsKsjh.class);
        Example.Criteria criKsjh = example.createCriteria()
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        if (StringUtils.isNotBlank(ksjhbh)) {
            criKsjh.andEqualTo("ksjhbh", ksjhbh);
        }
        if (StringUtils.isNotBlank(kssj)) {
            criKsjh.andGreaterThanOrEqualTo("jssj", DateUtil.parseDateTime(kssj));
        }
        if (StringUtils.isNotBlank(jssj)) {
            criKsjh.andLessThanOrEqualTo("kssj", DateUtil.parseDateTime(jssj));
        }

        list = modelMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(list)) {
            List<String> ksjhbhList = Lists.newArrayList();
            Example ksccExample = new Example(KsKscc.class);
            for (KsKsjh ksjh : list) {
                ksjhbhList.add(ksjh.getKsjhbh());
            }
            ksccExample.createCriteria().andIn("ksjhbh", ksjhbhList)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
            List<KsKscc> ksccList = ksKsccMapper.selectByExample(ksccExample);
            //处理考试场次
            Map<String, List<KsKscc>> ksccMap = Maps.newHashMap();
            for (KsKscc kscc : ksccList) {
                String mksjhbh = kscc.getKsjhbh();
                if (ksccMap.containsKey(mksjhbh)) {
                    ksccMap.get(mksjhbh).add(kscc);
                } else {
                    List<KsKscc> mkscc = Lists.newArrayList();
                    mkscc.add(kscc);
                    ksccMap.put(mksjhbh, mkscc);
                }
            }

            for (KsKsjh ksjh : list) {
                ExamPlanVO examPlanVO = new ExamPlanVO();
                String mksjhbh = ksjh.getKsjhbh();
                examPlanVO.setKsbh(mksjhbh);
                examPlanVO.setKsmc(ksjh.getMc());
                examPlanVO.setKsksrq(DateUtil.formatDate(ksjh.getKssj()));
                examPlanVO.setKsjsrq(DateUtil.formatDate(ksjh.getJssj()));
                examPlanVO.setSbjm(ksjh.getSbjm());
                examPlanVO.setJkxxqy(String.valueOf(ksjh.getKzqy()));
                if (ksccMap.containsKey(mksjhbh)) {
                    List<KsKscc> mksccList = ksccMap.get(mksjhbh);
                    Map<String, ExamSeqVO> seqMap = Maps.newTreeMap();
                    for (KsKscc kscc : mksccList) {
                        String ccm = kscc.getCcm();
                        if (seqMap.containsKey(ccm)) {
                            ExamSeqVO seqVO = seqMap.get(ccm);
                            String kmbh = seqVO.getKmbh() + "," + kscc.getKmm();
                            String kmmc = seqVO.getKmmc() + "," + kscc.getKmmc();
                            seqVO.setKmbh(kmbh);
                            seqVO.setKmmc(kmmc);
                        } else {
                            ExamSeqVO seqVO = new ExamSeqVO();
                            String kmbh = kscc.getKmm();
                            String kmmc = kscc.getKmmc();
                            seqVO.setCcbh(kscc.getCcm());
                            seqVO.setName(kscc.getCcmc());
                            seqVO.setCcjssj(DateUtil.formatDateTime(kscc.getJssj()));
                            seqVO.setCckssj(DateUtil.formatDateTime(kscc.getKssj()));
                            seqVO.setYxcdsj(kscc.getYxcdsj() == null ? "" : kscc.getYxcdsj().toString());
                            seqVO.setKmbh(kmbh);
                            seqVO.setKmmc(kmmc);
                            seqMap.put(ccm, seqVO);
                        }
                    }
                    for (Map.Entry<String, ExamSeqVO> entry : seqMap.entrySet()) {
                        examPlanVO.getCc().add(entry.getValue());
                    }
                }
                planVOList.add(examPlanVO);
            }
        }
        return planVOList;
    }

    @Override
    public List<KsKsjh> getKsjhByBzhkdid(String bzhkdid, Date kssj, Date jssj) {
        return modelMapper.selectListByBzhkdid(bzhkdid, kssj, jssj);
    }

    @Override
    public DrlxlbVO drlxlb() {
        DrlxlbVO result = new DrlxlbVO();
        List<DrlxItemVO> drlxItemVOS = new ArrayList<>();
        SuperiorPlatEnum[] values = SuperiorPlatEnum.values();
        for (SuperiorPlatEnum value : values) {
            DrlxItemVO itemVO = new DrlxItemVO();
            itemVO.setDrlxdm(value.getCode());
            itemVO.setDrlxmc(value.getName());
            if (value.equals(SuperiorPlatEnum.SXCJXX)){
                itemVO.setZipFlag(true);
            }else {
                itemVO.setZipFlag(false);
            }
            drlxItemVOS.add(itemVO);
        }
        result.setDrlxList(drlxItemVOS);
        return result;
    }

    @Override
    public SjptksjhlbVO getKsjhbhAndKsmc() {
        SjptksjhlbVO result = new SjptksjhlbVO();
        List<SjptksjhItemVO> itemVOList = new ArrayList<>();
        Example example = new Example(KsKsjh.class);
        example.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andNotEqualTo("kszt", KsjhztEnum.END.getCode());
        List<KsKsjh> ksjhList = modelMapper.selectByExample(example);
        for (KsKsjh ksjh : ksjhList) {
            SjptksjhItemVO itemVO = new SjptksjhItemVO();
            itemVO.setKsjhbh(ksjh.getKsjhbh());
            itemVO.setKsjhcm(ksjh.getMc());
            itemVOList.add(itemVO);
        }
        result.setSjptksjhList(itemVOList);
        return result;
    }

    @Override
    public void gbksjhmrzt(GbksjhmrztDTO dto) {
        Integer sfmr = dto.getSfmr();
        String ksjhbh = dto.getKsjhbh();
        modelMapper.updateSfmr(ksjhbh, sfmr);
        if (sfmr == 1) {
            Example example = new Example(KsKsjh.class);
            example.createCriteria().andEqualTo("sfmr", sfmr)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                    .andNotEqualTo("ksjhbh", ksjhbh);
            KsKsjh ksjh = new KsKsjh();
            ksjh.setSfmr(0);
            modelMapper.updateByExampleSelective(ksjh, example);
        }
    }

    @Override
    public void ksjhdsxfpz(KsjhdsxfpzDTO dto) {
        KsKsjh ksjh = new KsKsjh();
        ksjh.setKsjhbh(dto.getKsjhbh());
        if (StringUtils.equals(dto.getQydsxf(), "1")) {
            ksjh.setQydsxf(1);
            ksjh.setDsxfsj(DateUtil.parse(dto.getDsxfsj()));
            ksjh.setDsxfzt(0);
        } else {
            ksjh.setQydsxf(0);
        }
        modelMapper.updateByPrimaryKeySelective(ksjh);
    }

    @Override
    public KsjhdsxfpzcxVO ksjhdsxfpzcx(KsjhdsxfpzcxDTO dto) {
        KsKsjh ksKsjh = modelMapper.selectByPrimaryKey(dto.getKsjhbh());
        if (ksKsjh == null) {
            throw new IdentityVerifyException("未查询到考试计划：" + dto.getKsjhbh());
        }
        KsjhdsxfpzcxVO ksjhdsxfpzcxVO = new KsjhdsxfpzcxVO();
        if (Objects.equals(1, ksKsjh.getQydsxf())) {
            ksjhdsxfpzcxVO.setQydsxf("1");
            ksjhdsxfpzcxVO.setDsxfsj(DateUtil.formatDateTime(ksKsjh.getDsxfsj()));
        } else {
            ksjhdsxfpzcxVO.setQydsxf("0");
        }
        return ksjhdsxfpzcxVO;
    }

    @Override
    public void ksjhfhxfrwxj(KsjhfhxfrwxjDTO dto) {
        if (StringUtils.equals(dto.getType(), "1")) {
            //定时任务
            KsjhdsxfpzDTO ksjhdsxfpzDTO = new KsjhdsxfpzDTO();
            ksjhdsxfpzDTO.setKsjhbh(dto.getKsjhbh());
            ksjhdsxfpzDTO.setQydsxf("1");
            ksjhdsxfpzDTO.setDsxfsj(dto.getDsrwsj());
            ksjhdsxfpz(ksjhdsxfpzDTO);
        } else {
            //立即任务
            String pkgCatalogCode = PkgCatalogEnum.QL.getCode();
            Integer pkgCatalogVal = PkgCatalogEnum.QL.getValue();
            if (StringUtils.equals(dto.getSfcxdb(), "0")) {
                //不重新打包
                QlbztDTO qlbztDTO = new QlbztDTO();
                qlbztDTO.setKsjhbh(dto.getKsjhbh());
                QlbztVO qlbztVO = ksKssjPkgStatusService.qlbzt(qlbztDTO);
                if (StringUtils.equals(qlbztVO.getPkgstatus(), "1")) {
                    ksKssjDistributeTaskService.pkgDoneAutoDist(dto.getKsjhbh(), pkgCatalogVal);
                } else {
                    throw new IdentityVerifyException("数据打包未完成！ 考试计划：" + dto.getKsjhbh());
                }
            } else {
                //重新打包并下发
                // 初始化任务
                int taskTConf = 0;
                KsKsjh ksKsjh = modelMapper.selectByPrimaryKey(dto.getKsjhbh());
                Integer kzqy = ksKsjh.getKzqy();
                taskTConf += PkgTaskConfEnum.KS.getCode();
                taskTConf += PkgTaskConfEnum.KCPZ.getCode();
                if (KzqyEnum.KSZPQY.isEnabled(kzqy)) {
                    taskTConf += PkgTaskConfEnum.KSZP.getCode();
                }
                if ((kzqy & KzqyEnum.JKRYQY.getValue()) != 0) {
                    taskTConf += PkgTaskConfEnum.JKRY.getCode();
                }
                if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) != 0) {
                    taskTConf += PkgTaskConfEnum.JKRYBP.getCode();
                }
                if(KzqyEnum.JKRYQDQY.isEnabled(kzqy)){
                    taskTConf += PkgTaskConfEnum.JKRYZP.getCode();
                }
                ksKssjPkgStatusService.checkStatus(dto.getKsjhbh(), taskTConf, pkgCatalogCode);

                KsKssjPkgTask ksKssjPkgTask = new KsKssjPkgTask();
                ksKssjPkgTask.setName(DateUtil.format(new Date(), "yyyyMMdd_HHmmss"));
                ksKssjPkgTask.setTConf(taskTConf);
                ksKssjPkgTask.setKsjhbh(dto.getKsjhbh());
                ksKssjPkgTask.setPkgCatalog(pkgCatalogCode);
                KsKssjPkgTask task = ksKssjPkgTaskService.initTask(ksKssjPkgTask);

                PkgParam pkgParam = new PkgParam();
                pkgParam.setTaskConf(taskTConf);
                pkgParam.setTaskId(task.getId());
                pkgParam.setPackMode(pkgCatalogVal);
                pkgParam.setUseCache(false);
                pkgParam.setAutoDist(1);
                pkgParam.setKsjhbh(dto.getKsjhbh());
                taskProcessor.taskAsyncExecute(pkgParam);
            }
        }
    }

    @Override
    public Boolean nowTimeIsDistTaskTime(KsKsjh ksKsjh) {
        if (Objects.equals(1, ksKsjh.getQydsxf()) && Objects.equals(0, ksKsjh.getDsxfzt())) {
            Date nowTime = new Date();
            Date dsxfsj = ksKsjh.getDsxfsj();
            Date kssj = ksKsjh.getKssj();
            Date jssj = ksKsjh.getJssj();
            //当前时间在定时下发时间之后，并且在考试计划时间内、在第一场开考前
            if (nowTime.after(dsxfsj) && (kssj.before(nowTime) && nowTime.before(jssj))) {
                Example ksccExample = new Example(KsKscc.class);
                ksccExample.createCriteria()
                        .andEqualTo("ksjhbh", ksKsjh.getKsjhbh())
                        .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                ksccExample.orderBy("kssj");
                List<KsKscc> ksKsccs = ksKsccMapper.selectByExample(ksccExample);
                if (CollectionUtils.isNotEmpty(ksKsccs)) {
                    Date ccKssj = ksKsccs.get(0).getKssj();
                    if (nowTime.before(ccKssj)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public void insertOrUpdate(KsKsjh ksjh) {
        if (StringUtils.isBlank(ksjh.getKsjhbh())) {
            throw new IdentityVerifyException("考试计划编号不能为空");
        }
        KsKsjh ksKsjh = this.selectSingleByKey(ksjh.getKsjhbh());
        ksjh.setXgsj(DateUtil.getCurrentDT());
        if (ksKsjh != null) {
            //更新
            modelMapper.updateByPrimaryKeySelective(ksjh);
        } else {
            //新增
            this.insertSingle(ksjh);
        }
    }

    @Override
    public List<KsKsjh> getOngoingExamPlans() {
        try {
            Date nowDate = new Date();
            // 查询考试状态为进行中的考试计划
            Example example = new Example(KsKsjh.class);
            Example.Criteria criteria = example.createCriteria();
//            KsjhCjlxEnum
            criteria.andLessThanOrEqualTo("kssj", nowDate)
                    .andGreaterThanOrEqualTo("jssj", nowDate)
                    .andEqualTo("cjlx", KsjhCjlxEnum.SJPTDR.getCode())
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
            List<KsKsjh> examPlans = this.selectListByExample(example);
            return CollectionUtils.isNotEmpty(examPlans) ? examPlans : new ArrayList<>();

        } catch (Exception e) {
            log.error("获取进行中的考试计划失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}