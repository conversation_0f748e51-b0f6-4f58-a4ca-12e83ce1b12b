/**
 * xcwlkj.com Inc.
 * Copyright (c) 2025-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.domain;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * 空考位消息
 *
 * <AUTHOR>
 * @version $Id: KsKkwMsg.java, v 0.1 2025年07月28日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "ks_kkw_msg")
public class KsKkwMsg implements Serializable {

    /** 序列id */
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    private String id;

    /** 考试计划编号 */
    @Column(name = "KSJHBH")
    private String ksjhbh;

    /** 场次码 */
    @Column(name = "CCM")
    private String ccm;

    /** 标准化考点id */
    @Column(name = "BZHKDID")
    private String bzhkdid;

    /** 标准化考场id */
    @Column(name = "BZHKCID")
    private String bzhkcid;

    /** 设备序列号 */
    @Column(name = "SN")
    private String sn;

    /** 删除状态 0-正常  1-删除 */
    @Column(name = "SCZT")
    private String sczt;

    /** 创建时间 */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /** 更新时间 */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /** 考生准考证号 */
    @Column(name = "KS_ZKZH")
    private String ksZkzh;

    /** 考生编排座位号 */
    @Column(name = "KS_BPZWH")
    private String ksBpzwh;

    /** 考生实际座位号 */
    @Column(name = "KS_SJZWH")
    private String ksSjzwh;

    /** 空考位 */
    @Column(name = "KS_KKW")
    private String ksKkw;

    /** 座次起始位置码 */
    @Column(name = "ZCQSWZM")
    private String zcqswzm;

    /** 座位布局方式码 */
    @Column(name = "ZWBJFSM")
    private String zwbjfsm;

    /** 座位排列方式码 */
    @Column(name = "ZWPLFSM")
    private String zwplfsm;

    /** 逻辑考场号 */
    @Column(name = "LJKCH")
    private String ljkch;

    /** 考场号 */
    @Column(name = "KCH")
    private String kch;

    /** 设备类型 */
    @Column(name = "DEV_TYPE")
    private String devType;

    /** 入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工  6-缺考（空位）7-无编排 */
    @Column(name = "RCBZ")
    private String rcbz;

    /** 数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场 */
    @Column(name = "SJYXJ")
    private Integer sjyxj;

    /** 时间戳 */
    @Column(name = "TIMESTAMP")
    private Date timestamp;

    /** 验证方式 */
    @Column(name = "YZFS")
    private String yzfs;

    /** 验证结果 */
    @Column(name = "YZJG")
    private String yzjg;

    /** 是否入场 */
    @Column(name = "SFRC")
    private String sfrc;

    /** 入场时间 */
    @Column(name = "RCSJ")
    private String rcsj;

    /** 入场时间分组 */
    @Column(name = "RCSJFZ")
    private String rcsjfz;

    /** 人工验证结果 1-通过 0-不通过 */
    @Column(name = "RGYZJG")
    private String rgyzjg;

    /** 数据来源  web - 来自于 平台的页面 ；pad  - 来自于 核验终端 */
    @Column(name = "SJLY")
    private String sjly;

    /** 操作时间 */
    @Column(name = "CZSJ")
    private Date czsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
