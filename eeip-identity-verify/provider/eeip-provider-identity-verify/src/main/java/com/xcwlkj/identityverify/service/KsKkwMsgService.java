/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service;

import com.github.pagehelper.PageInfo;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwTjDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwXqDTO;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwTjRespModel;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwXqRespModel;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjXqVO;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.service.BaseService;
import com.xcwlkj.identityverify.model.domain.KsKkwMsg;

import java.util.List;


/**
 * 空考位消息服务
 * <AUTHOR>
 * @version $Id: KsKkwMsgService.java, v 0.1 2025年07月28日 xcwlkj.com Exp $
 */
@Service
public interface KsKkwMsgService extends BaseService<KsKkwMsg> {

    /**
     * 根据考试计划编号和场次码查询空考位消息
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @return 空考位消息列表
     */
    List<KsKkwMsg> findByKsjhbhAndCcm(String ksjhbh, String ccm);

    /**
     * 根据考试计划编号、场次码和考场号查询空考位消息
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @param kch 考场号
     * @return 空考位消息列表
     */
    List<KsKkwMsg> findByKsjhbhAndCcmAndKch(String ksjhbh, String ccm, String kch);

    /**
     * 插入空考位消息
     * @param ksKkwMsg 空考位消息对象
     */
    void insertKkwMsg(KsKkwMsg ksKkwMsg);

    /**
     * 根据设备序列号查询空考位消息
     * @param sn 设备序列号
     * @return 空考位消息列表
     */
    List<KsKkwMsg> findBySn(String sn);

    /**
     * 根据准考证号查询空考位消息
     * @param zkzh 准考证号
     * @return 空考位消息列表
     */
    List<KsKkwMsg> findByZkzh(String zkzh);

    /**
     * 查询空考位统计信息（包含列表和摘要）
     * @param dto 查询条件
     * @return 空考位统计响应
     */
    KkwTjRespModel getKkwTj(KkwTjDTO dto);

    /**
     * 查询空考位详情列表
     * @param dto 查询条件
     * @return 空考位详情响应
     */
    KkwXqRespModel getKkwTjXq(KkwXqDTO dto);

    /**
     * 导出空考位详情Excel
     * @param dto 查询条件
     * @return Excel文件路径
     */
    String exportKkwXqExcel(KkwXqDTO dto);
}
