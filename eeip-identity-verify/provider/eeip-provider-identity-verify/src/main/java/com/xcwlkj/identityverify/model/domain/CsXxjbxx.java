/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 学校基本信息
 * 
 * <AUTHOR>
 * @version $Id: CsXxjbxx.java, v 0.1 2023年09月19日 17时13分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "cs_xxjbxx")
public class CsXxjbxx implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 学校代码 */
    @Id
    @Column(name = "xxdm")
    private String            xxdm;
    /** 学校名称 */
    @Column(name = "xxmc")
    private String            xxmc;
    /** 组织机构码 */
    @Column(name = "zzjgm")
    private String            zzjgm;
    /** 学校地址 */
    @Column(name = "xxdz")
    private String            xxdz;
    /** 学校所在地行政区划码 */
    @Column(name = "xzqhm")
    private String            xzqhm;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


