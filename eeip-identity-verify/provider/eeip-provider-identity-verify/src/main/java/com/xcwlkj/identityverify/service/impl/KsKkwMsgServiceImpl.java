/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwTjDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwXqDTO;
import com.xcwlkj.identityverify.model.enums.KkwYclxEnum;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwTjRespModel;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwXqRespModel;
import com.xcwlkj.identityverify.model.vo.attachment.UploadAttachmentReturnUrlVO;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjInfoVO;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjItem;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjXqVO;
import com.xcwlkj.identityverify.template.KkwXqTemplate;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.model.domain.KsKkwMsg;

import com.xcwlkj.identityverify.mapper.KsKkwMsgMapper;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 空考位消息服务实现
 * <AUTHOR>
 * @version $Id: KsKkwMsgServiceImpl.java, v 0.1 2025年07月28日 xcwlkj.com Exp $
 */
@Slf4j
@Service("ksKkwMsgService")
public class KsKkwMsgServiceImpl extends BaseServiceImpl<KsKkwMsgMapper, KsKkwMsg> implements KsKkwMsgService {

    @Resource
    private KsKkwMsgMapper modelMapper;
    @Value("${xc.temp.generatePath}")
    private String generatePath;
    @Resource
    private AttachmentHandler attachmentHandler;
    @Override
    public List<KsKkwMsg> findByKsjhbhAndCcm(String ksjhbh, String ccm) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public List<KsKkwMsg> findByKsjhbhAndCcmAndKch(String ksjhbh, String ccm, String kch) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("kch", kch)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public void insertKkwMsg(KsKkwMsg ksKkwMsg) {
        if (StringUtil.isEmpty(ksKkwMsg.getId())) {
            ksKkwMsg.setId(IdGenerateUtil.generateId());
        }
        if (ksKkwMsg.getCreateTime() == null) {
            ksKkwMsg.setCreateTime(new Date());
        }
        if (ksKkwMsg.getUpdateTime() == null) {
            ksKkwMsg.setUpdateTime(new Date());
        }
        if (StringUtil.isEmpty(ksKkwMsg.getSczt())) {
            ksKkwMsg.setSczt(ScztEnum.NOTDEL.getCode());
        }
        modelMapper.insertSelective(ksKkwMsg);
    }

    @Override
    public List<KsKkwMsg> findBySn(String sn) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("sn", sn)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public List<KsKkwMsg> findByZkzh(String zkzh) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksZkzh", zkzh)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public KkwTjRespModel getKkwTj(KkwTjDTO dto) {
        // 查询统计摘要信息
        KkwTjInfoVO summary = modelMapper.selectKkwTjInfo(dto);

        // 分页查询列表数据
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KkwTjItem> list = modelMapper.selectKkwTjLb(dto);
        PageInfo<KkwTjItem> pageInfo = new PageInfo<>(list);

        KkwTjRespModel respModel = new KkwTjRespModel();
        respModel.setPageInfo(pageInfo);
        respModel.setSummary(summary);

        log.info("空考位统计查询完成，总记录数：{}，摘要信息：{}", pageInfo.getTotal(), summary);
        return respModel;
    }

    @Override
    public KkwXqRespModel getKkwTjXq(KkwXqDTO dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KkwTjXqVO> list = modelMapper.selectKkwTjXq(dto);
        PageInfo<KkwTjXqVO> pageInfo = new PageInfo<>(list);

        // 构建响应对象
        KkwXqRespModel respModel = new KkwXqRespModel();
        respModel.setPageInfo(pageInfo);

        log.info("空考位详情查询完成，总记录数：{}", pageInfo.getTotal());
        return respModel;
    }

    @Override
    public String exportKkwXqExcel(KkwXqDTO dto) {
        log.info("开始导出空考位详情Excel，查询条件：{}", dto);

        // 查询所有数据，不分页
        List<KkwTjXqVO> dataList = modelMapper.selectKkwTjXq(dto);

        // 数据转换为Excel模板对象
        List<KkwXqTemplate> templateList = new ArrayList<>();
        for (KkwTjXqVO vo : dataList) {
            KkwXqTemplate template = new KkwXqTemplate();
            template.setKch(vo.getKch());
            template.setCsmc(vo.getCsmc());
            template.setZkzh(vo.getZkzh());
            template.setXm(vo.getXm());
            template.setZwh(vo.getZwh());
            template.setYclx(KkwYclxEnum.getDescriptionByCode(vo.getYclx()));
            template.setZdsbsj(vo.getZdsbsj());
            // 添加逻辑考场号（使用考场号作为逻辑考场号）
            template.setLjkcbh(vo.getLjkcbh());
            templateList.add(template);
        }


        String rootPath = generatePath + File.separator + "kkw" + File.separator;
        File rootDir = new File(rootPath);
        if (!rootDir.exists()) {
            rootDir.mkdirs();
        }
        // 生成文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String fileName = "空考位详情_" + timestamp + ".xlsx";

        String filePath = rootPath + fileName;

        // 使用EasyExcel写入文件
        EasyExcel.write(filePath, KkwXqTemplate.class)
                .sheet("空考位详情")
                .doWrite(templateList);
        Date expiteTime = DateUtil.offsetHour(DateUtil.getCurrentDT(), 2);
        UploadAttachmentReturnUrlVO uploadAttachmentReturnUrlVO = attachmentHandler.uploadAttachmentReturnUrl(filePath, expiteTime, null);
        rootDir.delete();

        return uploadAttachmentReturnUrlVO.getAttachmentUrl();
    }
}
