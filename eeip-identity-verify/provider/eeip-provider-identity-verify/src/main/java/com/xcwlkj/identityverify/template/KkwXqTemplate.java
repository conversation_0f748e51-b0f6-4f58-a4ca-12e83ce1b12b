package com.xcwlkj.identityverify.template;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

/**
 * 空考位详情导出Excel模板
 * <AUTHOR>
 */
@Data
@ContentRowHeight(17)//内容部分行高
@HeadRowHeight(32)//表头行高
@HeadFontStyle(fontHeightInPoints = 12)//表头字体
//内容格式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,//居中
        borderBottom = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,//边框
        borderRight = BorderStyleEnum.THIN,borderLeft = BorderStyleEnum.THIN)
public class KkwXqTemplate {
    
    /**
     * 考场号
     */
    @ColumnWidth(15)
    @ExcelProperty({"考场号"})
    private String kch;
    
    /**
     * 场所名称
     */
    @ColumnWidth(20)
    @ExcelProperty({"场所名称"})
    private String csmc;
    
    /**
     * 准考证号
     */
    @ColumnWidth(18)
    @ExcelProperty({"准考证号"})
    private String zkzh;
    
    /**
     * 姓名
     */
    @ColumnWidth(12)
    @ExcelProperty({"姓名"})
    private String xm;
    
    /**
     * 座位号
     */
    @ColumnWidth(10)
    @ExcelProperty({"座位号"})
    private String zwh;
    
    /**
     * 异常描述
     */
    @ColumnWidth(18)
    @ExcelProperty({"异常描述"})
    private String yclx;
    
    /**
     * 终端上报时间
     */
    @ColumnWidth(20)
    @ExcelProperty({"终端上报时间"})
    private String zdsbsj;
    
    /**
     * 逻辑考场号
     */
    @ColumnWidth(15)
    @ExcelProperty({"逻辑考场号"})
    private String ljkcbh;
}