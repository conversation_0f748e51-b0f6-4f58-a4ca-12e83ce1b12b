package com.xcwlkj.identityverify.provincePlatform.request.items;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HyxxItem implements Serializable {

    /** 证件号码 */
    private String zjhm;

    /** 姓名 */
    private String xm;

    /** 验证时间yyyy-MM-dd HH:mm:ss */
    private Date yzsj;

    /** 核验结果 1-通过  0-不通过 */
    private String hyjg;

    /** 准考证号 */
    private String zkzh;

    /** 考点代码 */
    private String kddm;

    /** 考场号 */
    private String kch;

    /** 刷证结果 1-通过  0-不通过 */
    private String szjg;

    /** 人脸识别结果 1-通过  0-不通过 */
    private String rlsbjg;

    /** 指纹认证结果 1-通过  0-不通过 */
    private String zwrzjg;
}
