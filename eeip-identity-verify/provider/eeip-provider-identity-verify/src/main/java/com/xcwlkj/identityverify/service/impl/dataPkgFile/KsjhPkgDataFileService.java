package com.xcwlkj.identityverify.service.impl.dataPkgFile;

import com.xcwlkj.identityverify.model.domain.KsKcxx;
import com.xcwlkj.identityverify.model.domain.KsKsjh;
import com.xcwlkj.identityverify.model.dos.KsKsjh4PkgDO;
import com.xcwlkj.identityverify.model.dos.PkgDOUtil;
import com.xcwlkj.identityverify.model.enums.KsPkgFileEnum;
import com.xcwlkj.identityverify.service.KsKsjhService;
import com.xcwlkj.identityverify.service.PkgDataFileService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class KsjhPkgDataFileService implements PkgDataFileService<KsKsjh4PkgDO> {

    @Resource
    private KsKsjhService ksKsjhService;

    private final Set<String> ignoreFields;

    public KsjhPkgDataFileService() {
        Set<String> ignoreFields = new HashSet<>();
        ignoreFields.add("jhms");
        ignoreFields.add("cssbbh");
        ignoreFields.add("cssbmy");
        ignoreFields.add("qysbbmd");
        ignoreFields.add("wifiqy");
        this.ignoreFields = ignoreFields;
    }

    @Override
    public KsPkgFileEnum getPkgFileType() {
        return KsPkgFileEnum.KSJH;
    }

    @Override
    public List<KsKsjh4PkgDO> dataAcquire(String ksjhbh, KsKcxx kcxx) {
        KsKsjh ksjh = ksKsjhService.selectSingleByKey(ksjhbh);
        KsKsjh4PkgDO ksjhPkg =  PkgDOUtil.format(ksjh);

//            CssbxxVO cssbxxVO = sbglXcmcFeignApi.getCssbxx().getResult();
//            ksjhPkg.setCssbbh(cssbxxVO.getSbbh());
//            ksjhPkg.setCssbmy(cssbxxVO.getSbmy());

        return Collections.singletonList(ksjhPkg);
    }

    @Override
    public List<KsKsjh4PkgDO> dataAcquire(String ksjhbh) {
        return dataAcquire(ksjhbh, null);
    }

    @Override
    public Set<String> getIgnoreFields(String ksjhbh) {
        return ignoreFields;
    }
}
