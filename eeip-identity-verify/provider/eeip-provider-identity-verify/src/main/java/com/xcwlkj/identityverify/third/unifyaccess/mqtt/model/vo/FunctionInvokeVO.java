package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {
 *  "messageId":"1697432795753844736",
 *  "deviceId":"CENCGW100_S01MA00BBA000D1005E",
 *  "headers":{
 *  "useTask":true,
 *  "async":true,
 *  "productId":"CENCGW100_S",
 *  "batchId":"",
 *  "deviceName":"校级网关设备6",
 *  "taskId":"3e89ba80-1e7c-4e7a-8370-13b325111b46",
 *  "send-from":"jetlinks-platform:8844"
 *  },
 *  "timestamp":1693552264336,
 *  "functionId":"NOTIFY_XFKSJH",
 *  "inputs":[
 *  {
 *  "name":"Data",
 *  "value": {
 *   "functionId": "NOTIFY_XFKSJH",
 *   "messageType": "INVOKE_FUNCTION",
 *   "inputs": [
 *     {
 *       "name": "Data",
 *       "value": {
 *         "cc": [
 *           {
 *             "ccjssj": "2023-08-01 11:30:00",
 *             "yxcdsj": "",
 *             "ccbh": "11",
 *             "name": "第一天第一场(综合应用能力)",
 *             "cckssj": "2023-08-01 09:00:00",
 *             "kmbh": "38",
 *             "kmmc": "综合应用能力"
 *           },
 *           {
 *             "ccjssj": "2023-12-31 17:00:00",
 *             "yxcdsj": "",
 *             "ccbh": "12",
 *             "name": "第一天第二场(申论)",
 *             "cckssj": "2023-08-01 14:00:00",
 *             "kmbh": "37",
 *             "kmmc": "申论"
 *           }
 *         ],
 *         "ksksrq": "2023-08-01",
 *         "jkxxqy": "0",
 *         "sbjm": "1",
 *         "secret": {
 *           "enable": "true",
 *           "key": "gxhy@123",
 *           "algorithm": "zip"
 *         },
 *         "ksbh": "1001202301",
 *         "ksmc": "2023年8月公务员考试",
 *         "ksjsrq": "2023-12-31",
 *         "fileList": [
 *           {
 *             "fileUrl": "http://************:8811/remote/file/zjkszhpt/private/23083011135501762993778513154049.zip?expire=1693451865689&sign=e730a34b5a5489f32e39eedb5645ce8b",
 *             "fileMd5": "9222f7042d513ee4c5ad9cebb5097e0b",
 *             "type": "ks",
 *             "hashType": "md5",
 *             "version": "8"
 *           },
 *           {
 *             "fileUrl": "http://************:8811/remote/file/zjkszhpt/private/23083011135701762993790978625537.zip?expire=1693451865696&sign=a2e64e36b2c8eaa77e6e7901dcd5d794",
 *             "fileMd5": "a53dc4592dc595e209ebfcd75f8ac55d",
 *             "type": "common",
 *             "hashType": "md5",
 *             "version": "7"
 *           },
 *           {
 *             "fileUrl": "http://************:8811/remote/file/zjkszhpt/private/23083011135801762993798796808193.zip?expire=1693451865702&sign=61e485d889a654bebe12e0953711a1c7",
 *             "fileMd5": "fc876f3c3296b139d8b233c0a0888e09",
 *             "type": "kdkszp",
 *             "hashType": "md5",
 *             "version": "10"
 *           }
 *         ],
 *         "taskId": "23083011174501762995704982149120",
 *         "yzms": "1"
 *       }
 *     }
 *   ],
 *   "messageId": "1696723857827794944",
 *   "deviceId": "CENCGW100_S01MA00BBA000D1005E",
 *   "timestamp": 1693365469335
 * } }], "messageType":"INVOKE_FUNCTION"
 * }
 */
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class FunctionInvokeVO {

    @JsonProperty("messageId")
    private String messageId;
    @JsonProperty("deviceId")
    private String deviceId;
    @JsonProperty("headers")
    private HeadersDTO headers;
    @JsonProperty("timestamp")
    private Long timestamp;
    @JsonProperty("functionId")
    private String functionId;
    @JsonProperty("inputs")
    private List<InputsDTO> inputs;
    @JsonProperty("messageType")
    private String messageType;

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class HeadersDTO {
        @JsonProperty("useTask")
        private Boolean useTask;
        @JsonProperty("async")
        private Boolean async;
        @JsonProperty("productId")
        private String productId;
        @JsonProperty("batchId")
        private String batchId;
        @JsonProperty("deviceName")
        private String deviceName;
        @JsonProperty("taskId")
        private String taskId;
        @JsonProperty("send-from")
        private String sendfrom;
    }

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class InputsDTO {
        @JsonProperty("name")
        private String name;
        @JsonProperty("value")
        private ValueDTO value;

        @NoArgsConstructor
        @Data
        @JsonIgnoreProperties(ignoreUnknown=true)
        public static class ValueDTO {
            @JsonProperty("cc")
            private List<CcDTO> cc;
            @JsonProperty("ksksrq")
            private String ksksrq;
            @JsonProperty("jkxxqy")
            private String jkxxqy;
            @JsonProperty("sbjm")
            private String sbjm;
            @JsonProperty("secret")
            private SecretDTO secret;
            @JsonProperty("ksbh")
            private String ksbh;
            @JsonProperty("ksmc")
            private String ksmc;
            @JsonProperty("ksjsrq")
            private String ksjsrq;
            @JsonProperty("fileList")
            private List<FileListDTO> fileList;
            @JsonProperty("taskId")
            private String taskId;
            @JsonProperty("yzms")
            private String yzms;

            @NoArgsConstructor
            @Data
            @JsonIgnoreProperties(ignoreUnknown=true)
            public static class SecretDTO {
                @JsonProperty("enable")
                private String enable;
                @JsonProperty("key")
                private String key;
                @JsonProperty("algorithm")
                private String algorithm;
            }

            @NoArgsConstructor
            @Data
            @JsonIgnoreProperties(ignoreUnknown=true)
            public static class CcDTO {
                @JsonProperty("ccjssj")
                private String ccjssj;
                @JsonProperty("yxcdsj")
                private String yxcdsj;
                @JsonProperty("ccbh")
                private String ccbh;
                @JsonProperty("name")
                private String name;
                @JsonProperty("cckssj")
                private String cckssj;
                @JsonProperty("kmbh")
                private String kmbh;
                @JsonProperty("kmmc")
                private String kmmc;
            }

            @NoArgsConstructor
            @Data
            @JsonIgnoreProperties(ignoreUnknown=true)
            public static class FileListDTO {
                @JsonProperty("fileUrl")
                private String fileUrl;
                @JsonProperty("fileMd5")
                private String fileMd5;
                @JsonProperty("type")
                private String type;
                @JsonProperty("hashType")
                private String hashType;
                @JsonProperty("version")
                private String version;
            }
        }
    }
}
