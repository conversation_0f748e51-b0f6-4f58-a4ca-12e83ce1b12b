package com.xcwlkj.identityverify.service.impl.mqttHandler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xcwlkj.biz.unifyaccess.mqtt.service.IMessageService;
import com.xcwlkj.identityverify.model.dto.sbsj.CjYdzzAppFfRwDTO;
import com.xcwlkj.identityverify.service.CsXxjbxxService;
import com.xcwlkj.identityverify.service.sbsj.BizCsxxExtService;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.JetlinksWrapper;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.req.DeviceFirewareReqModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.req.DeviceProductReqModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.req.FirewareAddReqModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.resp.DeviceFirewareRespModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.resp.DeviceProductRespModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.resp.FirewareAddRespModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.resp.JetlinksPageRespModel;
import com.xcwlkj.identityverify.third.unifyaccess.http.service.UnifyAccessService;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttFuncInvokeEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttUnifyAccessEventHandlerEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.AppGjjsVO;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.FunctionInvokeAppGjjsVO;
import com.xcwlkj.identityverify.util.AccessJsonUtils;
import com.xcwlkj.identityverify.util.HsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MqttYdzdgjjsEventHandler extends MqttBaseEventHandler {

    @Autowired
    private IMessageService messageService;
    @Resource
    private CsXxjbxxService csXxjbxxService;
    @Resource
    private BizCsxxExtService bizCsxxExtService;
    @Resource
    private UnifyAccessService unifyAccessService;

    @Value("${xc.temp.path}")
    private String tempPath;

    boolean mark = false;

    @Override
    public void handleFuncInvoke(String productId, String deviceId, MqttFuncInvokeEnum funcInvokeEnum, String msg) {
        ObjectMapper objectMapper = new ObjectMapper();
        AppGjjsVO appGjjsVO = null;
        try {
            FunctionInvokeAppGjjsVO functionInvokeVO = objectMapper.readValue(msg, FunctionInvokeAppGjjsVO.class);
            appGjjsVO = functionInvokeVO.formatToAppGjjsVO();

        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        String xjjrFileUrl = null;
        if (appGjjsVO != null) {
            xjjrFileUrl = buildResult(appGjjsVO);
            String sendMsg = createGjjsResult(appGjjsVO, mark ? "1" : "-1");
            doSendUpperHsPlatYdzdgjjsResult(sendMsg, funcInvokeEnum);
        }
        if (mark && appGjjsVO != null) {
            log.info("开始分发固件到各考场网关");
            //下载成功以后成功以后进行分发，即发送获取移动终端app固件版本消息到考场网关
            CjYdzzAppFfRwDTO dto = new CjYdzzAppFfRwDTO();
            dto.setFffs("2");
            dto.setYdzzAppBb(appGjjsVO.getVersion());
            dto.setDescription("省级平台下发到校级平台");
            dto.setYdzzAppSign(appGjjsVO.getSign());
            dto.setYdzzAppSignMethod(appGjjsVO.getSignMethod());
            dto.setYdzzAppVersion(appGjjsVO.getVersion());
            String versionOrder = appGjjsVO.getVersionOrder();
            dto.setYdzzAppVersionOrder(versionOrder.matches("-?\\d+(\\.\\d+)?") ? Integer.parseInt(versionOrder) : 0);
            dto.setYdzzAppUrl(xjjrFileUrl);
            dto.setYdzzAppPackageName(appGjjsVO.getPackageName());
            bizCsxxExtService.cjYdzzAppFfRw(dto);
        }
    }

    @Override
    protected HanleResult doHanleEvent(String productId, Object req, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr) {
        return null;
    }

    @Override
    protected MqttUnifyAccessEventHandlerEnum getEventHandlerType() {
        return MqttUnifyAccessEventHandlerEnum.YDZDGJJS_EVENT_HANDLER;
    }

    public void doSendUpperHsPlatYdzdgjjsResult(String msg, MqttFuncInvokeEnum devEventEnum) {
        log.info("发送移动终端固定下载完成消息到上级平台[{}]", msg);
        messageService.sendToMqtt(msg, HsUtils.buildEventTopic(devEventEnum.getCode()));
    }

    /**
     * {
     *     "headers": {
     *         "batchId": "2025-07-28 14:20:08",
     *         "productId": "CENCGW100_S",
     *         "deviceName": "校级核验平台_阿里云",
     *         "taskId": "14a4326f-8f92-497c-8fb9-7d85a4a779e3"
     *     },
     *     "functionId": "NOTIFY_XFYDZDGJ",
     *     "messageType": "INVOKE_FUNCTION",
     *     "inputs": [
     *         {
     *             "name": "Data",
     *             "value": {
     *                 "versionOrder": "28",
     *                 "sign": "12121212",
     *                 "messageId": "2507281420081399396012333727744",
     *                 "version": "1.5.0",
     *                 "parameters": {
     *                     "packageName": "com.hisome.gxkdicv"
     *                 },
     *                 "signMethod": "MD5",
     *                 "url": "http://47.111.5.196:8811/remote/file/zjkszhpt/default/23121309325001839044353910116353.apk",
     *                 "timestamp": 1753683608970
     *             }
     *         }
     *     ],
     *     "messageId": "1949716484218183680",
     *     "deviceId": "CENCGW100_SIVb770d1a6a1ec5b44",
     *     "timestamp": 1753683608475
     * }
     *
     * @param appGjjsVO 接收上级下发的固件的信息
     * @return 固件上传到校级接入的url
     */
    public String buildResult(AppGjjsVO appGjjsVO) {
        String result = null;
        try {
            JSONObject productIdJson = new JSONObject();
            productIdJson.put("column", "productId");
            productIdJson.put("value", "HISOME_ANDROID");
            JSONObject versionJson = new JSONObject();
            versionJson.put("column", "version");
            versionJson.put("value", appGjjsVO.getVersion());

            JSONArray jsonArray = new JSONArray();
            jsonArray.add(productIdJson);
            jsonArray.add(versionJson);

            JetlinksWrapper<JetlinksPageRespModel<DeviceFirewareRespModel>> wrapper = unifyAccessService.queryPageDeviceFireware(new DeviceFirewareReqModel(0, 999, jsonArray));
            if (wrapper != null && Objects.equals(wrapper.getCode(), "200")) {
                List<DeviceFirewareRespModel> list = wrapper.getResult().getData();
                if (CollectionUtils.isEmpty(list)) {
                    String filePathName = downloadFileFromHttp(appGjjsVO.getUrl(), tempPath);
                    File file = new File(filePathName);
                    if (file.exists()) {
                        JetlinksWrapper<String> fileUrlWrapper = unifyAccessService.fileUpload(file);
                        if (fileUrlWrapper != null && Objects.equals(fileUrlWrapper.getCode(), "200") && StringUtils.isNotBlank(fileUrlWrapper.getResult())) {
                            String fileUrl = fileUrlWrapper.getResult();

                            Map<String, String> productMap = new HashMap<>();
                            JetlinksWrapper<JetlinksPageRespModel<DeviceProductRespModel>> productWrapper = unifyAccessService.queryPageDeviceProduct(new DeviceProductReqModel(0, 999));
                            if (productWrapper != null && Objects.equals(productWrapper.getCode(), "200")) {
                                if (CollectionUtils.isEmpty(productWrapper.getResult().getData())) {
                                    productMap = productWrapper.getResult().getData().stream().collect(Collectors.toMap(DeviceProductRespModel::getId, DeviceProductRespModel::getName, (oldVal, newVal) -> newVal));
                                }
                            }

                            FirewareAddReqModel reqModel = new FirewareAddReqModel();
                            reqModel.setProductId(appGjjsVO.getDeviceId());
                            reqModel.setProductName(productMap.getOrDefault(appGjjsVO.getDeviceId(), appGjjsVO.getDeviceId()));
                            reqModel.setName("省级平台下发到校级平台固件 " + appGjjsVO.getVersion());
                            reqModel.setVersion(appGjjsVO.getVersion());
                            String versionOrder = appGjjsVO.getVersionOrder();
                            reqModel.setVersionOrder(versionOrder.matches("-?\\d+(\\.\\d+)?") ? Integer.parseInt(versionOrder) : 0);
                            reqModel.setUrl(fileUrl);
                            reqModel.setSign(appGjjsVO.getSign());
                            reqModel.setSignMethod(appGjjsVO.getSignMethod());
                            reqModel.setSize((int) file.length());
                            if (StringUtils.isNotBlank(appGjjsVO.getPackageName())) {
                                List<FirewareAddReqModel.Propertie> properties = new ArrayList<>(2);
                                properties.add(new FirewareAddReqModel.Propertie() {{
                                    setId("packageName");
                                    setValue(appGjjsVO.getPackageName());
                                }});
                                reqModel.setProperties(properties);
                            }
                            JetlinksWrapper<FirewareAddRespModel> firewareAddWrapper = unifyAccessService.firewareAdd(reqModel);
                            if (firewareAddWrapper != null && Objects.equals(firewareAddWrapper.getCode(), "200") && null != firewareAddWrapper.getResult()) {
                                result = firewareAddWrapper.getResult().getUrl();
                                mark = true;
                            }
                        }
                        file.delete();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    //根据上级平台下发的文件url下载文件
    public static String downloadFileFromHttp(String fileUrl, String saveDir) throws IOException {
        log.info("根据上级平台下发的文件url下载文件");
        // 1. 创建URL对象
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        // 2. 检查HTTP响应码（200表示成功）
        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw new IOException("服务器返回非200状态: " + responseCode);
        }

        // 3. 获取文件名（优先从Content-Disposition头提取，否则从URL解析）
        String fileName;
        String disposition = connection.getHeaderField("Content-Disposition");

        if (disposition != null && disposition.contains("filename=")) {
            fileName = disposition.split("filename=")[1].replace("\"", "");
        } else {
            fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
        }

        // 4. 构建保存路径
        String savePath = Paths.get(saveDir, fileName).toString();

        // 5. 读取输入流并写入文件
        try (BufferedInputStream in = new BufferedInputStream(connection.getInputStream());
             FileOutputStream out = new FileOutputStream(savePath)) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }
        log.info("文件已下载至: {}", savePath);
        return savePath;
    }

    /**
     * {
     *     "data": {
     *         "TIMESTAMP": "1753257256000",
     *         "Data": {
     *             "messageId": "1947927612782043136",
     *             "orgCode": "K330100030",
     *             "sn": "CENCGW800_S03MC11BDJ000099",
     *             "devType": "170",
     *             "version": "1.8.9",
     *             "versionOrder": "61",
     *             "status": "1",
     *             "parameters": {
     *                 "packageName": "com.hisome.gxicv"
     *             }
     *         },
     *         "OPERATION": "NOTIFY_XFYDZDGJ"
     *     },
     *     "deviceId": "CENCGW800_S03MC11BDJ000099",
     *     "timestamp": 1753257256000
     * }
     * 创建固件接收结果返回字符串
     * @param xzzt 下载状态
     * @param appGjjsVO 固件信息
     */
    private String createGjjsResult(AppGjjsVO appGjjsVO, String xzzt) {
        String bzhkdid = csXxjbxxService.getBzhkdid();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("messageId", appGjjsVO.getMessageId());
        jsonObject.put("orgCode", bzhkdid);
        jsonObject.put("sn", appGjjsVO.getDeviceId());
        jsonObject.put("devType", "170");
        jsonObject.put("status", xzzt);
        jsonObject.put("version", appGjjsVO.getVersion());
        jsonObject.put("versionOrder", appGjjsVO.getVersionOrder());
        JSONObject packageNameJson = new JSONObject();
        packageNameJson.put("packageName", appGjjsVO.getPackageName());
        jsonObject.put("parameters", packageNameJson);
        JSONObject object = AccessJsonUtils.generateAccessJsonV2("NOTIFY_XFYDZDGJ", jsonObject);
        JSONObject result = new JSONObject();
        result.put("data", object);
        result.put("deviceId", appGjjsVO.getDeviceId());
        result.put("timestamp", System.currentTimeMillis() / 1000);
        return result.toJSONString();
    }
}