package com.xcwlkj.identityverify.third.unifyaccess.http.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/**
 * 对接统一接入平台URL地址
 * <AUTHOR>
 *
 */
public enum UnifyAccessUrlEnum {

	login("/remote/access/user/login","登录")
	,validate("/remote/access/device/validate","设备验签")
	,sendCommand("/remote/access/device/sendCommand","下发给设备命令")
	,batchCommand("/remote/access/device/batchCommand","批量下发给设备指令")//listByDeviceNames
	,queryDeviceStatus("/remote/access/device/listByDeviceNames","根据设备序列号数组查询设备状态")
	,queryBatchCommandResult("/remote/access/device/batchSendCommandResult","批次命令下发结果查询")

    ,jetlinksLogin("/authorize/login","jetlinks登录")
    ,jetlinksQueryPageDeviceFireware("/device/firmware/_query","jetlinks设备固件版本信息分页查询")
    ,jetlinksQueryDeviceFirewareUpgrade("/firmware/upgrade/history/_query","jetlinks设备固件升级状态查询")
    ,queryPageDeviceProduct("/device/product/_query","设备产品信息分页查询")
    ,queryPageDeviceFireware("/firmware/_query","设备固件版本信息分页查询")
    ,createFirmwareUpgradeTask("/firmware/upgrade/task","创建设备固件升级任务")
    ,publishUpgradeTaskToDevice("/firmware/upgrade/task/{id}/_deploy","发布升级任务到指定设备")
    ,pushUpgradeByTaskId("/firmware/upgrade/task/{id}/pushUpgrade","根据任务ID推送升级")

    ,deviceInstance("/device/instance/{id}","根据ID查询")

    ,emqxSubscribersAdd("/remote/access/emqx/subscribers/add","Emqx消息桥接添加订阅")
    ,emqxSubscribersDel("/remote/access/emqx/subscribers/del","Emqx消息桥接删除订阅")
    ,emqxSubscribersList("/remote/access/emqx/subscribers/list","Emqx消息桥接查询订阅")

    ,deviceCategory("/device/category","productId字典信息")

    ,fileUpload("/file/static","文件上传到接入")
    ,firmwareAdd("/firmware","固件新增")
    ;
	
	private String code;
    private String desc;

    private UnifyAccessUrlEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static UnifyAccessUrlEnum get(String code) {
        for (UnifyAccessUrlEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }
}
