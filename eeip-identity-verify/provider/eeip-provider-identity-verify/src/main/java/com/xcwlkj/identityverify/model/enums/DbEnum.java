package com.xcwlkj.identityverify.model.enums;

public enum DbEnum {

    MYSQL("jdbc:mysql://","mysql")
    ,DM("jdbc:dm://","dm")
    ;
    private String code;
    private String desc;

    private DbEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
