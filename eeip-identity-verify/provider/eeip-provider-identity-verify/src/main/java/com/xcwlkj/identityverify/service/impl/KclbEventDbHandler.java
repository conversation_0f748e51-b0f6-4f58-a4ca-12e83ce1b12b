package com.xcwlkj.identityverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xcwlkj.identityverify.mapper.KsKcxxMapper;
import com.xcwlkj.identityverify.model.domain.KsKcxx;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.KclbQueryReq;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.resp.KclbQueryResp;
import com.xcwlkj.model.enums.ScztEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 处理mqtt考考场列表事件处理功能
 */
@Slf4j
@Service
public class KclbEventDbHandler {

    @Resource
    private KsKcxxMapper ksKcxxMapper;

    public Object kcqkQuery(KclbQueryReq kclbQueryReq) {
        log.info("mqtt考场列表查询消息:{}", kclbQueryReq.getData().toString());
        KclbQueryResp result = new KclbQueryResp();
        KclbQueryReq req = kclbQueryReq.getData();
        if (null == req) {
            log.info("处理mqtt考场列表查询消息内容为空");
            return result;
        }
        String ksjh = req.getKsjh();
        String kscc = req.getKscc();

        Example example = new Example(KsKcxx.class);
        example.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode()).andEqualTo("ksjhbh", ksjh).andEqualTo("ccm", kscc);
        List<KsKcxx> kcxxList = ksKcxxMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(kcxxList)) {
            List<KclbQueryResp.KcxxItemVO> kclb = new ArrayList<>(kcxxList.size());
            KclbQueryResp.KcxxItemVO vo;
            for (KsKcxx kcxx : kcxxList) {
                vo = new KclbQueryResp.KcxxItemVO();
                vo.setBzhkcid(kcxx.getBzhkcid());
                vo.setBzhkcmc(kcxx.getBzhkcmc());
                vo.setKcbh(kcxx.getKcbh());
                vo.setLjkcbh(kcxx.getLjkcbh());
                kclb.add(vo);
            }
            result.setKclb(kclb);
        }
        log.info(JSON.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty));
        return result;
    }
}
