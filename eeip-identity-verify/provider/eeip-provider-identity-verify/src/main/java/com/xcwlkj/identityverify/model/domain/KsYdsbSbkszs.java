/**
 * xcwlkj.com Inc.
 * Copyright (c) 2025-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.domain;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * 移动终端上报考生总数
 * 
 * <AUTHOR>
 * @version $Id: KsYdsbSbkszs.java, v 0.1 2025年02月07日 13时54分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "ks_ydsb_sbkszs")
public class KsYdsbSbkszs implements Serializable {

    /** 序列id */
    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "id")
    private String   id;
    /** 考试计划编号 */
    @Column(name = "ksjhbh")
    private String            ksjhbh;
    /** 设备序列号 */
    @Column(name = "sbxlh")
    private String            sbxlh;
    /** 上报时间 */
    @Column(name = "sbsj")
    private Date            sbsj;
    /** 考点名称 */
    @Column(name = "kdmc")
    private String            kdmc;
    /** 上报考生总数=入场人数+缺考人数 */
    @Column(name = "sbkszs")
    private Integer            sbkszs;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;
    /** 场次码 */
    @Column(name = "ccm")
    private String            ccm;
    /** 考场编号 */
    @Column(name = "kcbh")
    private String            kcbh;
    /** 逻辑考场编号 */
    @Column(name = "ljkcbh")
    private String            ljkcbh;
    /** 上报类型 BZ-标准 FB-非标 */
    @Column(name = "sblx")
    private String            sblx;
    /** 上报标志 0-未上报 1-已上报 */
    @Column(name = "report_flag")
    private String            reportFlag;
    /** 上报时间 */
    @Column(name = "report_time")
    private Date            reportTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


