package com.xcwlkj.identityverify.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/**
 * 网关标签生成文件类型 枚举
 */
public enum WgbqFileTypeEnum {

    PDF("pdf","pdf文件",".pdf"),
    EXCEL("excel","EXCEL文件",".xlsx");

    private String code;

    private String msg;

    private String suffix;


    /**
     * @param code 结果码
     * @param msg 描述信息
     */
    WgbqFileTypeEnum(String code, String msg, String suffix) {
        this.msg = msg;
        this.code = code;
        this.suffix = suffix;
    }

    public static WgbqFileTypeEnum get(String code) {
        for (WgbqFileTypeEnum c : values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }

    public String getMsg() {
        return msg;
    }

    public String getCode() {
        return code;
    }

    public String getSuffix() {
        return suffix;
    }
}
