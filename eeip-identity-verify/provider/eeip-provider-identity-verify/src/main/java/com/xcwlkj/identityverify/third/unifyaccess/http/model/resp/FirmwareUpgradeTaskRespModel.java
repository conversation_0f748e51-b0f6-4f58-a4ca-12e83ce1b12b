package com.xcwlkj.identityverify.third.unifyaccess.http.model.resp;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

@Data
public class FirmwareUpgradeTaskRespModel {

    /** 任务id */
    private String id;
    /** 任务名称 */
    private String name;
    /** 创建时间 */
    private String createTime;
    /** 产品ID */
    private String productId;
    /** 固件ID */
    private String firmwareId;
    /** 说明 */
    private String description;
    /** 升级超时时间 */
    private int timeoutSeconds;
    /** 升级方式,可用值:{text=设备拉取, value=pull},{text=平台推送, value=push} */
    private JSONObject mode;
}
