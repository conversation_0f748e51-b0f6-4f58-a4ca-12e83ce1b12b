server:
  port: 9173
  tomcat:
    remote-ip-header: x-forward-for
    uri-encoding: UTF-8
    accept-count: 1000
    max-threads: 1000
    max-connections: 2000
    max-http-header-size: 8096
    #核心代码，设置tomcat的basedir 
    basedir: ${user.home}/tomcat/tmp
  use-forward-headers: true
spring:
  application:
    name: identityverify-service
  main:
    allow-bean-definition-overriding: true
  datasource:
    #url: ******************************************************************************
    #username: root
    #password: cd_hisome
    #driver-class-name: com.mysql.jdbc.Driver
    url: jdbc:dm://*************:30236/EEIP_ALONE?characterEncoding=utf8
    username: EEIP_ALONE
    password: hI$ome123456
    driver-class-name: dm.jdbc.driver.DmDriver
  #redis配置
  redis:
    cluster:
      # redis 集群地址配置，和单机配置只要配置一个即可，不配置为空,集群配置优先
      #      nodes: redis1.xccloud.com:7001,redis1.xccloud.com:7004,redis2.xccloud.com:7002,redis2.xccloud.com:7005,redis3.xccloud.com:7003,redis3.xccloud.com:7006
      nodes:
      #redis 单机配置
      host: 127.0.0.1
      port: 6379
      passwd: cd_hisome
      timeOut: 5000
      #跨集群执行命令时要遵循的最大重定向数量
      max-redirects: 3
      #缓存注解(@Cacheable、@CacheEvict等)缓存的有效时间,默认180秒
      expire-time: 180
  servlet:
    multipart:
      max-file-size: 100MB #单个文件大小 Max file size默认1M
      max-request-size: 100MB #总上传的数据大小 Max request size默认10M
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'

#feign配置
feign:
  hystrix:
    enabled: true
  # feign 使用okhttp3 作为客户端连接
  httpclient:
    enabled: false
  okhttp:
    enabled: true
####超时配置####
hystrix:
  threadpool:
    default:
      coreSize: 10 #默认为10,基本得原则时保持线程池尽可能小，他主要是为了释放压力，防止资源被阻塞
      maxQueueSize: 1500 #最大排队长度。默认-1,不能动态调整
      #动态控制线程池队列的上限，即使maxQueueSize没有达到，达到queueSizeRejectionThreshold该值后，请求也会被拒绝，默认值5
      queueSizeRejectionThreshold: 1200
  command:
    default:
      execution:
        isolation:
          strategy: THREAD #HystrixCommand.run()的隔离策略，THREAD和SEMAPHORE
          thread:
            timeoutInMilliseconds: 230000
        timeout:
          enabled: true
ribbon:
  #请求处理的超时时间 下级服务响应最大时间,超出时间消费方（路由也是消费方）返回timeout,超时时间不可大于断路器的超时时间
  ReadTimeout: 50000
  #ribbon请求连接的超时时间- 限制5秒内必须请求到服务，并不限制服务处理的返回时间
  ConnectTimeout: 5000
  # 对所有的操作请求都进行重试，如果是get则可以，如果是post,put等操作没有实现幂等的情况下是很危险的，所以设置为false
  OkToRetryOnAllOperations: false
  #对当前实例的重试次数
  MaxAutoRetries: 1
  #切换实例的重试次数
  MaxAutoRetriesNextServer: 1
  # ribbon 使用okhttp3 作为客户端连接
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  ServerListRefreshInterval: 5000 #负载均衡器服务列表缓存
  eureka:
    enabled: true
###超时配置###
mybatis:
  type-aliases-package: com.xcwlkj.identityverify.model.domain
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
mapper:
  mappers: tk.mybatis.mapper.common.Mapper
  not-empty: false
  identity: MYSQL

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

xc:
  platForm:
    platName: ZGD
  enableSqlLogInterceptor: false
  zk:
    isOn: 1
    zkAddressList: 127.0.0.1:2181
  xcDfs:
    prefixUrl: 'http://fastdfs.xccloud.com:9999'
    channel: SJCZ #渠道名
    serviceAddress: http://************:8811
    serviceInnerAddress: http://************:8811
    appId: 4cc1dc4bf33e09d3785f6e6af7eca295
    appSecret: 1d83458ab19d56373be5b1e275f586e0
    expiredMinute: 5
    fileObjectPrefix: ${xc.xcDfs.serviceAddress}/remote/dfs/
    filePathPrefix: ${xc.xcDfs.serviceAddress}/remote/file/
    fileObjectInnerPrefix: ${xc.xcDfs.serviceInnerAddress}/remote/dfs/
    filePathInnerPrefix: ${xc.xcDfs.serviceInnerAddress}/remote/file/
    distributeExpireMinute: 1440
    filePathReflectPrefix: http://************:8811/remote/file/
  temp:
    path: 'E:/tool\'
    generatePath: 'E:/zip'
    webUrl: 'http://************:8000/upload/'
  mqtt:
    url: tcp://*************:2883
    #    url: tcp://**************:1883
    revTopics: /+/+/event/+
  tyjr:
    #urlPrefix: http://**************:8844
    urlPrefix: http://**************:8844
    appId: RDbKcP5Z3mGwxeJB
    accessKey: SNTiZ8mHKKhWFhNFApFRPMtk
    accessJsonType: 2

  identityVerify:
    distributePkg:
      tokenNum: 10
      tokenSupplyIntervalSec: 10
      max_retry_count: 1
    #文件上传
    wjsc:
      fileName:
        ksbmxx: ks.db
        jkryjbxx: jky.db
        jkrybpxx: jkybp.db
        sjjkzp: jkryzp.db
        sjkszp: kszp.db
        sjpzxx: pzxx.db
    pkg:
      # 考生数据打包路径
      path: "f:/tmp"
      secret:
        enable: true
        key: gxhy@123
        algorithm: zip
      threadCounts: 16
    wgcl:
      mb:
        keys: wgclPrcl,wgclKzks
    #省平台身份验证信息
    hisomeProvicePlat:
      appid: gxsfhy
      secret: 8efc68adb41744d1810d2b6527ed32c5

  #rocketMQ 配置
  rocketMq:
    enable: true
    reliableMessageConsumer: false #消费者是否使用可靠消息, 默认不使用 @MqProducerStore
    reliableMessageProducer: true #生产者是否使用可靠消息, 默认不使用 @MqConsumerStore
    instanceName: identityverify-service
    namesrvAddr: ************:9876 #服务地址
    consumerGroup: IDV #消费者组
    producerGroup: IDV #生产者组
  idGenerateKey: 1
  schedule:
    enable: false

HsFaceFeature:
  HsFaceFeatureCreatorPath: /mnt/data/eeip/HsFaceFeatureCreator
  HsFaceFeatureFilePath: /mnt/data/eeip/HsFaceFeatureFile
