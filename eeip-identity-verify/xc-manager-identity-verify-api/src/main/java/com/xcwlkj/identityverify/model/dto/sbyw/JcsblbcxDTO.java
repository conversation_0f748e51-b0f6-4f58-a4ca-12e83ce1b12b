/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.sbyw;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 基础设备(考场网关/电子班牌)列表查询dto
 * <AUTHOR>
 * @version $Id: JcsblbcxDTO.java, v 0.1 2023年10月07日 16时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JcsblbcxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 场所编号 */
    @NotBlank(message = "场所编号不能为空")
    private String csbh;
    /** 场所类型 */
    @NotBlank(message = "场所类型不能为空")
    private String cslx;
    /** 审核状态 */
    private String shzt;
    /** 序列号 */
    private String xlh;

    private String ipdz;
    /** 所在考场 */
    private String szkc;
    /** 设备名称 */
    private String sbmc;
    /** 在线状态 */
    private String zxzt;
    /** ntp是否启用 1-启用 0-禁用 */
    private String ntpEnable;
    /** ntp源 */
    private String ntpSource;
    /** 移动终端电量 1-高 0-低 */
    private String ydzddl;
    /**  */
    @NotBlank(message = "不能为空")
    private Integer pageNum;
    /**  */
    @NotBlank(message = "不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
