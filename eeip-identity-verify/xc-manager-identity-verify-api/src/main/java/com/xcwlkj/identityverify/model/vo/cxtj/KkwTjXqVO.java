package com.xcwlkj.identityverify.model.vo.cxtj;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 空考位详情VO
 * <AUTHOR>
 * @version $Id: KkwTjXqVO.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KkwTjXqVO implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** 考场号 */
    private String kch;
    /** 逻辑考场号 */
    private String ljkcbh;
    /** 场所名称 */
    private String csmc;
    
    /** 准考证号 */
    private String zkzh;
    
    /** 姓名 */
    private String xm;
    
    /** 座位号 */
    private String zwh;
    
    /** 异常描述 1-误识别 ，2-坐错他人位置 ，3-实际未参加考试， 4-他人坐错位置， 5-人工核验， 6-缺考（空位），7-无编排 */
    private String yclx;
    
    /** 终端上报时间 */
    private String zdsbsj;
    
    /** 上报上级平台情况 */
    private String sfsb;
    
    /** 上报上级平台上报时间 */
    private String sbsjsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
