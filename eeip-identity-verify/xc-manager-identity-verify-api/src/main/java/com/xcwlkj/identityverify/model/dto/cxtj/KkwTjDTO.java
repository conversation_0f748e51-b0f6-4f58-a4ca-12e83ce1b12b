package com.xcwlkj.identityverify.model.dto.cxtj;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 空考位统计查询DTO
 * <AUTHOR>
 * @version $Id: KkwTjDTO.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KkwTjDTO implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;

    /** 考试场次 */
    private String ccm;
    
    /** 终端上报情况 0-否 1-是 */
    private String sbzt;
    
    /** 页码 */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;
    
    /** 每页大小 */
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
