package com.xcwlkj.identityverify.model.req.sbyw;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class SbbqscReqModel extends RemoteReqBaseModel {
    private static final long serialVersionUID = -1L;
    /**
     * 场所编号
     */
    @NotBlank(message = "场所编号不能为空")
    private String csbh;
}
