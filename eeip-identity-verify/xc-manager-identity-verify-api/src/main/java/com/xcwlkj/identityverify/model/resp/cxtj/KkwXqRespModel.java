package com.xcwlkj.identityverify.model.resp.cxtj;

import com.github.pagehelper.PageInfo;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjXqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 空考位详情查询响应模型
 * <AUTHOR>
 * @version $Id: KkwXqRespModel.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwXqRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 分页数据 */
    private PageInfo<KkwTjXqVO> pageInfo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
