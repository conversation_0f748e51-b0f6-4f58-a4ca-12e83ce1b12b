/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.req.ksgl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import com.xcwlkj.identityverify.model.dto.ksgl.KcglgxItemDTO;
import java.util.List;
import javax.validation.constraints.NotBlank;


/**
 * 考场关系保存关联请求
 * <AUTHOR>
 * @version $Id: KcgxBcglReqModel.java, v 0.1 2023年10月26日 14时32分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KcgxBcglReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 考场关联关系 */
    @NotNull(message = "考场关联关系不能为空")
    private List<KcglgxItemDTO> kcglgx;
    /** 未关联考场标志集合 */
    @NotNull(message = "未关联考场编号集合不能为空")
    private List<String> wglkcbhs;
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 是否复制到其他场次：1/是；0/否 */
    private String sffzdqtcc;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}