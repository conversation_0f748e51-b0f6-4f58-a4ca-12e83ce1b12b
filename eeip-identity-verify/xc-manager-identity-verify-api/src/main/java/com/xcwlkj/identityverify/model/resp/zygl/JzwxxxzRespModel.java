/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.zygl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 建筑物信息新增响应
 * <AUTHOR>
 * @version $Id: JzwxxxzRespModel.java, v 0.1 2024年01月29日 16时37分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JzwxxxzRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}