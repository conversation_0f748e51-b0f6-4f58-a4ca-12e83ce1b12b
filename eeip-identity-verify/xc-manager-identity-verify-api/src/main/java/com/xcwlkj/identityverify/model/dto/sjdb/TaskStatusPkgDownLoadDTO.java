/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.sjdb;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 数据打包状态对应包下载dto
 * <AUTHOR>
 * @version $Id: TaskStatusPkgDownLoadDTO.java, v 0.1 2023年12月15日 10时00分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TaskStatusPkgDownLoadDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 打包状态id */
    @NotBlank(message = "打包状态id不能为空")
    private String id;
    /** 文件类型 */
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
