/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.vo.sysconfig;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * mqtt配置查询vo
 * <AUTHOR>
 * @version $Id: MqttpzcxItemVO.java, v 0.1 2025年02月12日 10时05分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class MqttpzcxItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** mqtt_ip-地址 mqtt_port-端口 mqtt_username-用户名 mqtt_password-密码 */
    private String code;
    /**  */
    private String name;
    /**  */
    private String value;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
