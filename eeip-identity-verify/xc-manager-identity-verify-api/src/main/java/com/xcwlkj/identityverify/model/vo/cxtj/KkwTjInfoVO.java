package com.xcwlkj.identityverify.model.vo.cxtj;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 空考位统计摘要信息VO
 * <AUTHOR>
 * @version $Id: KkwTjInfoVO.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KkwTjInfoVO implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** 总考场数 */
    private Integer total;
    
    /** 已上报考场数 */
    private Integer ysb;
    
    /** 未上报考场数 */
    private Integer wsb;
    
    /** 上报比例 */
    private String reportRate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
