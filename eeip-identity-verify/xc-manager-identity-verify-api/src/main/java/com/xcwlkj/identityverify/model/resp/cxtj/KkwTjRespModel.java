package com.xcwlkj.identityverify.model.resp.cxtj;

import com.github.pagehelper.PageInfo;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjItem;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjInfoVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 空考位统计查询响应模型
 * <AUTHOR>
 * @version $Id: KkwTjRespModel.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwTjRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 分页数据 */
    private PageInfo<KkwTjItem> pageInfo;
    
    /** 统计摘要 */
    private KkwTjInfoVO summary;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
