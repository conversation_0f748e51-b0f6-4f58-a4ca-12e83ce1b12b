package com.xcwlkj.identityverify.model.resp.cxtj;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * 空考位详情导出响应模型
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwXqdcRespModel extends RemoteRespBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;
    
    /** 导出文件路径 */
    private String dclj;
    
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}