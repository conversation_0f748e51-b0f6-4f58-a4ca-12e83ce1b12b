package com.xcwlkj.identityverify.model.vo.sbmlpz;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class SbmlpzXfXqItemVO implements Serializable {

    /** 场所编号 */
    private String csbh;
    /** 场所名称 */
    private String csmc;
    /** 设备类型 kdwg-考点网关，kcwg-考场网关 */
    private String sblx;
    /** IP地址 */
    private String ipdz;
    /** 在线状态 1-在线， 0-离线 */
    private String zxzt;
    /** 下发结果 -2-不在线，-1-超时，0-失败，1-下发中，2-成功 */
    private String xfjg;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
