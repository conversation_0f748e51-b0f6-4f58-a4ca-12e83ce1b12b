/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.cxtj;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 考场零考生入场查询dto
 * <AUTHOR>
 * @version $Id: KsrcZeroLbDTO.java, v 0.1 2025年02月06日 10时28分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KsrcZeroLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 场所名称 */
    private String csmc;
    /** 网关状态   -1 未安装、0 离线、1 在线、2 已安装) */
    private String wgzt;
    /** 移动终端状态   -1 未安装、0 离线、1 在线、2 已安装 */
    private String ydzdzt;
    /** 错误原因  1：网关设备不存在  2：网关不在线 3：USB未连接 4：移动终端未安装 5：移动终端不在线 6：移动终端APP状态异常 */
    private String errorType;
    /** 1：零考生考场  2：未上报完全考场 */
    @NotBlank(message = "1：零考生考场  2：未上报完全考场不能为空")
    private String type;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageNum;
    /** 页大小 */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
