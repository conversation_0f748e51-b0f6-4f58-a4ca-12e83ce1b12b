package com.xcwlkj.identityverify.model.req.cxtj;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 空考位详情查询请求模型
 * <AUTHOR>
 * @version $Id: KkwXqReqModel.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwXqReqModel extends RemoteReqBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    
    /** 考试场次 */
    private String ccm;
    
    /** 异常类型 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工核验 6-缺考（空位） 7-无编排 */
    private String yclx;
    
    /** 准考证号 */
    private String zkzh;
    
    /** 考场号 */
    private String kch;
    
    /** 页码 */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;
    
    /** 每页大小 */
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
