/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.sjsb;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * 数据上报事件列表dto
 * <AUTHOR>
 * @version $Id: SjsbsjlbDTO.java, v 0.1 2023年11月16日 15时09分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SjsbsjlbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 事件类型 1401-超时未报,1402-数据缺失,1403-设备故障 */
    private String sjlx;
    /** 处理状态 1-未处理,2-已处理 */
    private String clzt;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
