/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.zygl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.identityverify.model.vo.zygl.JsxxlbItemVO;
import java.util.List;


/**
 * 教室信息列表查询响应
 * <AUTHOR>
 * @version $Id: JsxxlbcxRespModel.java, v 0.1 2024年01月29日 13时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JsxxlbcxRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 教室信息列表 */
    private List<JsxxlbItemVO> jsxxlb;
    /**  */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}