/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.ksgl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 考场教室对应关系完整核验响应
 * <AUTHOR>
 * @version $Id: KcjsdygxwzhyRespModel.java, v 0.1 2024年11月13日 15时29分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KcjsdygxwzhyRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 状态  0：部分场次未完成对应 1：所有场次完成对应 */
    private String status;
    /** 未对应完整场次 */
    private List<String> wdywcccList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}