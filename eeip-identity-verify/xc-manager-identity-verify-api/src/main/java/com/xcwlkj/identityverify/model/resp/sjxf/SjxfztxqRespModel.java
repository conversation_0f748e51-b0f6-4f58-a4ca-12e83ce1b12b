/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.sjxf;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.identityverify.model.vo.sjxf.DataDistributeStatusXqItemVO;
import java.util.List;


/**
 * 数据下发状态详情响应
 * <AUTHOR>
 * @version $Id: SjxfztxqRespModel.java, v 0.1 2023年11月01日 09时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SjxfztxqRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 数据下发状态详情列表 */
    private List<DataDistributeStatusXqItemVO> dataDistributeStatusXqList;
    /** 总条数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}