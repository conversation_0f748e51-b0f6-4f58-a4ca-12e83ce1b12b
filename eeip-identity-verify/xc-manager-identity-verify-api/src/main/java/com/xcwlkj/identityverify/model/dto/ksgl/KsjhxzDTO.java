/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.ksgl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * 考试计划新增dto
 * <AUTHOR>
 * @version $Id: KsjhxzDTO.java, v 0.1 2023年09月20日 10时50分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KsjhxzDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;

    /** 名称 */
    private String mc;
    /** 开始时间 */
    private String kssj;
    /** 结束时间 */
    private String jssj;
    /** 学年 */
    private String xn;
    /** 学期 */
    private String xq;
    /** 监考人员基本数据 */
    private Boolean jkryjbsj;
    /** 监考人员编排数据 */
    private Boolean jkrybpsj;
    /** 监考签到启用 */
    private Boolean jkqdqy;
    /** 特征值启用 */
    private Boolean tzzqy;
    /** 考生照片上传启用 */
    private Boolean kszpscqy;
    /** 对比模式 1-1:1 2-1:N */
    private String dbms;
    /** 监考签到人数 */
    private String jkqdrs;
    /** 考生照片启用 */
    private Boolean kszpqy;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
