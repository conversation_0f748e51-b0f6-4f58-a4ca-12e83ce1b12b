package com.xcwlkj.identityverify.model.vo.cxtj;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 空考位统计列表项VO
 * <AUTHOR>
 * @version $Id: KkwTjItem.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KkwTjItem implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** 考场号 */
    private String kch;
    
    /** 场所名称 */
    private String csmc;
    
    /** 终端上报情况 0-否 1-是 */
    private String sfsb;
    
    /** 终端上报时间 */
    private String sbsj;
    
    /** 考场ID（用于详情查询） */
    private String bzhkcid;
    
    /** 逻辑考场编号 */
    private String ljkcbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
