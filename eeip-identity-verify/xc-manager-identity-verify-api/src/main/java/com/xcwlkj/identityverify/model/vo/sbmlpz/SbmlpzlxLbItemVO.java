package com.xcwlkj.identityverify.model.vo.sbmlpz;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class SbmlpzlxLbItemVO implements Serializable {

    /** 配置类型代码 */
    private String lxdm;
    /** 配置类型名称 */
    private String lxmc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
