/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.cxtj;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.identityverify.model.vo.cxtj.KsrcsjscItemVO;
import java.util.List;


/**
 * 考生入场数据上传列表响应
 * <AUTHOR>
 * @version $Id: KsrcsjsclbRespModel.java, v 0.1 2024年02月21日 16时39分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KsrcsjsclbRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 考生入场数据上传列表 */
    private List<KsrcsjscItemVO> ksrcsjscList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}