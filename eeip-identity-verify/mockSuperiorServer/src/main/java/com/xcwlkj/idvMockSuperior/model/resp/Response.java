package com.xcwlkj.idvMockSuperior.model.resp;

@lombok.Data
public class Response<T> {
    private String code;
    private T data;
    /**
     * 消息内容，当接口result不成功时，有可能说明具体的错误信息
     */
    private String message;
    private boolean result;

    public static <T> Response<T> success(T data) {
        Response<T> response = new Response<>();
        response.setCode("200");
        response.setData(data);
        response.setResult(true);
        return response;
    }

    public static <T> Response<T> fail(String message) {
        Response<T> response = new Response<>();
        response.setCode("-9999999");
        response.setMessage(message);
        response.setResult(false);
        return response;
    }
}