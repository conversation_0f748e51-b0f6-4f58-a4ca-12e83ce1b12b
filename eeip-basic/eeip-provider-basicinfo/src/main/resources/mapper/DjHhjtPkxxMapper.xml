<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.basicinfo.mapper.DjHhjtPkxxMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.basicinfo.model.domain.DjHhjtPkxx">
        <id column="zj" jdbcType="VARCHAR" property="zj" />
        <result column="ksjc" jdbcType="VARCHAR" property="ksjc" />
        <result column="kssj" jdbcType="VARCHAR" property="kssj" />
        <result column="jsjc" jdbcType="VARCHAR" property="jsjc" />
        <result column="jssj" jdbcType="VARCHAR" property="jssj" />
        <result column="bz" jdbcType="VARCHAR" property="bz" />
        <result column="zzt" jdbcType="VARCHAR" property="zzt" />
        <result column="xqj" jdbcType="VARCHAR" property="xqj" />
        <result column="kcxh" jdbcType="VARCHAR" property="kcxh" />
        <result column="xq" jdbcType="VARCHAR" property="xq" />
        <result column="skjsdm" jdbcType="VARCHAR" property="skjsdm" />
        <result column="skjsmc" jdbcType="VARCHAR" property="skjsmc" />
        <result column="sys_create_time" jdbcType="VARCHAR" property="sysCreateTime" />
        <result column="sys_data_id" jdbcType="VARCHAR" property="sysDataId" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        zj,
        ksjc,
        kssj,
        jsjc,
        jssj,
        bz,
        zzt,
        xqj,
        kcxh,
        xq,
        skjsdm,
        skjsmc,
        sys_create_time,
        sys_data_id

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="zj != null and zj != ''">
            AND zj = #{zj,jdbcType=VARCHAR}
        </if>
        <if test="ksjc != null and ksjc != ''">
            AND ksjc = #{ksjc,jdbcType=VARCHAR}
        </if>
        <if test="kssj != null and kssj != ''">
            AND kssj = #{kssj,jdbcType=VARCHAR}
        </if>
        <if test="jsjc != null and jsjc != ''">
            AND jsjc = #{jsjc,jdbcType=VARCHAR}
        </if>
        <if test="jssj != null and jssj != ''">
            AND jssj = #{jssj,jdbcType=VARCHAR}
        </if>
        <if test="bz != null and bz != ''">
            AND bz = #{bz,jdbcType=VARCHAR}
        </if>
        <if test="zzt != null and zzt != ''">
            AND zzt = #{zzt,jdbcType=VARCHAR}
        </if>
        <if test="xqj != null and xqj != ''">
            AND xqj = #{xqj,jdbcType=VARCHAR}
        </if>
        <if test="kcxh != null and kcxh != ''">
            AND kcxh = #{kcxh,jdbcType=VARCHAR}
        </if>
        <if test="xq != null and xq != ''">
            AND xq = #{xq,jdbcType=VARCHAR}
        </if>
        <if test="skjsdm != null and skjsdm != ''">
            AND skjsdm = #{skjsdm,jdbcType=VARCHAR}
        </if>
        <if test="skjsmc != null and skjsmc != ''">
            AND skjsmc = #{skjsmc,jdbcType=VARCHAR}
        </if>
        <if test="sysCreateTime != null and sysCreateTime != ''">
            AND sys_create_time = #{sysCreateTime,jdbcType=VARCHAR}
        </if>
        <if test="sysDataId != null and sysDataId != ''">
            AND sys_data_id = #{sysDataId,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="zj != null ">
            zj = #{zj,jdbcType=VARCHAR},
        </if>
        <if test="ksjc != null ">
            ksjc = #{ksjc,jdbcType=VARCHAR},
        </if>
        <if test="kssj != null ">
            kssj = #{kssj,jdbcType=VARCHAR},
        </if>
        <if test="jsjc != null ">
            jsjc = #{jsjc,jdbcType=VARCHAR},
        </if>
        <if test="jssj != null ">
            jssj = #{jssj,jdbcType=VARCHAR},
        </if>
        <if test="bz != null ">
            bz = #{bz,jdbcType=VARCHAR},
        </if>
        <if test="zzt != null ">
            zzt = #{zzt,jdbcType=VARCHAR},
        </if>
        <if test="xqj != null ">
            xqj = #{xqj,jdbcType=VARCHAR},
        </if>
        <if test="kcxh != null ">
            kcxh = #{kcxh,jdbcType=VARCHAR},
        </if>
        <if test="xq != null ">
            xq = #{xq,jdbcType=VARCHAR},
        </if>
        <if test="skjsdm != null ">
            skjsdm = #{skjsdm,jdbcType=VARCHAR},
        </if>
        <if test="skjsmc != null ">
            skjsmc = #{skjsmc,jdbcType=VARCHAR},
        </if>
        <if test="sysCreateTime != null ">
            sys_create_time = #{sysCreateTime,jdbcType=VARCHAR},
        </if>
        <if test="sysDataId != null ">
            sys_data_id = #{sysDataId,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.basicinfo.model.domain.DjHhjtPkxx"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from dj_hhjt_pkxx
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>

    <insert id="insertAllList">
        insert into dj_hhjt_pkxx (<include refid="Base_Column_List" />)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.zj},
            #{item.ksjc},
            #{item.kssj},
            #{item.jsjc},
            #{item.jssj},
            #{item.bz},
            #{item.zzt},
            #{item.xqj},
            #{item.kcxh},
            #{item.xq},
            #{item.skjsdm},
            #{item.skjsmc},
            #{item.sysCreateTime},
            #{item.sysDataId}
            )
        </foreach>
    </insert>

    <delete id="removeDuplicates">
        DELETE FROM dj_hhjt_pkxx where kcxh in (select * FROM (SELECT kcxh FROM dj_hhjt_pkxx GROUP BY kcxh HAVING count(0) >1) t );
    </delete>
</mapper>
