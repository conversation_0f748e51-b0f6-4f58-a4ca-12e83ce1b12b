<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.basicinfo.mapper.DjHhjtJzgjbxxMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.basicinfo.model.domain.DjHhjtJzgjbxx">
        <id column="zj" jdbcType="VARCHAR" property="zj" />
        <result column="gh" jdbcType="VARCHAR" property="gh" />
        <result column="xm" jdbcType="VARCHAR" property="xm" />
        <result column="ymxm" jdbcType="VARCHAR" property="ymxm" />
        <result column="cym" jdbcType="VARCHAR" property="cym" />
        <result column="rylb" jdbcType="VARCHAR" property="rylb" />
        <result column="xb" jdbcType="VARCHAR" property="xb" />
        <result column="csrq" jdbcType="VARCHAR" property="csrq" />
        <result column="nl" jdbcType="VARCHAR" property="nl" />
        <result column="mz" jdbcType="VARCHAR" property="mz" />
        <result column="jg" jdbcType="VARCHAR" property="jg" />
        <result column="gj" jdbcType="VARCHAR" property="gj" />
        <result column="sfzlb" jdbcType="VARCHAR" property="sfzlb" />
        <result column="sfzjhm" jdbcType="VARCHAR" property="sfzjhm" />
        <result column="csd" jdbcType="VARCHAR" property="csd" />
        <result column="xzz" jdbcType="VARCHAR" property="xzz" />
        <result column="cjgzsj" jdbcType="VARCHAR" property="cjgzsj" />
        <result column="cjqssj" jdbcType="VARCHAR" property="cjqssj" />
        <result column="yrxs" jdbcType="VARCHAR" property="yrxs" />
        <result column="rxsj" jdbcType="VARCHAR" property="rxsj" />
        <result column="gl" jdbcType="VARCHAR" property="gl" />
        <result column="xl" jdbcType="VARCHAR" property="xl" />
        <result column="zzmm" jdbcType="VARCHAR" property="zzmm" />
        <result column="zwxl" jdbcType="VARCHAR" property="zwxl" />
        <result column="cjdpsj" jdbcType="VARCHAR" property="cjdpsj" />
        <result column="sj" jdbcType="VARCHAR" property="sj" />
        <result column="yx" jdbcType="VARCHAR" property="yx" />
        <result column="qtlxfs" jdbcType="VARCHAR" property="qtlxfs" />
        <result column="jkzk" jdbcType="VARCHAR" property="jkzk" />
        <result column="hyzk" jdbcType="VARCHAR" property="hyzk" />
        <result column="jzglb" jdbcType="VARCHAR" property="jzglb" />
        <result column="jzgly" jdbcType="VARCHAR" property="jzgly" />
        <result column="yjfx" jdbcType="VARCHAR" property="yjfx" />
        <result column="xyjg" jdbcType="VARCHAR" property="xyjg" />
        <result column="sfsjt" jdbcType="VARCHAR" property="sfsjt" />
        <result column="gwlb" jdbcType="VARCHAR" property="gwlb" />
        <result column="gwdj" jdbcType="VARCHAR" property="gwdj" />
        <result column="gwpysj" jdbcType="VARCHAR" property="gwpysj" />
        <result column="xzzw" jdbcType="VARCHAR" property="xzzw" />
        <result column="xzzwjb" jdbcType="VARCHAR" property="xzzwjb" />
        <result column="xzzwprsj" jdbcType="VARCHAR" property="xzzwprsj" />
        <result column="xzcxl" jdbcType="VARCHAR" property="xzcxl" />
        <result column="xrzczy" jdbcType="VARCHAR" property="xrzczy" />
        <result column="xrzcmc" jdbcType="VARCHAR" property="xrzcmc" />
        <result column="xrzcqdsj" jdbcType="VARCHAR" property="xrzcqdsj" />
        <result column="zczsbh" jdbcType="VARCHAR" property="zczsbh" />
        <result column="zcfzdw" jdbcType="VARCHAR" property="zcfzdw" />
        <result column="zczgzsc" jdbcType="VARCHAR" property="zczgzsc" />
        <result column="zcprzsc" jdbcType="VARCHAR" property="zcprzsc" />
        <result column="qtzcmc" jdbcType="VARCHAR" property="qtzcmc" />
        <result column="qtzcjb" jdbcType="VARCHAR" property="qtzcjb" />
        <result column="qtzczsbh" jdbcType="VARCHAR" property="qtzczsbh" />
        <result column="qtzcqdsj" jdbcType="VARCHAR" property="qtzcqdsj" />
        <result column="qtzcprsj" jdbcType="VARCHAR" property="qtzcprsj" />
        <result column="sfygxjszgz" jdbcType="VARCHAR" property="sfygxjszgz" />
        <result column="gxjszgzbm" jdbcType="VARCHAR" property="gxjszgzbm" />
        <result column="gxjszgzfzsj" jdbcType="VARCHAR" property="gxjszgzfzsj" />
        <result column="zgxl" jdbcType="VARCHAR" property="zgxl" />
        <result column="zgxwcc" jdbcType="VARCHAR" property="zgxwcc" />
        <result column="zgxlcc" jdbcType="VARCHAR" property="zgxlcc" />
        <result column="zgxw" jdbcType="VARCHAR" property="zgxw" />
        <result column="hdzgxldyxhjg" jdbcType="VARCHAR" property="hdzgxldyxhjg" />
        <result column="zgxlsxzy" jdbcType="VARCHAR" property="zgxlsxzy" />
        <result column="zgxlbysj" jdbcType="VARCHAR" property="zgxlbysj" />
        <result column="hdzgxwsj" jdbcType="VARCHAR" property="hdzgxwsj" />
        <result column="hdzgxwdyxhjg" jdbcType="VARCHAR" property="hdzgxwdyxhjg" />
        <result column="zgxlxz" jdbcType="VARCHAR" property="zgxlxz" />
        <result column="dyxlcc" jdbcType="VARCHAR" property="dyxlcc" />
        <result column="dyxl" jdbcType="VARCHAR" property="dyxl" />
        <result column="dyxwcc" jdbcType="VARCHAR" property="dyxwcc" />
        <result column="dyxw" jdbcType="VARCHAR" property="dyxw" />
        <result column="hddyxldyxhjg" jdbcType="VARCHAR" property="hddyxldyxhjg" />
        <result column="dyxlsxzy" jdbcType="VARCHAR" property="dyxlsxzy" />
        <result column="dyxlbysj" jdbcType="VARCHAR" property="dyxlbysj" />
        <result column="dyxlxz" jdbcType="VARCHAR" property="dyxlxz" />
        <result column="jxkssj" jdbcType="VARCHAR" property="jxkssj" />
        <result column="jxjssj" jdbcType="VARCHAR" property="jxjssj" />
        <result column="ltxsj" jdbcType="VARCHAR" property="ltxsj" />
        <result column="txyy" jdbcType="VARCHAR" property="txyy" />
        <result column="jyyy" jdbcType="VARCHAR" property="jyyy" />
        <result column="jysj" jdbcType="VARCHAR" property="jysj" />
        <result column="zyjszwqdsj" jdbcType="VARCHAR" property="zyjszwqdsj" />
        <result column="jszgzsc" jdbcType="VARCHAR" property="jszgzsc" />
        <result column="zgxlzssc" jdbcType="VARCHAR" property="zgxlzssc" />
        <result column="zgxwzssc" jdbcType="VARCHAR" property="zgxwzssc" />
        <result column="zgxlxxxs" jdbcType="VARCHAR" property="zgxlxxxs" />
        <result column="dyxlxxxs" jdbcType="VARCHAR" property="dyxlxxxs" />
        <result column="sbgx" jdbcType="VARCHAR" property="sbgx" />
        <result column="xndabh" jdbcType="VARCHAR" property="xndabh" />
        <result column="bjdbm" jdbcType="VARCHAR" property="bjdbm" />
        <result column="grtc" jdbcType="VARCHAR" property="grtc" />
        <result column="xrzcjb" jdbcType="VARCHAR" property="xrzcjb" />
        <result column="xrzcprsj" jdbcType="VARCHAR" property="xrzcprsj" />
        <result column="rjzy" jdbcType="VARCHAR" property="rjzy" />
        <result column="zcszy" jdbcType="VARCHAR" property="zcszy" />
        <result column="bzlx" jdbcType="VARCHAR" property="bzlx" />
        <result column="gzdh" jdbcType="VARCHAR" property="gzdh" />
        <result column="glzj" jdbcType="VARCHAR" property="glzj" />
        <result column="bmdm" jdbcType="VARCHAR" property="bmdm" />
        <result column="bmmc" jdbcType="VARCHAR" property="bmmc" />
        <result column="rjbmdm" jdbcType="VARCHAR" property="rjbmdm" />
        <result column="rjbmmc" jdbcType="VARCHAR" property="rjbmmc" />
        <result column="sjbmdm" jdbcType="VARCHAR" property="sjbmdm" />
        <result column="sjbmmc" jdbcType="VARCHAR" property="sjbmmc" />
        <result column="sys_create_time" jdbcType="VARCHAR" property="sysCreateTime" />
        <result column="sys_data_id" jdbcType="VARCHAR" property="sysDataId" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        zj,
        gh,
        xm,
        ymxm,
        cym,
        rylb,
        xb,
        csrq,
        nl,
        mz,
        jg,
        gj,
        sfzlb,
        sfzjhm,
        csd,
        xzz,
        cjgzsj,
        cjqssj,
        yrxs,
        rxsj,
        gl,
        xl,
        zzmm,
        zwxl,
        cjdpsj,
        sj,
        yx,
        qtlxfs,
        jkzk,
        hyzk,
        jzglb,
        jzgly,
        yjfx,
        xyjg,
        sfsjt,
        gwlb,
        gwdj,
        gwpysj,
        xzzw,
        xzzwjb,
        xzzwprsj,
        xzcxl,
        xrzczy,
        xrzcmc,
        xrzcqdsj,
        zczsbh,
        zcfzdw,
        zczgzsc,
        zcprzsc,
        qtzcmc,
        qtzcjb,
        qtzczsbh,
        qtzcqdsj,
        qtzcprsj,
        sfygxjszgz,
        gxjszgzbm,
        gxjszgzfzsj,
        zgxl,
        zgxwcc,
        zgxlcc,
        zgxw,
        hdzgxldyxhjg,
        zgxlsxzy,
        zgxlbysj,
        hdzgxwsj,
        hdzgxwdyxhjg,
        zgxlxz,
        dyxlcc,
        dyxl,
        dyxwcc,
        dyxw,
        hddyxldyxhjg,
        dyxlsxzy,
        dyxlbysj,
        dyxlxz,
        jxkssj,
        jxjssj,
        ltxsj,
        txyy,
        jyyy,
        jysj,
        zyjszwqdsj,
        jszgzsc,
        zgxlzssc,
        zgxwzssc,
        zgxlxxxs,
        dyxlxxxs,
        sbgx,
        xndabh,
        bjdbm,
        grtc,
        xrzcjb,
        xrzcprsj,
        rjzy,
        zcszy,
        bzlx,
        gzdh,
        glzj,
        bmdm,
        bmmc,
        rjbmdm,
        rjbmmc,
        sjbmdm,
        sjbmmc,
        sys_create_time,
        sys_data_id

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="zj != null and zj != ''">
            AND zj = #{zj,jdbcType=VARCHAR}
        </if>
        <if test="gh != null and gh != ''">
            AND gh = #{gh,jdbcType=VARCHAR}
        </if>
        <if test="xm != null and xm != ''">
            AND xm = #{xm,jdbcType=VARCHAR}
        </if>
        <if test="ymxm != null and ymxm != ''">
            AND ymxm = #{ymxm,jdbcType=VARCHAR}
        </if>
        <if test="cym != null and cym != ''">
            AND cym = #{cym,jdbcType=VARCHAR}
        </if>
        <if test="rylb != null and rylb != ''">
            AND rylb = #{rylb,jdbcType=VARCHAR}
        </if>
        <if test="xb != null and xb != ''">
            AND xb = #{xb,jdbcType=VARCHAR}
        </if>
        <if test="csrq != null and csrq != ''">
            AND csrq = #{csrq,jdbcType=VARCHAR}
        </if>
        <if test="nl != null and nl != ''">
            AND nl = #{nl,jdbcType=VARCHAR}
        </if>
        <if test="mz != null and mz != ''">
            AND mz = #{mz,jdbcType=VARCHAR}
        </if>
        <if test="jg != null and jg != ''">
            AND jg = #{jg,jdbcType=VARCHAR}
        </if>
        <if test="gj != null and gj != ''">
            AND gj = #{gj,jdbcType=VARCHAR}
        </if>
        <if test="sfzlb != null and sfzlb != ''">
            AND sfzlb = #{sfzlb,jdbcType=VARCHAR}
        </if>
        <if test="sfzjhm != null and sfzjhm != ''">
            AND sfzjhm = #{sfzjhm,jdbcType=VARCHAR}
        </if>
        <if test="csd != null and csd != ''">
            AND csd = #{csd,jdbcType=VARCHAR}
        </if>
        <if test="xzz != null and xzz != ''">
            AND xzz = #{xzz,jdbcType=VARCHAR}
        </if>
        <if test="cjgzsj != null and cjgzsj != ''">
            AND cjgzsj = #{cjgzsj,jdbcType=VARCHAR}
        </if>
        <if test="cjqssj != null and cjqssj != ''">
            AND cjqssj = #{cjqssj,jdbcType=VARCHAR}
        </if>
        <if test="yrxs != null and yrxs != ''">
            AND yrxs = #{yrxs,jdbcType=VARCHAR}
        </if>
        <if test="rxsj != null and rxsj != ''">
            AND rxsj = #{rxsj,jdbcType=VARCHAR}
        </if>
        <if test="gl != null and gl != ''">
            AND gl = #{gl,jdbcType=VARCHAR}
        </if>
        <if test="xl != null and xl != ''">
            AND xl = #{xl,jdbcType=VARCHAR}
        </if>
        <if test="zzmm != null and zzmm != ''">
            AND zzmm = #{zzmm,jdbcType=VARCHAR}
        </if>
        <if test="zwxl != null and zwxl != ''">
            AND zwxl = #{zwxl,jdbcType=VARCHAR}
        </if>
        <if test="cjdpsj != null and cjdpsj != ''">
            AND cjdpsj = #{cjdpsj,jdbcType=VARCHAR}
        </if>
        <if test="sj != null and sj != ''">
            AND sj = #{sj,jdbcType=VARCHAR}
        </if>
        <if test="yx != null and yx != ''">
            AND yx = #{yx,jdbcType=VARCHAR}
        </if>
        <if test="qtlxfs != null and qtlxfs != ''">
            AND qtlxfs = #{qtlxfs,jdbcType=VARCHAR}
        </if>
        <if test="jkzk != null and jkzk != ''">
            AND jkzk = #{jkzk,jdbcType=VARCHAR}
        </if>
        <if test="hyzk != null and hyzk != ''">
            AND hyzk = #{hyzk,jdbcType=VARCHAR}
        </if>
        <if test="jzglb != null and jzglb != ''">
            AND jzglb = #{jzglb,jdbcType=VARCHAR}
        </if>
        <if test="jzgly != null and jzgly != ''">
            AND jzgly = #{jzgly,jdbcType=VARCHAR}
        </if>
        <if test="yjfx != null and yjfx != ''">
            AND yjfx = #{yjfx,jdbcType=VARCHAR}
        </if>
        <if test="xyjg != null and xyjg != ''">
            AND xyjg = #{xyjg,jdbcType=VARCHAR}
        </if>
        <if test="sfsjt != null and sfsjt != ''">
            AND sfsjt = #{sfsjt,jdbcType=VARCHAR}
        </if>
        <if test="gwlb != null and gwlb != ''">
            AND gwlb = #{gwlb,jdbcType=VARCHAR}
        </if>
        <if test="gwdj != null and gwdj != ''">
            AND gwdj = #{gwdj,jdbcType=VARCHAR}
        </if>
        <if test="gwpysj != null and gwpysj != ''">
            AND gwpysj = #{gwpysj,jdbcType=VARCHAR}
        </if>
        <if test="xzzw != null and xzzw != ''">
            AND xzzw = #{xzzw,jdbcType=VARCHAR}
        </if>
        <if test="xzzwjb != null and xzzwjb != ''">
            AND xzzwjb = #{xzzwjb,jdbcType=VARCHAR}
        </if>
        <if test="xzzwprsj != null and xzzwprsj != ''">
            AND xzzwprsj = #{xzzwprsj,jdbcType=VARCHAR}
        </if>
        <if test="xzcxl != null and xzcxl != ''">
            AND xzcxl = #{xzcxl,jdbcType=VARCHAR}
        </if>
        <if test="xrzczy != null and xrzczy != ''">
            AND xrzczy = #{xrzczy,jdbcType=VARCHAR}
        </if>
        <if test="xrzcmc != null and xrzcmc != ''">
            AND xrzcmc = #{xrzcmc,jdbcType=VARCHAR}
        </if>
        <if test="xrzcqdsj != null and xrzcqdsj != ''">
            AND xrzcqdsj = #{xrzcqdsj,jdbcType=VARCHAR}
        </if>
        <if test="zczsbh != null and zczsbh != ''">
            AND zczsbh = #{zczsbh,jdbcType=VARCHAR}
        </if>
        <if test="zcfzdw != null and zcfzdw != ''">
            AND zcfzdw = #{zcfzdw,jdbcType=VARCHAR}
        </if>
        <if test="zczgzsc != null and zczgzsc != ''">
            AND zczgzsc = #{zczgzsc,jdbcType=VARCHAR}
        </if>
        <if test="zcprzsc != null and zcprzsc != ''">
            AND zcprzsc = #{zcprzsc,jdbcType=VARCHAR}
        </if>
        <if test="qtzcmc != null and qtzcmc != ''">
            AND qtzcmc = #{qtzcmc,jdbcType=VARCHAR}
        </if>
        <if test="qtzcjb != null and qtzcjb != ''">
            AND qtzcjb = #{qtzcjb,jdbcType=VARCHAR}
        </if>
        <if test="qtzczsbh != null and qtzczsbh != ''">
            AND qtzczsbh = #{qtzczsbh,jdbcType=VARCHAR}
        </if>
        <if test="qtzcqdsj != null and qtzcqdsj != ''">
            AND qtzcqdsj = #{qtzcqdsj,jdbcType=VARCHAR}
        </if>
        <if test="qtzcprsj != null and qtzcprsj != ''">
            AND qtzcprsj = #{qtzcprsj,jdbcType=VARCHAR}
        </if>
        <if test="sfygxjszgz != null and sfygxjszgz != ''">
            AND sfygxjszgz = #{sfygxjszgz,jdbcType=VARCHAR}
        </if>
        <if test="gxjszgzbm != null and gxjszgzbm != ''">
            AND gxjszgzbm = #{gxjszgzbm,jdbcType=VARCHAR}
        </if>
        <if test="gxjszgzfzsj != null and gxjszgzfzsj != ''">
            AND gxjszgzfzsj = #{gxjszgzfzsj,jdbcType=VARCHAR}
        </if>
        <if test="zgxl != null and zgxl != ''">
            AND zgxl = #{zgxl,jdbcType=VARCHAR}
        </if>
        <if test="zgxwcc != null and zgxwcc != ''">
            AND zgxwcc = #{zgxwcc,jdbcType=VARCHAR}
        </if>
        <if test="zgxlcc != null and zgxlcc != ''">
            AND zgxlcc = #{zgxlcc,jdbcType=VARCHAR}
        </if>
        <if test="zgxw != null and zgxw != ''">
            AND zgxw = #{zgxw,jdbcType=VARCHAR}
        </if>
        <if test="hdzgxldyxhjg != null and hdzgxldyxhjg != ''">
            AND hdzgxldyxhjg = #{hdzgxldyxhjg,jdbcType=VARCHAR}
        </if>
        <if test="zgxlsxzy != null and zgxlsxzy != ''">
            AND zgxlsxzy = #{zgxlsxzy,jdbcType=VARCHAR}
        </if>
        <if test="zgxlbysj != null and zgxlbysj != ''">
            AND zgxlbysj = #{zgxlbysj,jdbcType=VARCHAR}
        </if>
        <if test="hdzgxwsj != null and hdzgxwsj != ''">
            AND hdzgxwsj = #{hdzgxwsj,jdbcType=VARCHAR}
        </if>
        <if test="hdzgxwdyxhjg != null and hdzgxwdyxhjg != ''">
            AND hdzgxwdyxhjg = #{hdzgxwdyxhjg,jdbcType=VARCHAR}
        </if>
        <if test="zgxlxz != null and zgxlxz != ''">
            AND zgxlxz = #{zgxlxz,jdbcType=VARCHAR}
        </if>
        <if test="dyxlcc != null and dyxlcc != ''">
            AND dyxlcc = #{dyxlcc,jdbcType=VARCHAR}
        </if>
        <if test="dyxl != null and dyxl != ''">
            AND dyxl = #{dyxl,jdbcType=VARCHAR}
        </if>
        <if test="dyxwcc != null and dyxwcc != ''">
            AND dyxwcc = #{dyxwcc,jdbcType=VARCHAR}
        </if>
        <if test="dyxw != null and dyxw != ''">
            AND dyxw = #{dyxw,jdbcType=VARCHAR}
        </if>
        <if test="hddyxldyxhjg != null and hddyxldyxhjg != ''">
            AND hddyxldyxhjg = #{hddyxldyxhjg,jdbcType=VARCHAR}
        </if>
        <if test="dyxlsxzy != null and dyxlsxzy != ''">
            AND dyxlsxzy = #{dyxlsxzy,jdbcType=VARCHAR}
        </if>
        <if test="dyxlbysj != null and dyxlbysj != ''">
            AND dyxlbysj = #{dyxlbysj,jdbcType=VARCHAR}
        </if>
        <if test="dyxlxz != null and dyxlxz != ''">
            AND dyxlxz = #{dyxlxz,jdbcType=VARCHAR}
        </if>
        <if test="jxkssj != null and jxkssj != ''">
            AND jxkssj = #{jxkssj,jdbcType=VARCHAR}
        </if>
        <if test="jxjssj != null and jxjssj != ''">
            AND jxjssj = #{jxjssj,jdbcType=VARCHAR}
        </if>
        <if test="ltxsj != null and ltxsj != ''">
            AND ltxsj = #{ltxsj,jdbcType=VARCHAR}
        </if>
        <if test="txyy != null and txyy != ''">
            AND txyy = #{txyy,jdbcType=VARCHAR}
        </if>
        <if test="jyyy != null and jyyy != ''">
            AND jyyy = #{jyyy,jdbcType=VARCHAR}
        </if>
        <if test="jysj != null and jysj != ''">
            AND jysj = #{jysj,jdbcType=VARCHAR}
        </if>
        <if test="zyjszwqdsj != null and zyjszwqdsj != ''">
            AND zyjszwqdsj = #{zyjszwqdsj,jdbcType=VARCHAR}
        </if>
        <if test="jszgzsc != null and jszgzsc != ''">
            AND jszgzsc = #{jszgzsc,jdbcType=VARCHAR}
        </if>
        <if test="zgxlzssc != null and zgxlzssc != ''">
            AND zgxlzssc = #{zgxlzssc,jdbcType=VARCHAR}
        </if>
        <if test="zgxwzssc != null and zgxwzssc != ''">
            AND zgxwzssc = #{zgxwzssc,jdbcType=VARCHAR}
        </if>
        <if test="zgxlxxxs != null and zgxlxxxs != ''">
            AND zgxlxxxs = #{zgxlxxxs,jdbcType=VARCHAR}
        </if>
        <if test="dyxlxxxs != null and dyxlxxxs != ''">
            AND dyxlxxxs = #{dyxlxxxs,jdbcType=VARCHAR}
        </if>
        <if test="sbgx != null and sbgx != ''">
            AND sbgx = #{sbgx,jdbcType=VARCHAR}
        </if>
        <if test="xndabh != null and xndabh != ''">
            AND xndabh = #{xndabh,jdbcType=VARCHAR}
        </if>
        <if test="bjdbm != null and bjdbm != ''">
            AND bjdbm = #{bjdbm,jdbcType=VARCHAR}
        </if>
        <if test="grtc != null and grtc != ''">
            AND grtc = #{grtc,jdbcType=VARCHAR}
        </if>
        <if test="xrzcjb != null and xrzcjb != ''">
            AND xrzcjb = #{xrzcjb,jdbcType=VARCHAR}
        </if>
        <if test="xrzcprsj != null and xrzcprsj != ''">
            AND xrzcprsj = #{xrzcprsj,jdbcType=VARCHAR}
        </if>
        <if test="rjzy != null and rjzy != ''">
            AND rjzy = #{rjzy,jdbcType=VARCHAR}
        </if>
        <if test="zcszy != null and zcszy != ''">
            AND zcszy = #{zcszy,jdbcType=VARCHAR}
        </if>
        <if test="bzlx != null and bzlx != ''">
            AND bzlx = #{bzlx,jdbcType=VARCHAR}
        </if>
        <if test="gzdh != null and gzdh != ''">
            AND gzdh = #{gzdh,jdbcType=VARCHAR}
        </if>
        <if test="glzj != null and glzj != ''">
            AND glzj = #{glzj,jdbcType=VARCHAR}
        </if>
        <if test="bmdm != null and bmdm != ''">
            AND bmdm = #{bmdm,jdbcType=VARCHAR}
        </if>
        <if test="bmmc != null and bmmc != ''">
            AND bmmc = #{bmmc,jdbcType=VARCHAR}
        </if>
        <if test="rjbmdm != null and rjbmdm != ''">
            AND rjbmdm = #{rjbmdm,jdbcType=VARCHAR}
        </if>
        <if test="rjbmmc != null and rjbmmc != ''">
            AND rjbmmc = #{rjbmmc,jdbcType=VARCHAR}
        </if>
        <if test="sjbmdm != null and sjbmdm != ''">
            AND sjbmdm = #{sjbmdm,jdbcType=VARCHAR}
        </if>
        <if test="sjbmmc != null and sjbmmc != ''">
            AND sjbmmc = #{sjbmmc,jdbcType=VARCHAR}
        </if>
        <if test="sysCreateTime != null and sysCreateTime != ''">
            AND sys_create_time = #{sysCreateTime,jdbcType=VARCHAR}
        </if>
        <if test="sysDataId != null and sysDataId != ''">
            AND sys_data_id = #{sysDataId,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="zj != null ">
            zj = #{zj,jdbcType=VARCHAR},
        </if>
        <if test="gh != null ">
            gh = #{gh,jdbcType=VARCHAR},
        </if>
        <if test="xm != null ">
            xm = #{xm,jdbcType=VARCHAR},
        </if>
        <if test="ymxm != null ">
            ymxm = #{ymxm,jdbcType=VARCHAR},
        </if>
        <if test="cym != null ">
            cym = #{cym,jdbcType=VARCHAR},
        </if>
        <if test="rylb != null ">
            rylb = #{rylb,jdbcType=VARCHAR},
        </if>
        <if test="xb != null ">
            xb = #{xb,jdbcType=VARCHAR},
        </if>
        <if test="csrq != null ">
            csrq = #{csrq,jdbcType=VARCHAR},
        </if>
        <if test="nl != null ">
            nl = #{nl,jdbcType=VARCHAR},
        </if>
        <if test="mz != null ">
            mz = #{mz,jdbcType=VARCHAR},
        </if>
        <if test="jg != null ">
            jg = #{jg,jdbcType=VARCHAR},
        </if>
        <if test="gj != null ">
            gj = #{gj,jdbcType=VARCHAR},
        </if>
        <if test="sfzlb != null ">
            sfzlb = #{sfzlb,jdbcType=VARCHAR},
        </if>
        <if test="sfzjhm != null ">
            sfzjhm = #{sfzjhm,jdbcType=VARCHAR},
        </if>
        <if test="csd != null ">
            csd = #{csd,jdbcType=VARCHAR},
        </if>
        <if test="xzz != null ">
            xzz = #{xzz,jdbcType=VARCHAR},
        </if>
        <if test="cjgzsj != null ">
            cjgzsj = #{cjgzsj,jdbcType=VARCHAR},
        </if>
        <if test="cjqssj != null ">
            cjqssj = #{cjqssj,jdbcType=VARCHAR},
        </if>
        <if test="yrxs != null ">
            yrxs = #{yrxs,jdbcType=VARCHAR},
        </if>
        <if test="rxsj != null ">
            rxsj = #{rxsj,jdbcType=VARCHAR},
        </if>
        <if test="gl != null ">
            gl = #{gl,jdbcType=VARCHAR},
        </if>
        <if test="xl != null ">
            xl = #{xl,jdbcType=VARCHAR},
        </if>
        <if test="zzmm != null ">
            zzmm = #{zzmm,jdbcType=VARCHAR},
        </if>
        <if test="zwxl != null ">
            zwxl = #{zwxl,jdbcType=VARCHAR},
        </if>
        <if test="cjdpsj != null ">
            cjdpsj = #{cjdpsj,jdbcType=VARCHAR},
        </if>
        <if test="sj != null ">
            sj = #{sj,jdbcType=VARCHAR},
        </if>
        <if test="yx != null ">
            yx = #{yx,jdbcType=VARCHAR},
        </if>
        <if test="qtlxfs != null ">
            qtlxfs = #{qtlxfs,jdbcType=VARCHAR},
        </if>
        <if test="jkzk != null ">
            jkzk = #{jkzk,jdbcType=VARCHAR},
        </if>
        <if test="hyzk != null ">
            hyzk = #{hyzk,jdbcType=VARCHAR},
        </if>
        <if test="jzglb != null ">
            jzglb = #{jzglb,jdbcType=VARCHAR},
        </if>
        <if test="jzgly != null ">
            jzgly = #{jzgly,jdbcType=VARCHAR},
        </if>
        <if test="yjfx != null ">
            yjfx = #{yjfx,jdbcType=VARCHAR},
        </if>
        <if test="xyjg != null ">
            xyjg = #{xyjg,jdbcType=VARCHAR},
        </if>
        <if test="sfsjt != null ">
            sfsjt = #{sfsjt,jdbcType=VARCHAR},
        </if>
        <if test="gwlb != null ">
            gwlb = #{gwlb,jdbcType=VARCHAR},
        </if>
        <if test="gwdj != null ">
            gwdj = #{gwdj,jdbcType=VARCHAR},
        </if>
        <if test="gwpysj != null ">
            gwpysj = #{gwpysj,jdbcType=VARCHAR},
        </if>
        <if test="xzzw != null ">
            xzzw = #{xzzw,jdbcType=VARCHAR},
        </if>
        <if test="xzzwjb != null ">
            xzzwjb = #{xzzwjb,jdbcType=VARCHAR},
        </if>
        <if test="xzzwprsj != null ">
            xzzwprsj = #{xzzwprsj,jdbcType=VARCHAR},
        </if>
        <if test="xzcxl != null ">
            xzcxl = #{xzcxl,jdbcType=VARCHAR},
        </if>
        <if test="xrzczy != null ">
            xrzczy = #{xrzczy,jdbcType=VARCHAR},
        </if>
        <if test="xrzcmc != null ">
            xrzcmc = #{xrzcmc,jdbcType=VARCHAR},
        </if>
        <if test="xrzcqdsj != null ">
            xrzcqdsj = #{xrzcqdsj,jdbcType=VARCHAR},
        </if>
        <if test="zczsbh != null ">
            zczsbh = #{zczsbh,jdbcType=VARCHAR},
        </if>
        <if test="zcfzdw != null ">
            zcfzdw = #{zcfzdw,jdbcType=VARCHAR},
        </if>
        <if test="zczgzsc != null ">
            zczgzsc = #{zczgzsc,jdbcType=VARCHAR},
        </if>
        <if test="zcprzsc != null ">
            zcprzsc = #{zcprzsc,jdbcType=VARCHAR},
        </if>
        <if test="qtzcmc != null ">
            qtzcmc = #{qtzcmc,jdbcType=VARCHAR},
        </if>
        <if test="qtzcjb != null ">
            qtzcjb = #{qtzcjb,jdbcType=VARCHAR},
        </if>
        <if test="qtzczsbh != null ">
            qtzczsbh = #{qtzczsbh,jdbcType=VARCHAR},
        </if>
        <if test="qtzcqdsj != null ">
            qtzcqdsj = #{qtzcqdsj,jdbcType=VARCHAR},
        </if>
        <if test="qtzcprsj != null ">
            qtzcprsj = #{qtzcprsj,jdbcType=VARCHAR},
        </if>
        <if test="sfygxjszgz != null ">
            sfygxjszgz = #{sfygxjszgz,jdbcType=VARCHAR},
        </if>
        <if test="gxjszgzbm != null ">
            gxjszgzbm = #{gxjszgzbm,jdbcType=VARCHAR},
        </if>
        <if test="gxjszgzfzsj != null ">
            gxjszgzfzsj = #{gxjszgzfzsj,jdbcType=VARCHAR},
        </if>
        <if test="zgxl != null ">
            zgxl = #{zgxl,jdbcType=VARCHAR},
        </if>
        <if test="zgxwcc != null ">
            zgxwcc = #{zgxwcc,jdbcType=VARCHAR},
        </if>
        <if test="zgxlcc != null ">
            zgxlcc = #{zgxlcc,jdbcType=VARCHAR},
        </if>
        <if test="zgxw != null ">
            zgxw = #{zgxw,jdbcType=VARCHAR},
        </if>
        <if test="hdzgxldyxhjg != null ">
            hdzgxldyxhjg = #{hdzgxldyxhjg,jdbcType=VARCHAR},
        </if>
        <if test="zgxlsxzy != null ">
            zgxlsxzy = #{zgxlsxzy,jdbcType=VARCHAR},
        </if>
        <if test="zgxlbysj != null ">
            zgxlbysj = #{zgxlbysj,jdbcType=VARCHAR},
        </if>
        <if test="hdzgxwsj != null ">
            hdzgxwsj = #{hdzgxwsj,jdbcType=VARCHAR},
        </if>
        <if test="hdzgxwdyxhjg != null ">
            hdzgxwdyxhjg = #{hdzgxwdyxhjg,jdbcType=VARCHAR},
        </if>
        <if test="zgxlxz != null ">
            zgxlxz = #{zgxlxz,jdbcType=VARCHAR},
        </if>
        <if test="dyxlcc != null ">
            dyxlcc = #{dyxlcc,jdbcType=VARCHAR},
        </if>
        <if test="dyxl != null ">
            dyxl = #{dyxl,jdbcType=VARCHAR},
        </if>
        <if test="dyxwcc != null ">
            dyxwcc = #{dyxwcc,jdbcType=VARCHAR},
        </if>
        <if test="dyxw != null ">
            dyxw = #{dyxw,jdbcType=VARCHAR},
        </if>
        <if test="hddyxldyxhjg != null ">
            hddyxldyxhjg = #{hddyxldyxhjg,jdbcType=VARCHAR},
        </if>
        <if test="dyxlsxzy != null ">
            dyxlsxzy = #{dyxlsxzy,jdbcType=VARCHAR},
        </if>
        <if test="dyxlbysj != null ">
            dyxlbysj = #{dyxlbysj,jdbcType=VARCHAR},
        </if>
        <if test="dyxlxz != null ">
            dyxlxz = #{dyxlxz,jdbcType=VARCHAR},
        </if>
        <if test="jxkssj != null ">
            jxkssj = #{jxkssj,jdbcType=VARCHAR},
        </if>
        <if test="jxjssj != null ">
            jxjssj = #{jxjssj,jdbcType=VARCHAR},
        </if>
        <if test="ltxsj != null ">
            ltxsj = #{ltxsj,jdbcType=VARCHAR},
        </if>
        <if test="txyy != null ">
            txyy = #{txyy,jdbcType=VARCHAR},
        </if>
        <if test="jyyy != null ">
            jyyy = #{jyyy,jdbcType=VARCHAR},
        </if>
        <if test="jysj != null ">
            jysj = #{jysj,jdbcType=VARCHAR},
        </if>
        <if test="zyjszwqdsj != null ">
            zyjszwqdsj = #{zyjszwqdsj,jdbcType=VARCHAR},
        </if>
        <if test="jszgzsc != null ">
            jszgzsc = #{jszgzsc,jdbcType=VARCHAR},
        </if>
        <if test="zgxlzssc != null ">
            zgxlzssc = #{zgxlzssc,jdbcType=VARCHAR},
        </if>
        <if test="zgxwzssc != null ">
            zgxwzssc = #{zgxwzssc,jdbcType=VARCHAR},
        </if>
        <if test="zgxlxxxs != null ">
            zgxlxxxs = #{zgxlxxxs,jdbcType=VARCHAR},
        </if>
        <if test="dyxlxxxs != null ">
            dyxlxxxs = #{dyxlxxxs,jdbcType=VARCHAR},
        </if>
        <if test="sbgx != null ">
            sbgx = #{sbgx,jdbcType=VARCHAR},
        </if>
        <if test="xndabh != null ">
            xndabh = #{xndabh,jdbcType=VARCHAR},
        </if>
        <if test="bjdbm != null ">
            bjdbm = #{bjdbm,jdbcType=VARCHAR},
        </if>
        <if test="grtc != null ">
            grtc = #{grtc,jdbcType=VARCHAR},
        </if>
        <if test="xrzcjb != null ">
            xrzcjb = #{xrzcjb,jdbcType=VARCHAR},
        </if>
        <if test="xrzcprsj != null ">
            xrzcprsj = #{xrzcprsj,jdbcType=VARCHAR},
        </if>
        <if test="rjzy != null ">
            rjzy = #{rjzy,jdbcType=VARCHAR},
        </if>
        <if test="zcszy != null ">
            zcszy = #{zcszy,jdbcType=VARCHAR},
        </if>
        <if test="bzlx != null ">
            bzlx = #{bzlx,jdbcType=VARCHAR},
        </if>
        <if test="gzdh != null ">
            gzdh = #{gzdh,jdbcType=VARCHAR},
        </if>
        <if test="glzj != null ">
            glzj = #{glzj,jdbcType=VARCHAR},
        </if>
        <if test="bmdm != null ">
            bmdm = #{bmdm,jdbcType=VARCHAR},
        </if>
        <if test="bmmc != null ">
            bmmc = #{bmmc,jdbcType=VARCHAR},
        </if>
        <if test="rjbmdm != null ">
            rjbmdm = #{rjbmdm,jdbcType=VARCHAR},
        </if>
        <if test="rjbmmc != null ">
            rjbmmc = #{rjbmmc,jdbcType=VARCHAR},
        </if>
        <if test="sjbmdm != null ">
            sjbmdm = #{sjbmdm,jdbcType=VARCHAR},
        </if>
        <if test="sjbmmc != null ">
            sjbmmc = #{sjbmmc,jdbcType=VARCHAR},
        </if>
        <if test="sysCreateTime != null ">
            sys_create_time = #{sysCreateTime,jdbcType=VARCHAR},
        </if>
        <if test="sysDataId != null ">
            sys_data_id = #{sysDataId,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.basicinfo.model.domain.DjHhjtJzgjbxx"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from dj_hhjt_jzgjbxx
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>

    <insert id="insertAllList">
        insert into dj_hhjt_jzgjbxx (<include refid="Base_Column_List" />)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.zj},
            #{item.gh},
            #{item.xm},
            #{item.ymxm},
            #{item.cym},
            #{item.rylb},
            #{item.xb},
            #{item.csrq},
            #{item.nl},
            #{item.mz},
            #{item.jg},
            #{item.gj},
            #{item.sfzlb},
            #{item.sfzjhm},
            #{item.csd},
            #{item.xzz},
            #{item.cjgzsj},
            #{item.cjqssj},
            #{item.yrxs},
            #{item.rxsj},
            #{item.gl},
            #{item.xl},
            #{item.zzmm},
            #{item.zwxl},
            #{item.cjdpsj},
            #{item.sj},
            #{item.yx},
            #{item.qtlxfs},
            #{item.jkzk},
            #{item.hyzk},
            #{item.jzglb},
            #{item.jzgly},
            #{item.yjfx},
            #{item.xyjg},
            #{item.sfsjt},
            #{item.gwlb},
            #{item.gwdj},
            #{item.gwpysj},
            #{item.xzzw},
            #{item.xzzwjb},
            #{item.xzzwprsj},
            #{item.xzcxl},
            #{item.xrzczy},
            #{item.xrzcmc},
            #{item.xrzcqdsj},
            #{item.zczsbh},
            #{item.zcfzdw},
            #{item.zczgzsc},
            #{item.zcprzsc},
            #{item.qtzcmc},
            #{item.qtzcjb},
            #{item.qtzczsbh},
            #{item.qtzcqdsj},
            #{item.qtzcprsj},
            #{item.sfygxjszgz},
            #{item.gxjszgzbm},
            #{item.gxjszgzfzsj},
            #{item.zgxl},
            #{item.zgxwcc},
            #{item.zgxlcc},
            #{item.zgxw},
            #{item.hdzgxldyxhjg},
            #{item.zgxlsxzy},
            #{item.zgxlbysj},
            #{item.hdzgxwsj},
            #{item.hdzgxwdyxhjg},
            #{item.zgxlxz},
            #{item.dyxlcc},
            #{item.dyxl},
            #{item.dyxwcc},
            #{item.dyxw},
            #{item.hddyxldyxhjg},
            #{item.dyxlsxzy},
            #{item.dyxlbysj},
            #{item.dyxlxz},
            #{item.jxkssj},
            #{item.jxjssj},
            #{item.ltxsj},
            #{item.txyy},
            #{item.jyyy},
            #{item.jysj},
            #{item.zyjszwqdsj},
            #{item.jszgzsc},
            #{item.zgxlzssc},
            #{item.zgxwzssc},
            #{item.zgxlxxxs},
            #{item.dyxlxxxs},
            #{item.sbgx},
            #{item.xndabh},
            #{item.bjdbm},
            #{item.grtc},
            #{item.xrzcjb},
            #{item.xrzcprsj},
            #{item.rjzy},
            #{item.zcszy},
            #{item.bzlx},
            #{item.gzdh},
            #{item.glzj},
            #{item.bmdm},
            #{item.bmmc},
            #{item.rjbmdm},
            #{item.rjbmmc},
            #{item.sjbmdm},
            #{item.sjbmmc},
            #{item.sysCreateTime},
            #{item.sysDataId}
            )
        </foreach>
    </insert>
</mapper>
