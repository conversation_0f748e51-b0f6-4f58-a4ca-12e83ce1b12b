/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.basicinfo.model.domain.DjHhjtJsxx;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;



/**
 * dj_hhjt_jsxx数据库操作
 * <AUTHOR>
 * @version $Id: InitDjHhjtJsxxMapper.java, v 0.1 2023年08月31日 09时38分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface DjHhjtJsxxMapper extends MyMapper<DjHhjtJsxx> {

    /**
	 * 分页查询dj_hhjt_jsxx
	 * 
	 * @param example
	 * @return
	 */
	List<DjHhjtJsxx> pageList(DjHhjtJsxx example);
    
    void insertAllList(List<DjHhjtJsxx> record);
}
