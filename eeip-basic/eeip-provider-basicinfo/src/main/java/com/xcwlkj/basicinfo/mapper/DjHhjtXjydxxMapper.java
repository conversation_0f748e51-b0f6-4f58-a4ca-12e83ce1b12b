/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.basicinfo.model.domain.DjHhjtXjydxx;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;



/**
 * dj_hhjt_xjydxx数据库操作
 * <AUTHOR>
 * @version $Id: InitDjHhjtXjydxxMapper.java, v 0.1 2023年08月31日 09时39分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface DjHhjtXjydxxMapper extends MyMapper<DjHhjtXjydxx> {

    /**
	 * 分页查询dj_hhjt_xjydxx
	 * 
	 * @param example
	 * @return
	 */
	List<DjHhjtXjydxx> pageList(DjHhjtXjydxx example);
}
