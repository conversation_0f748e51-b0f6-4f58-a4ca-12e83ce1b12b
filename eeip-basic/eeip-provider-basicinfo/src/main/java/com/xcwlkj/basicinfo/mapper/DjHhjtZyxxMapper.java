/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.basicinfo.model.domain.DjHhjtZyxx;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;



/**
 * dj_hhjt_zyxx数据库操作
 * <AUTHOR>
 * @version $Id: InitDjHhjtZyxxMapper.java, v 0.1 2023年08月31日 09时39分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface DjHhjtZyxxMapper extends MyMapper<DjHhjtZyxx> {

    /**
	 * 分页查询dj_hhjt_zyxx
	 * 
	 * @param example
	 * @return
	 */
	List<DjHhjtZyxx> pageList(DjHhjtZyxx example);
    
    void insertAllList(List<DjHhjtZyxx> record);
}
