/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.service;

import com.xcwlkj.basicinfo.model.domain.JcxxDcpz;
import com.xcwlkj.basicinfo.model.dto.dataexporttemplate.AddV1DTO;
import com.xcwlkj.basicinfo.model.enums.DataFileTypeEnum;
import org.springframework.stereotype.Service;
import com.xcwlkj.basicinfo.model.dto.dataexporttemplate.ListV1DTO;
import com.xcwlkj.basicinfo.model.vo.dataexporttemplate.ListV1VO;
import com.xcwlkj.basicinfo.model.dto.dataexporttemplate.DeleteV1DTO;
import org.springframework.web.multipart.MultipartFile;


/**
 * 导出配置表服务
 * <AUTHOR>
 * @version $Id: JcxxDcpzService.java, v 0.1 2022年10月27日 09时49分 xcwlkj.com Exp $
 */
@Service
public interface JcxxDcpzService extends BaseService<JcxxDcpz> {

    /**
     * 解析导出配置中的文件名称
     * @param jcxxDcpz
     * @return
     */
    String parseFileName(JcxxDcpz jcxxDcpz);
	
	/**
	 * 添加模板
	 * @param dto
	 * @return
	 */
	void addTemplateV1(AddV1DTO dto);

	/**
	 * 保存模板
	 * @param file 文件
	 * @param pzbh 配置编号
	 * @param fileType 文件类型
	 * @return 模板文件名称
	 */
	String saveTemplate(MultipartFile file, String pzbh, DataFileTypeEnum fileType);

	/**
	 * 模板列表
	 * @param dto
	 * @return
	 */
	ListV1VO listV1(ListV1DTO dto);
	/**
	 * 模板删除
	 * @param dto
	 * @return
	 */
	void deleteV1(DeleteV1DTO dto);
}