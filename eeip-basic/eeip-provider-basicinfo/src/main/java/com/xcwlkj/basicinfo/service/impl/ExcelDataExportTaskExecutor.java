package com.xcwlkj.basicinfo.service.impl;

import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.basicinfo.model.enums.DataFileTypeEnum;
import com.xcwlkj.utils.DBFUtil;
import com.xcwlkj.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

/**
 * 数据导出任务
 * 数据文件类型：EXCEL
 */
@Slf4j
@Service
public class ExcelDataExportTaskExecutor extends AbstractDataExportTaskExecutor{

    // 导出模板目录
    @Value("${xc.export.muban}")
    private String exportMubanPath;
    @Value("${xc.export.tempPath}")
    private String exportTempPath;

    @Override
    protected DataFileTypeEnum getExecutorKey() {
        return DataFileTypeEnum.EXCEL;
    }

    @Override
    protected String writeData(DataResult result) {
        String tableName = result.getTable();
        List<List<String>> dataList = result.getResultData();

        String templatePath = result.getTemplate();

        File file = new File(exportTempPath);
        if (!file.exists()){
            file.mkdirs();
        }
        String finalXlsxPath = exportTempPath + File.separator + tableName;
        ExcelUtil.writeExcel(dataList, dataList.get(0).size(), templatePath, finalXlsxPath);
        return finalXlsxPath;
    }
}
