/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.service.impl;

import com.xcwlkj.basicinfo.mapper.DjHhjtXqxxMapper;
import com.xcwlkj.basicinfo.model.domain.DjHhjtXqxx;
import com.xcwlkj.basicinfo.service.DjHhjtXqxxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * dj_hhjt_xqxx服务
 * <AUTHOR>
 * @version $Id: DjHhjtXqxxServiceImpl.java, v 0.1 2023年08月31日 09时39分 xcwlkj.com Exp $
 */
@Service("djHhjtXqxxService")
public class DjHhjtXqxxServiceImpl extends BaseServiceImpl<DjHhjtXqxxMapper, DjHhjtXqxx> implements DjHhjtXqxxService  {

    @Resource
    private DjHhjtXqxxMapper modelMapper;
    
    @Override
    public void insertList(List<DjHhjtXqxx> record) {
        modelMapper.insertAllList(record);
    }
}