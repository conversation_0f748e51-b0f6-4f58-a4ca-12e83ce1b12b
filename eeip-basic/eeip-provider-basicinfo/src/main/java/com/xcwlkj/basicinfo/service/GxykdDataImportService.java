package com.xcwlkj.basicinfo.service;

import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 广西医科大基础数据导入服务
 */
@Service
public interface GxykdDataImportService {

    /**
     * 教室课程安排情况表导入
     * @param kkxnd 开课学年度
     * @param kkxqm 开课学期码
     * @param xqh 校区号
     * @param file excel文件
     */
    void lessonAndClassroomImport(String kkxnd, String kkxqm, String xqh, File file);

    /**
     * 教学大纲导入
     * @param kkxnd 开课学年度
     * @param kkxqm 开课学期码
     * @param xqh 校区号
     * @param file excel文件
     */
    void syllabusImport(String kkxnd, String kkxqm, String xqh, File file);
}
