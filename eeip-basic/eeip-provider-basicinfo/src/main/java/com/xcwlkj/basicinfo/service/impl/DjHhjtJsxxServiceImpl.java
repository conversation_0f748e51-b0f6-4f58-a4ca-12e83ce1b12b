/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.service.impl;

import com.xcwlkj.basicinfo.mapper.DjHhjtJsxxMapper;
import com.xcwlkj.basicinfo.model.domain.DjHhjtJsxx;
import com.xcwlkj.basicinfo.service.DjHhjtJsxxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * dj_hhjt_jsxx服务
 * <AUTHOR>
 * @version $Id: DjHhjtJsxxServiceImpl.java, v 0.1 2023年08月31日 09时38分 xcwlkj.com Exp $
 */
@Service("djHhjtJsxxService")
public class DjHhjtJsxxServiceImpl extends BaseServiceImpl<DjHhjtJsxxMapper, DjHhjtJsxx> implements DjHhjtJsxxService  {

    @Resource
    private DjHhjtJsxxMapper modelMapper;
    
    @Override
    public void insertList(List<DjHhjtJsxx> record) {
        modelMapper.insertAllList(record);
    }
}