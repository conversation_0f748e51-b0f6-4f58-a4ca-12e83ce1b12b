package com.xcwlkj.basicinfo.job.dataapi.hhjtu;

import com.xcwlkj.base.exception.BusinessException;

/**
 * [注释]
 * Version:   1.0
 * Author:    HanJia
 * Date:      2023/8/30 17:54
 * Copyright: copyright (c) 2020
 **/
public enum HhjtApiDzEnum {
    /**登录授权**/
    TOKENURL("tokenUrl","https://ds.zjtu.edu.cn/gateway-api/jwt/token","登录鉴权URL"),
    APPID("appid","y6tr3c07784o7972","应用id"),
    SECRET("secret","2f2e6c90524711ec29ed7edc5b798916","secretid"),
    
    /**应用接口**/
    WEBURL("weburl","https://ds.zjtu.edu.cn/gateway-api/api/agent/data-service/api-use","接口地址"),
    JKID_JXRW("jkidJxrw","8c6f8bc00f0f11ed6bc3853e7344fce7","教学任务信息（+学期）"),
    JKID_BJJB("jkidBjjb","c9b632904c2311ec217041dd58091707","班级基本信息"),
    JKID_BMJBXX("jkidBmjbxx","3329ac204d0f11ec217041dd58091707","部门基本信息"),
    JKID_JSXX("jkidJsxx","9e2c549057e711ec1530fec319ee972a","教室信息"),
    JKID_JXLXX("jkidJxlxx","b67d0ef057e111ec1530fec319ee972a","教学楼信息"),
    JKID_JZGJBXX("jkidJzgjbxx","c4b41eb0533a11ec217041dd58091707","教职工基本信息"),
    JKID_KCXX("jkidKcxx","e7121d00589811ec1530fec319ee972a","课程信息"),
    JKID_XQXX("jkidXqxx","deb8be3051c111ec217041dd58091707","校区信息"),
    JKID_XJYDXX("jkidXjydxx","a2d2cb40613411ec1530fec319ee972a","学籍异动信息"),
    JKID_XQJBXX("jkidXqjbxx","9f165d6051bb11ec217041dd58091707","学期基本信息"),
    JKID_XSJBXX("jkidXsjbxx","6d337360dbfd11ec02d6776bd4f5fe65","学生基本信息"),
    JKID_ZYFXXX("jkidZyfxxx","5f82d9a04c2311ec217041dd58091707","专业方向信息"),
    JKID_ZYXX("jkidZyxx","da725dd04c2211ec217041dd58091707","专业信息"),
    JKID_PKXQXX("jkidPkxqxx","26c01360fc2e11ec6bc3853e7344fce7","排课信息（学期）"),
    JKID_XKXQXX("jkidXkxqxx","11d90db0fc3011ec6bc3853e7344fce7","选课信息（学期）"),
    JKID_XSCJXQXX("jkidXscjxqxx","850d3db0fc3011ec6bc3853e7344fce7","学生成绩信息（学期）"),
    JKID_XSKSAPXQXX("jkidXsksapxqxx","c18b14b0fc3011ec6bc3853e7344fce7","学生考试安排信息（学期）"),
    ;
    
    private final String code;
    private final String desc;
    private final String msg;
    
    HhjtApiDzEnum(String code, String desc, String msg) {
        this.code = code;
        this.desc = desc;
        this.msg = msg;
    }
    
    public String getCode() {
        return this.code;
    }
    
    public String getDesc() {
        return this.desc;
    }
    
    public String getMsg() {
        return this.msg;
    }
    
    public static HhjtApiDzEnum get(String code) {
        for (HhjtApiDzEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }
}
