/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.basicinfo.model.domain.JcxxDcpz;
import com.xcwlkj.basicinfo.model.domain.JcxxDcrw;
import com.xcwlkj.basicinfo.model.dto.dataexporttask.ExportTaskAddV1DTO;
import com.xcwlkj.basicinfo.model.enums.DataFileTypeEnum;
import com.xcwlkj.basicinfo.model.enums.TaskStatusEnum;
import com.xcwlkj.basicinfo.model.vo.dataexporttask.ExportTaskAddV1VO;
import com.xcwlkj.basicinfo.model.vo.dataexporttask.TaskItemVO;
import com.xcwlkj.basicinfo.service.DataExportTaskFactory;
import com.xcwlkj.basicinfo.service.JcxxDcpzService;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.xcwlkj.basicinfo.mapper.JcxxDcrwMapper;
import com.xcwlkj.basicinfo.service.JcxxDcrwService;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import com.xcwlkj.basicinfo.model.dto.dataexporttask.TaskQueryDTO;
import com.xcwlkj.basicinfo.model.vo.dataexporttask.TaskQueryVO;
import com.xcwlkj.basicinfo.model.dto.dataexporttask.TaskListDTO;
import com.xcwlkj.basicinfo.model.vo.dataexporttask.TaskListVO;
import com.xcwlkj.base.exception.BusinessException;
import java.util.concurrent.TimeUnit;


/**
 * 导出任务表服务
 * <AUTHOR>
 * @version $Id: JcxxDcrwServiceImpl.java, v 0.1 2022年10月27日 09时50分 xcwlkj.com Exp $
 */
@Service("jcxxDcrwService")
public class JcxxDcrwServiceImpl  implements JcxxDcrwService  {
    private ExecutorService executorService = new ThreadPoolExecutor(2, 20,
            5L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>());

    @Resource
    private JcxxDcrwMapper modelMapper;
    @Resource
    private JcxxDcpzService jcxxDcpzService;
    @Resource
    private DataExportTaskFactory dataExportTaskFactory;

    /**
     * 增加任务
     * @param rw
     */
    @Override
    public void addTask(JcxxDcrw rw) {
        rw.setCjsj(new Date());
        rw.setXgsj(new Date());
        modelMapper.insertSelective(rw);
    }

    /**
     * 修改任务结果
     *
     * @param rw
     */
    @Override
    public void modifyTaskResult(JcxxDcrw rw) {
        rw.setXgsj(new Date());
        modelMapper.updateByPrimaryKeySelective(rw);
    }

    /**
     * @see com.xcwlkj.basicinfo.service.JcxxDcrwService#addV1(ExportTaskAddV1DTO)
     */
    @Override
    public ExportTaskAddV1VO addV1(ExportTaskAddV1DTO dto) {
        JcxxDcrw model = new JcxxDcrw();
        model.setBh(IdGenerateUtil.generateId());
        model.setPzbh(dto.getPzbh());//模板编号
        JcxxDcpz mb = jcxxDcpzService.selectByPrimaryKey(dto.getPzbh());
        DataFileTypeEnum fileType = DataFileTypeEnum.get(mb.getMblx());
        if (StringUtils.isBlank(dto.getRwmc())) {
            model.setRwmc(mb.getPzmc());
        }else {
            model.setRwmc(dto.getRwmc());
        }
        model.setRc(JSONObject.toJSONString(dto.getRc()));
        model.setRwlx("0");//任务类型，默认0：实时
        model.setJhzxsj(new Date());//计划执行时间
        model.setRwzt(TaskStatusEnum.wait.getCode());//任务状态
        addTask(model);

        // 任务类型实时时，线程池任务执行
        if("0".equals(model.getRwlx())){
            executorService.execute(() -> {
                dataExportTaskFactory.createTaskExecutor(fileType).execute(model);
            });
        }

        ExportTaskAddV1VO vo = new ExportTaskAddV1VO();
        vo.setBh(model.getBh());
        return vo;
    }
    /** 
     * @see com.xcwlkj.basicinfo.service.JcxxDcrwService#taskQuery(com.xcwlkj.basicinfo.model.dto.dataexporttask.TaskQueryDTO)
     */
	@Override
	public TaskQueryVO taskQuery(TaskQueryDTO dto) {

        TaskQueryVO taskQueryVO = new TaskQueryVO();
        JcxxDcrw jcxxDcrw = modelMapper.selectByPrimaryKey(dto.getBh());
        BeanUtil.copyProperties(jcxxDcrw, taskQueryVO);
        taskQueryVO.setZxwcsj(DateUtil.formatDateTime(jcxxDcrw.getZxwcsj()));

        return taskQueryVO;
    }
    /** 
     * @see com.xcwlkj.basicinfo.service.JcxxDcrwService#taskList(com.xcwlkj.basicinfo.model.dto.dataexporttask.TaskListDTO)
     */
	@Override
	public TaskListVO taskList(TaskListDTO dto) {
        TaskListVO result = new TaskListVO();
        PageHelper.startPage( dto.getPageNum(), dto.getPageSize());
        List<JcxxDcrw> list = modelMapper.selectAll();
        PageInfo<JcxxDcrw> pageInfo = new PageInfo<JcxxDcrw>(list);
        result.setTotalRows((int)pageInfo.getTotal());
        result.setTaskList(new ArrayList<>());
        for(JcxxDcrw model : list) {
            TaskItemVO itemVO = new TaskItemVO();
            itemVO.setPzbh(model.getPzbh());//配置编号
            itemVO.setRwmc(model.getRwmc());//任务名称
            itemVO.setRc(model.getRc());//入参
            itemVO.setZxwcsj(DateUtil.formatDateTime(model.getZxwcsj()));//执行完成时间
            itemVO.setZxjg(model.getZxjg());//执行结果
            itemVO.setJgms(model.getJgms());//结果描述
            itemVO.setDcwjdz(model.getDcwjdz());//导出文件地址
            itemVO.setRwzt(model.getRwzt());//任务状态
            result.getTaskList().add(itemVO);
        }
        return result;

    }
}