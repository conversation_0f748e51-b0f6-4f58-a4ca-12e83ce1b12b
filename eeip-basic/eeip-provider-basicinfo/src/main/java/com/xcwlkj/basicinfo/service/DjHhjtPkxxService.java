/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.service;

import com.xcwlkj.basicinfo.model.domain.DjHhjtPkxx;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * dj_hhjt_pkxx服务
 * <AUTHOR>
 * @version $Id: DjHhjtPkxxService.java, v 0.1 2023年08月31日 09时38分 xcwlkj.com Exp $
 */
@Service
public interface DjHhjtPkxxService extends BaseService<DjHhjtPkxx> {
    
    void insertList(List<DjHhjtPkxx> record);
    
    void removeDuplicates();
}