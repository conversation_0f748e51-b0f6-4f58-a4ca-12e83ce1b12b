/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.service;

import com.xcwlkj.basicinfo.model.domain.JcxxDcrw;
import com.xcwlkj.basicinfo.model.dto.dataexporttask.ExportTaskAddV1DTO;
import com.xcwlkj.basicinfo.model.vo.dataexporttask.ExportTaskAddV1VO;
import com.xcwlkj.basicinfo.model.dto.dataexporttask.TaskQueryDTO;
import com.xcwlkj.basicinfo.model.vo.dataexporttask.TaskQueryVO;
import com.xcwlkj.basicinfo.model.dto.dataexporttask.TaskListDTO;
import com.xcwlkj.basicinfo.model.vo.dataexporttask.TaskListVO;
import org.springframework.stereotype.Service;



/**
 * 导出任务表服务
 * <AUTHOR>
 * @version $Id: JcxxDcrwService.java, v 0.1 2022年10月27日 09时50分 xcwlkj.com Exp $
 */
@Service
public interface JcxxDcrwService  {


    /**
     * 增加任务
     * @param rw
     */
    void addTask(JcxxDcrw rw);

    /**
     * 修改任务结果
     * @param rw
     */
    void modifyTaskResult(JcxxDcrw rw);

    /**
     * 新建导出任务
     * @param dto
     * @return
     */
    ExportTaskAddV1VO addV1(ExportTaskAddV1DTO dto);
	/**
	 * 查询导出结果
	 * @param dto
	 * @return
	 */
	TaskQueryVO taskQuery(TaskQueryDTO dto);
	/**
	 * 导出任务列表
	 * @param dto
	 * @return
	 */
	TaskListVO taskList(TaskListDTO dto);
}