/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.basicinfo.model.domain.DjHhjtPkxx;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;



/**
 * dj_hhjt_pkxx数据库操作
 * <AUTHOR>
 * @version $Id: InitDjHhjtPkxxMapper.java, v 0.1 2023年08月31日 09时38分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface DjHhjtPkxxMapper extends MyMapper<DjHhjtPkxx> {

    /**
	 * 分页查询dj_hhjt_pkxx
	 * 
	 * @param example
	 * @return
	 */
	List<DjHhjtPkxx> pageList(DjHhjtPkxx example);
    
    void insertAllList(List<DjHhjtPkxx> list);
    
    void removeDuplicates();
}
