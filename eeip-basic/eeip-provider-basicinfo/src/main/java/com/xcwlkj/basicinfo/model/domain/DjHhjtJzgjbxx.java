/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.model.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;



/**
 * dj_hhjt_jzgjbxx
 * 
 * <AUTHOR>
 * @version $Id: DjHhjtJzgjbxx.java, v 0.1 2023年08月31日 10时37分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "dj_hhjt_jzgjbxx")
public class DjHhjtJzgjbxx implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 主键 */
    @Id
    @Column(name = "zj")
    private String            zj;
    /** 工号 */
    @Column(name = "gh")
    private String            gh;
    /** 姓名 */
    @Column(name = "xm")
    private String            xm;
    /** 英文姓名 */
    @Column(name = "ymxm")
    private String            ymxm;
    /** 曾用名 */
    @Column(name = "cym")
    private String            cym;
    /** 人员类别 */
    @Column(name = "rylb")
    private String            rylb;
    /** 性别 */
    @Column(name = "xb")
    private String            xb;
    /** 出生日期 */
    @Column(name = "csrq")
    private String            csrq;
    /** 年龄 */
    @Column(name = "nl")
    private String            nl;
    /** 民族 */
    @Column(name = "mz")
    private String            mz;
    /** 籍贯 */
    @Column(name = "jg")
    private String            jg;
    /** 国籍/地区 */
    @Column(name = "gj")
    private String            gj;
    /** 身份证件类别 */
    @Column(name = "sfzlb")
    private String            sfzlb;
    /** 身份证件号码 */
    @Column(name = "sfzjhm")
    private String            sfzjhm;
    /** 出生地 */
    @Column(name = "csd")
    private String            csd;
    /** 现住址 */
    @Column(name = "xzz")
    private String            xzz;
    /** 参加工作时间 */
    @Column(name = "cjgzsj")
    private String            cjgzsj;
    /** 从教起始时间 */
    @Column(name = "cjqssj")
    private String            cjqssj;
    /** 用人形式 */
    @Column(name = "yrxs")
    private String            yrxs;
    /** 入校时间 */
    @Column(name = "rxsj")
    private String            rxsj;
    /** 工龄 */
    @Column(name = "gl")
    private String            gl;
    /** 校龄 */
    @Column(name = "xl")
    private String            xl;
    /** 政治面貌 */
    @Column(name = "zzmm")
    private String            zzmm;
    /** 职务系列 */
    @Column(name = "zwxl")
    private String            zwxl;
    /** 参加党派时间 */
    @Column(name = "cjdpsj")
    private String            cjdpsj;
    /** 手机 */
    @Column(name = "sj")
    private String            sj;
    /** 邮箱 */
    @Column(name = "yx")
    private String            yx;
    /** 其他联系方式 */
    @Column(name = "qtlxfs")
    private String            qtlxfs;
    /** 健康状况 */
    @Column(name = "jkzk")
    private String            jkzk;
    /** 婚姻状况 */
    @Column(name = "hyzk")
    private String            hyzk;
    /** 教职工类别 */
    @Column(name = "jzglb")
    private String            jzglb;
    /** 教职工来源 */
    @Column(name = "jzgly")
    private String            jzgly;
    /** 研究方向 */
    @Column(name = "yjfx")
    private String            yjfx;
    /** 学缘结构 */
    @Column(name = "xyjg")
    private String            xyjg;
    /** 是否双肩挑 */
    @Column(name = "sfsjt")
    private String            sfsjt;
    /** 岗位类别 */
    @Column(name = "gwlb")
    private String            gwlb;
    /** 岗位等级 */
    @Column(name = "gwdj")
    private String            gwdj;
    /** 岗位聘用时间 */
    @Column(name = "gwpysj")
    private String            gwpysj;
    /** 行政职务 */
    @Column(name = "xzzw")
    private String            xzzw;
    /** 行政职务级别 */
    @Column(name = "xzzwjb")
    private String            xzzwjb;
    /** 行政职务聘任时间 */
    @Column(name = "xzzwprsj")
    private String            xzzwprsj;
    /** 现职称系列 */
    @Column(name = "xzcxl")
    private String            xzcxl;
    /** 现任职称专业 */
    @Column(name = "xrzczy")
    private String            xrzczy;
    /** 现任职称名称 */
    @Column(name = "xrzcmc")
    private String            xrzcmc;
    /** 现任职称取得时间 */
    @Column(name = "xrzcqdsj")
    private String            xrzcqdsj;
    /** 职称证书编号 */
    @Column(name = "zczsbh")
    private String            zczsbh;
    /** 职称发证单位 */
    @Column(name = "zcfzdw")
    private String            zcfzdw;
    /** 职称资格证上传 */
    @Column(name = "zczgzsc")
    private String            zczgzsc;
    /** 职称聘任证上传 */
    @Column(name = "zcprzsc")
    private String            zcprzsc;
    /** 其他职称名称 */
    @Column(name = "qtzcmc")
    private String            qtzcmc;
    /** 其他职称级别 */
    @Column(name = "qtzcjb")
    private String            qtzcjb;
    /** 其他职称证书编号 */
    @Column(name = "qtzczsbh")
    private String            qtzczsbh;
    /** 其他职称取得时间 */
    @Column(name = "qtzcqdsj")
    private String            qtzcqdsj;
    /** 其他职称聘任时间 */
    @Column(name = "qtzcprsj")
    private String            qtzcprsj;
    /** 是否有高校教师资格证 */
    @Column(name = "sfygxjszgz")
    private String            sfygxjszgz;
    /** 高校教师资格证编码 */
    @Column(name = "gxjszgzbm")
    private String            gxjszgzbm;
    /** 高校教师资格证发证时间 */
    @Column(name = "gxjszgzfzsj")
    private String            gxjszgzfzsj;
    /** 最高学历 */
    @Column(name = "zgxl")
    private String            zgxl;
    /** 最高学位层次 */
    @Column(name = "zgxwcc")
    private String            zgxwcc;
    /** 最高学历层次 */
    @Column(name = "zgxlcc")
    private String            zgxlcc;
    /** 最高学位 */
    @Column(name = "zgxw")
    private String            zgxw;
    /** 获得最高学历的院校或机构 */
    @Column(name = "hdzgxldyxhjg")
    private String            hdzgxldyxhjg;
    /** 最高学历所学专业 */
    @Column(name = "zgxlsxzy")
    private String            zgxlsxzy;
    /** 最高学历毕业时间 */
    @Column(name = "zgxlbysj")
    private String            zgxlbysj;
    /** 获得最高学位时间 */
    @Column(name = "hdzgxwsj")
    private String            hdzgxwsj;
    /** 获得最高学位的院校或机构 */
    @Column(name = "hdzgxwdyxhjg")
    private String            hdzgxwdyxhjg;
    /** 最高学历学制 */
    @Column(name = "zgxlxz")
    private String            zgxlxz;
    /** 第一学历层次 */
    @Column(name = "dyxlcc")
    private String            dyxlcc;
    /** 第一学历 */
    @Column(name = "dyxl")
    private String            dyxl;
    /** 第一学位层次 */
    @Column(name = "dyxwcc")
    private String            dyxwcc;
    /** 第一学位 */
    @Column(name = "dyxw")
    private String            dyxw;
    /** 获得第一学历的院校或机构 */
    @Column(name = "hddyxldyxhjg")
    private String            hddyxldyxhjg;
    /** 第一学历所学专业 */
    @Column(name = "dyxlsxzy")
    private String            dyxlsxzy;
    /** 第一学历毕业时间 */
    @Column(name = "dyxlbysj")
    private String            dyxlbysj;
    /** 第一学历学制 */
    @Column(name = "dyxlxz")
    private String            dyxlxz;
    /** 见习开始时间 */
    @Column(name = "jxkssj")
    private String            jxkssj;
    /** 见习结束时间 */
    @Column(name = "jxjssj")
    private String            jxjssj;
    /** 离退休时间 */
    @Column(name = "ltxsj")
    private String            ltxsj;
    /** 退休原因 */
    @Column(name = "txyy")
    private String            txyy;
    /** 减员原因 */
    @Column(name = "jyyy")
    private String            jyyy;
    /** 减员时间 */
    @Column(name = "jysj")
    private String            jysj;
    /** 专业技术职务取得时间 */
    @Column(name = "zyjszwqdsj")
    private String            zyjszwqdsj;
    /** 教师资格证上传 */
    @Column(name = "jszgzsc")
    private String            jszgzsc;
    /** 最高学历证书上传 */
    @Column(name = "zgxlzssc")
    private String            zgxlzssc;
    /** 最高学位证书上传 */
    @Column(name = "zgxwzssc")
    private String            zgxwzssc;
    /** 最高学历学习形式 */
    @Column(name = "zgxlxxxs")
    private String            zgxlxxxs;
    /** 第一学历学习形式 */
    @Column(name = "dyxlxxxs")
    private String            dyxlxxxs;
    /** 社保关系 */
    @Column(name = "sbgx")
    private String            sbgx;
    /** 校内档案编号 */
    @Column(name = "xndabh")
    private String            xndabh;
    /** 被借调部门 */
    @Column(name = "bjdbm")
    private String            bjdbm;
    /** 个人特长 */
    @Column(name = "grtc")
    private String            grtc;
    /** 现任职称级别 */
    @Column(name = "xrzcjb")
    private String            xrzcjb;
    /** 现任职称聘任时间 */
    @Column(name = "xrzcprsj")
    private String            xrzcprsj;
    /** 任教专业 */
    @Column(name = "rjzy")
    private String            rjzy;
    /** 现从事专业 */
    @Column(name = "zcszy")
    private String            zcszy;
    /** 编制类型 */
    @Column(name = "bzlx")
    private String            bzlx;
    /** 工作电话 */
    @Column(name = "gzdh")
    private String            gzdh;
    /** 关联主键 */
    @Column(name = "glzj")
    private String            glzj;
    /** 部门代码 */
    @Column(name = "bmdm")
    private String            bmdm;
    /** 部门名称 */
    @Column(name = "bmmc")
    private String            bmmc;
    /** 二级部门代码 */
    @Column(name = "rjbmdm")
    private String            rjbmdm;
    /** 二级部门名称 */
    @Column(name = "rjbmmc")
    private String            rjbmmc;
    /** 三级部门代码 */
    @Column(name = "sjbmdm")
    private String            sjbmdm;
    /** 三级部门名称 */
    @Column(name = "sjbmmc")
    private String            sjbmmc;
    /** 系统创建时间 */
    @Column(name = "sys_create_time")
    private String            sysCreateTime;
    /** 系统数据主键 */
    @Column(name = "sys_data_id")
    private String            sysDataId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


