/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.service.impl;

import com.xcwlkj.basicinfo.mapper.DjHhjtBmjbxxMapper;
import com.xcwlkj.basicinfo.model.domain.DjHhjtBmjbxx;
import com.xcwlkj.basicinfo.service.DjHhjtBmjbxxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * dj_hhjt_bmjbxx服务
 * <AUTHOR>
 * @version $Id: DjHhjtBmjbxxServiceImpl.java, v 0.1 2023年08月31日 09时38分 xcwlkj.com Exp $
 */
@Service("djHhjtBmjbxxService")
public class DjHhjtBmjbxxServiceImpl extends BaseServiceImpl<DjHhjtBmjbxxMapper, DjHhjtBmjbxx> implements DjHhjtBmjbxxService  {

    @Resource
    private DjHhjtBmjbxxMapper modelMapper;
    
    @Override
    public void insertList(List<DjHhjtBmjbxx> record) {
        modelMapper.insertAllList(record);
    }
}