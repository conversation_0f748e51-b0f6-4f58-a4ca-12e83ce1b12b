/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.dictdata;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 获取字典数据信息vo
 *
 * <AUTHOR>
 * @version $Id: DictItemVO.java, v 0.1 2022年10月12日 10时08分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DictDataItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private String id;
    /**
     * 字典标签
     */
    private String dictLabel;
    /**
     * 字典键值
     */
    private String dictValue;
    /**
     * 字典类型id
     */
    private String dictTypeId;
    /**
     * 字典类型
     */
    private String dictType;
    /**
     * 样式属性
     */
    private String cssClass;
    /**
     * 表格回显样式
     */
    private String listClass;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 是否默认
     */
    private Integer isDefault;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
