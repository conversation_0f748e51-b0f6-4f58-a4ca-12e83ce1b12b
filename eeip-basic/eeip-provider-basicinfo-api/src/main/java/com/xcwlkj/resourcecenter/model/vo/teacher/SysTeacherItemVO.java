/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.teacher;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 查询教职工信息vo
 * <AUTHOR>
 * @version $Id: TeacherItemVO.java, v 0.1 2022年10月13日 09时53分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SysTeacherItemVO implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 单位号 */
    private String dwh;
    /** 单位名称 */
    private String dwmc;
    /** 职工号 */
    private String gh;
    /** 教师姓名 */
    private String xm;
    /** 二级学科码 */
    private String ejxkm;
    /** 性别码 */
    private String xbm;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
