/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.classroom;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 教室列表vo
 * <AUTHOR>
 * @version $Id: ClassroomItemVO.java, v 0.1 2022年10月28日 10时27分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SysClassroomItemVO implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 教室号 */
    private String jsh;
    /** 教室名称 */
    private String jsmc;
    /** 校区号 */
    private String xqh;
    /** 校区名称 */
    private String xqmc;
    /** 教学楼号 */
    private String jxlh;
    /** 教学楼名称 */
    private String jxlmc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
