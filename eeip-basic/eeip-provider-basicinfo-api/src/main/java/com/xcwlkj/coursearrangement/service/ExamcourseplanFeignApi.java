/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


import com.xcwlkj.core.annotation.mock.YapiMock;
import com.xcwlkj.coursearrangement.service.hystrix.ExamcourseplanFeignHystrix;
import com.xcwlkj.coursearrangement.model.dto.examcourseplan.SyncPlanListDTO;
import com.xcwlkj.coursearrangement.model.vo.examcourseplan.SyncPlanListVO;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * 
 * <AUTHOR>
 * @version $Id: ExamcourseplanFeignApi.java, v 0.1 2023年07月24日 17时20分 xcwlkj.com Exp $
 */
@FeignClient(value = "eeip-basicinfo-service" , contextId = "ExamcourseplanFeignApi", fallback = ExamcourseplanFeignHystrix.class)
public interface ExamcourseplanFeignApi {

   
	/**
	 * 考试课程计划同步
	 * @param syncPlanListDto
	 * @return
	 */
	@YapiMock(projectId="2166", returnClass = SyncPlanListVO.class)
    @PostMapping(value = "/sys/coursearrangement/examCoursePlan/syncPlanList")
    Wrapper<SyncPlanListVO> syncPlanList(@RequestBody SyncPlanListDTO syncPlanListDto);
}




