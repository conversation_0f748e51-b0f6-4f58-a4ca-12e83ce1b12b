/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.vo.bj;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 班级绑定详情vo
 *
 * <AUTHOR>
 * @version $Id: BjExtraDetailVO.java, v 0.1 2024年12月17日 14时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BjExtraDetailVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /** 班级名称 */
    private String bjmc;
    /** 班主任名称 */
    private String bzrmc;
    /** 班级口号 */
    private String slogan;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
