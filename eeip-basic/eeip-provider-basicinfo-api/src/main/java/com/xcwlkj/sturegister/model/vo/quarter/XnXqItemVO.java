/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.vo.quarter;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 查询学期信息vo
 *
 * <AUTHOR>
 * @version $Id: QuarterItemVO.java, v 0.1 2022年10月12日 09时34分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class XnXqItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private String bh;
    /**
     * 年度
     * 格式：YYYY，如：2008
     */
    private String nd;
    /**
     * 学年（度）
     * 格式：YYYY-YYYY，如：2001-2002
     */
    private String xn;
    /**
     * 学期码
     */
    private String xqm;
    /**
     *
     */
    private String ksrq;
    /**
     *
     */
    private String jsrq;
    /**
     * 创建时间
     */
    private String cjsj;
    /**
     * 修改时间
     */
    private String xgsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
