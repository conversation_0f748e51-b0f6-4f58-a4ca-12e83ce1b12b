/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.examarrangement.model.domain.JcxxKsbmxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 考生报名信息表数据库操作
 * <AUTHOR>
 * @version $Id: InitJcxxKsbmxxMapper.java, v 0.1 2023年07月14日 08时56分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface JcxxKsbmxxMapper extends MyMapper<JcxxKsbmxx> {

    /**
	 * 分页查询jcxx_ksbmxx
	 * 
	 * @param example
	 * @return
	 */
	List<JcxxKsbmxx> pageList(JcxxKsbmxx example);

	/**
	 * 根据考试计划编号统计考生报名信息
	 *
	 * @param ksjhbh
	 * @return
	 */
	int countByKsjhbh(@Param("ksjhbh") String ksjhbh);

	/**
	 * 查询已关联考生报名信息的考试计划编号列表
	 *
	 * @return
	 */
	List<String> selectKsjgbhList();
}
