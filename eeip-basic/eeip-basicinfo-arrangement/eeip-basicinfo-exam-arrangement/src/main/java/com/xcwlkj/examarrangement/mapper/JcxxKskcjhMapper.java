/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.mapper;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.examarrangement.model.domain.JcxxKskcjh;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 考试课程计划表数据库操作
 * <AUTHOR>
 * @version $Id: InitJcxxKskcjhMapper.java, v 0.1 2023年07月13日 14时42分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface JcxxKskcjhMapper extends MyMapper<JcxxKskcjh> {

    /**
     * 分页查询jcxx_kskcjh
     *
     * @param example
     * @return
     */
    List<JcxxKskcjh> pageList(JcxxKskcjh example);
}
