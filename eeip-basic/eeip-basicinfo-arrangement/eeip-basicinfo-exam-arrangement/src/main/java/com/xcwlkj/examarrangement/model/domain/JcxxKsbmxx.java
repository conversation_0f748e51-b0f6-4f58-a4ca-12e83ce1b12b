/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


/**
 * 考生报名信息表
 * 
 * <AUTHOR>
 * @version $Id: JcxxKsbmxx.java, v 0.1 2023年07月14日 08时56分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "jcxx_ksbmxx")
public class JcxxKsbmxx implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 考生唯一标志号 */
    @Id
    @Column(name = "bh")
    private String            bh;
    /** 考试名称 */
    @Column(name = "ksmc")
    private String            ksmc;
    /** 考试计划标志号 */
    @Column(name = "ksjhid")
    private String            ksjhid;
    /** 学号 */
    @Column(name = "xh")
    private String            xh;
    /** 考生号 */
    @Column(name = "ksh")
    private String            ksh;
    /** 考生姓名 */
    @Column(name = "xm")
    private String            xm;
    /** 性别码 */
    @Column(name = "xbm")
    private String            xbm;
    /** 性别 */
    @Column(name = "xb")
    private String            xb;
    /** 身份证件类型码 */
    @Column(name = "sfzjlxm")
    private String            sfzjlxm;
    /** 身份证件类型 */
    @Column(name = "sfzjlx")
    private String            sfzjlx;
    /** 身份证号码 */
    @Column(name = "sfzjhm")
    private String            sfzjhm;
    /** 出生日期 */
    @Column(name = "csrq")
    private String            csrq;
    /** 民族码 */
    @Column(name = "mzm")
    private String            mzm;
    /** 民族 */
    @Column(name = "mz")
    private String            mz;
    /** 户口所在地码 */
    @Column(name = "hkszdm")
    private String            hkszdm;
    /** 户口所在地 */
    @Column(name = "hkszd")
    private String            hkszd;
    /** 照片 */
    @Column(name = "zp")
    private String            zp;
    /** 状态 */
    @Column(name = "zt")
    private String            zt;
    /** 课程号 */
    @Column(name = "kch")
    private String            kch;
    /** 课程名称 */
    @Column(name = "kcmc")
    private String            kcmc;
    /** 创建时间 */
    @Column(name = "cjsj")
    private Date            cjsj;
    /** 更新时间 */
    @Column(name = "xgsj")
    private Date            xgsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


