<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.examarrangement.mapper.JcxxKskcjhMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.examarrangement.model.domain.JcxxKskcjh">
        <id column="bh" jdbcType="VARCHAR" property="bh" />
        <result column="ksjhid" jdbcType="VARCHAR" property="ksjhid" />
        <result column="xn" jdbcType="VARCHAR" property="xn" />
        <result column="xq" jdbcType="VARCHAR" property="xq" />
        <result column="kch" jdbcType="VARCHAR" property="kch" />
        <result column="zc" jdbcType="BIGINT" property="zc" />
        <result column="ksmc" jdbcType="VARCHAR" property="ksmc" />
        <result column="kcmc" jdbcType="VARCHAR" property="kcmc" />
        <result column="kspch" jdbcType="BIGINT" property="kspch" />
        <result column="ksrq" jdbcType="DATE" property="ksrq" />
        <result column="kskssj" jdbcType="TIMESTAMP" property="kskssj" />
        <result column="ksjssj" jdbcType="TIMESTAMP" property="ksjssj" />
        <result column="kssc" jdbcType="BIGINT" property="kssc" />
        <result column="ksfslxm" jdbcType="VARCHAR" property="ksfslxm" />
        <result column="xgsj" jdbcType="TIMESTAMP" property="xgsj" />
        <result column="cjsj" jdbcType="TIMESTAMP" property="cjsj" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        bh,
        ksjhid,
        xn,
        xq,
        kch,
        zc,
        ksmc,
        kcmc,
        kspch,
        ksrq,
        kskssj,
        ksjssj,
        kssc,
        ksfslxm,
        xgsj,
        cjsj

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="bh != null and bh != ''">
            AND bh = #{bh,jdbcType=VARCHAR}
        </if>
        <if test="ksjhid != null and ksjhid != ''">
            AND ksjhid = #{ksjhid,jdbcType=VARCHAR}
        </if>
        <if test="xn != null and xn != ''">
            AND xn = #{xn,jdbcType=VARCHAR}
        </if>
        <if test="xq != null and xq != ''">
            AND xq = #{xq,jdbcType=VARCHAR}
        </if>
        <if test="kch != null and kch != ''">
            AND kch = #{kch,jdbcType=VARCHAR}
        </if>
        <if test="zc != null and zc != ''">
            AND zc = #{zc,jdbcType=BIGINT}
        </if>
        <if test="ksmc != null and ksmc != ''">
            AND ksmc = #{ksmc,jdbcType=VARCHAR}
        </if>
        <if test="kcmc != null and kcmc != ''">
            AND kcmc = #{kcmc,jdbcType=VARCHAR}
        </if>
        <if test="kspch != null and kspch != ''">
            AND kspch = #{kspch,jdbcType=BIGINT}
        </if>
        <if test="ksrq != null and ksrq != ''">
            AND ksrq = #{ksrq,jdbcType=DATE}
        </if>
        <if test="kskssj != null and kskssj != ''">
            AND kskssj = #{kskssj,jdbcType=TIMESTAMP}
        </if>
        <if test="ksjssj != null and ksjssj != ''">
            AND ksjssj = #{ksjssj,jdbcType=TIMESTAMP}
        </if>
        <if test="kssc != null and kssc != ''">
            AND kssc = #{kssc,jdbcType=BIGINT}
        </if>
        <if test="ksfslxm != null and ksfslxm != ''">
            AND ksfslxm = #{ksfslxm,jdbcType=VARCHAR}
        </if>
        <if test="xgsj != null and xgsj != ''">
            AND xgsj = #{xgsj,jdbcType=TIMESTAMP}
        </if>
        <if test="cjsj != null and cjsj != ''">
            AND cjsj = #{cjsj,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="bh != null ">
            bh = #{bh,jdbcType=VARCHAR},
        </if>
        <if test="ksjhid != null ">
            ksjhid = #{ksjhid,jdbcType=VARCHAR},
        </if>
        <if test="xn != null ">
            xn = #{xn,jdbcType=VARCHAR},
        </if>
        <if test="xq != null ">
            xq = #{xq,jdbcType=VARCHAR},
        </if>
        <if test="kch != null ">
            kch = #{kch,jdbcType=VARCHAR},
        </if>
        <if test="zc != null ">
            zc = #{zc,jdbcType=BIGINT},
        </if>
        <if test="ksmc != null ">
            ksmc = #{ksmc,jdbcType=VARCHAR},
        </if>
        <if test="kcmc != null ">
            kcmc = #{kcmc,jdbcType=VARCHAR},
        </if>
        <if test="kspch != null ">
            kspch = #{kspch,jdbcType=BIGINT},
        </if>
        <if test="ksrq != null ">
            ksrq = #{ksrq,jdbcType=DATE},
        </if>
        <if test="kskssj != null ">
            kskssj = #{kskssj,jdbcType=TIMESTAMP},
        </if>
        <if test="ksjssj != null ">
            ksjssj = #{ksjssj,jdbcType=TIMESTAMP},
        </if>
        <if test="kssc != null ">
            kssc = #{kssc,jdbcType=BIGINT},
        </if>
        <if test="ksfslxm != null ">
            ksfslxm = #{ksfslxm,jdbcType=VARCHAR},
        </if>
        <if test="xgsj != null ">
            xgsj = #{xgsj,jdbcType=TIMESTAMP},
        </if>
        <if test="cjsj != null ">
            cjsj = #{cjsj,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

    <!-- 分页查询 -->
    <select id="pageList" parameterType="com.xcwlkj.examarrangement.model.domain.JcxxKskcjh"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jcxx_ksjh
        where 1=1
        <include refid="Base_Where_Condition"/>
        <include refid="Base_OrderBy_Condition"/>
    </select>
</mapper>
