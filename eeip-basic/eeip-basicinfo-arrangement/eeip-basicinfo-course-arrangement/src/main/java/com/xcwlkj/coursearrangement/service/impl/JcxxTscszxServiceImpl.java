/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.service.impl;

import com.xcwlkj.coursearrangement.mapper.JcxxTscszxMapper;
import com.xcwlkj.coursearrangement.model.domain.JcxxTscszx;
import com.xcwlkj.coursearrangement.model.dto.work.WorkBindJshDTO;
import com.xcwlkj.coursearrangement.model.dto.work.WorkBindingDetailDTO;
import com.xcwlkj.coursearrangement.model.vo.work.WorkBindingDetailVO;
import com.xcwlkj.coursearrangement.service.JcxxTscszxService;
import com.xcwlkj.resourcecenter.mapper.JcxxJsjbxxMapper;
import com.xcwlkj.resourcecenter.model.domain.JcxxJsjbxx;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 特殊场所作息表（教室）服务
 * <AUTHOR>
 * @version $Id: JcxxTscszxServiceImpl.java, v 0.1 2024年08月05日 10时03分 xcwlkj.com Exp $
 */
@Service("jcxxTscszxService")
public class JcxxTscszxServiceImpl  implements JcxxTscszxService  {

    @Resource
    private JcxxTscszxMapper modelMapper;

    @Resource
    private JcxxJsjbxxMapper jcxxJsjbxxMapper;


    @Override
    public void workBindJsh(WorkBindJshDTO dto) {
        // 清空该作息下的所有教室绑定
        modelMapper.clearByZxbh(dto.getZxbh());
        // 重新绑定作息教室关系
        List<String> jshList = dto.getJshList();
        for (String jsh :jshList){
            // 查询教室名称
            JcxxJsjbxx jcxxJsjbxx = jcxxJsjbxxMapper.selectByPrimaryKey(jsh);

            Example example = new Example(JcxxTscszx.class);
            example.createCriteria().andEqualTo("jsh", jsh);
            List<JcxxTscszx> jcxxTscszxes = modelMapper.selectByExample(example);
            JcxxTscszx jcxxTscszx = new JcxxTscszx();
            if (CollectionUtils.isNotEmpty(jcxxTscszxes)){
                jcxxTscszx = jcxxTscszxes.get(0);
                jcxxTscszx.setZxbh(dto.getZxbh());
                modelMapper.updateByPrimaryKey(jcxxTscszx);
            } else {
                jcxxTscszx = new JcxxTscszx();
                jcxxTscszx.setZxbh(dto.getZxbh());
                jcxxTscszx.setJsh(jsh);
                jcxxTscszx.setJsmc(jcxxJsjbxx == null ? null : jcxxJsjbxx.getJsmc());
                jcxxTscszx.setCjsj(new Date());
                modelMapper.insert(jcxxTscszx);
            }
        }
    }

    @Override
    public WorkBindingDetailVO workBindingDetail(WorkBindingDetailDTO dto) {
        WorkBindingDetailVO result = new WorkBindingDetailVO();
        List<String> jshList = modelMapper.workBindingDetail(dto.getZxbh());
        result.setJshList(jshList);
        return result;
    }
}