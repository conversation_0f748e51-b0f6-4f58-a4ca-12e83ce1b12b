/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.service;

import com.xcwlkj.coursearrangement.model.domain.JcxxZxjc;
import com.xcwlkj.coursearrangement.model.dto.work.GetCurrEnabledZxjcDTO;
import com.xcwlkj.coursearrangement.model.dto.work.GetZxjcByTimeDTO;
import com.xcwlkj.coursearrangement.model.dto.work.RemoteWorkBindingDetailByJshDTO;
import com.xcwlkj.coursearrangement.model.dto.work.WorkAddItemDTO;
import com.xcwlkj.coursearrangement.model.dto.work.WorkBindingDetailByJshDTO;
import com.xcwlkj.coursearrangement.model.dto.work.WorkDeleteItemDTO;
import com.xcwlkj.coursearrangement.model.dto.work.WorkUpdateItemDTO;
import com.xcwlkj.coursearrangement.model.vo.work.GetCurrEnabledZxjcVO;
import com.xcwlkj.coursearrangement.model.vo.work.GetZxjcByTimeVO;
import com.xcwlkj.coursearrangement.model.vo.work.RemoteWorkBindingDetailByJshVO;
import com.xcwlkj.coursearrangement.model.vo.work.WorkBindingDetailByJshVO;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * jcxx_zxjc服务
 *
 * <AUTHOR>
 * @version $Id: JcxxZxjcService.java, v 0.1 2022年09月27日 14时49分 xcwlkj.com Exp $
 */
@Service
public interface JcxxZxjcService extends BaseService<JcxxZxjc> {

    /**
     * 修改子项-作息
     *
     * @param dto
     * @return
     */
    void workUpdateItem(WorkUpdateItemDTO dto);

    /**
     * 添加子项-作息
     *
     * @param dto
     * @return
     */
    void workAddItem(WorkAddItemDTO dto);

    /**
     * 删除子项-作息
     *
     * @param dto
     * @return
     */
    void workDeleteItem(WorkDeleteItemDTO dto);

    List<JcxxZxjc> getJc(String bh);
	/**
	 * 根据时间获取节次信息
	 * @param dto
	 * @return
	 */
	GetZxjcByTimeVO getZxjcByTime(GetZxjcByTimeDTO dto);
	/**
	 * 获取当前启用的作息节次
	 * @param dto
	 * @return
	 */
	GetCurrEnabledZxjcVO getCurrEnabledZxjc(GetCurrEnabledZxjcDTO dto);
	/**
	 * 根据教室号获取节次信息
	 * @param dto
	 * @return
	 */
	WorkBindingDetailByJshVO workBindingDetailByJsh(WorkBindingDetailByJshDTO dto);
	/**
	 * 根据教室号获取节次信息-远程服务
	 * @param dto
	 * @return
	 */
	RemoteWorkBindingDetailByJshVO remoteWorkBindingDetailByJsh(RemoteWorkBindingDetailByJshDTO dto);
}