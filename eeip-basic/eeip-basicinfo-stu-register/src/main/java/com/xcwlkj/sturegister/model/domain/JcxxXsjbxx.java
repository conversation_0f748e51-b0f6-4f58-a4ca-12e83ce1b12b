/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * jcxx_xsjbxx
 *
 * <AUTHOR>
 * @version $Id: JcxxXsjbxx.java, v 0.1 2022年09月27日 14时43分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "jcxx_xsjbxx")
public class JcxxXsjbxx implements Serializable {
    /**
     * 序列id
     */
    private static final long serialVersionUID = 1L;
    /**
     * 学号
     */
    @Id
    @Column(name = "xh")
    private String xh;
    /**
     * 姓名
     */
    @Column(name = "xm")
    private String xm;
    /**
     * 性别码
     */
    @Column(name = "xbm")
    private String xbm;
    /**
     * 民族码
     */
    @Column(name = "mzm")
    private String mzm;
    /**
     * 身份证件类型码
     */
    @Column(name = "sfzjlxm")
    private String sfzjlxm;
    /**
     * 身份证件号
     */
    @Column(name = "sfzjh")
    private String sfzjh;
    /**
     * 政治面貌码
     */
    @Column(name = "zzmmm")
    private String zzmmm;
    /**
     * 照片
     */
    @Column(name = "zp")
    private String zp;
    /**
     * 英文姓名
     */
    @Column(name = "ywxm")
    private String ywxm;
    /**
     * 姓名拼音
     */
    @Column(name = "xmpy")
    private String xmpy;
    /**
     * 曾用名
     */
    @Column(name = "cym")
    private String cym;
    /**
     * 出生日期
     */
    @Column(name = "csrq")
    private Date csrq;
    /**
     * 出生地码
     */
    @Column(name = "csdm")
    private String csdm;
    /**
     * 籍贯
     */
    @Column(name = "jg")
    private String jg;
    /**
     * 国籍/地区码
     */
    @Column(name = "gjdqm")
    private String gjdqm;
    /**
     * 婚姻状况码
     */
    @Column(name = "hyzkm")
    private String hyzkm;
    /**
     * 港澳台侨外码
     */
    @Column(name = "gatqwm")
    private String gatqwm;
    /**
     * 健康状况码
     */
    @Column(name = "jkzkm")
    private String jkzkm;
    /**
     * 信仰宗教码
     */
    @Column(name = "xyzjm")
    private String xyzjm;
    /**
     * 血型码
     */
    @Column(name = "xxm")
    private String xxm;
    /**
     * 身份证件有效期
     */
    @Column(name = "sfzjyxq")
    private Date sfzjyxq;
    /**
     * 是否独生子女
     */
    @Column(name = "sfdszn")
    private String sfdszn;
    /**
     * 创建时间
     */
    @Column(name = "cjsj")
    private Date cjsj;
    /**
     * 修改时间
     */
    @Column(name = "xgsj")
    private Date xgsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


