package com.xcwlkj.sturegister.model.enums;

import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.util.StringUtil;

public enum ClassEnum {
    XZB("0", "行政班"),
    XXB("1", "选修班");

    private String code;
    private String desc;

    ClassEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ClassEnum get(String code) {
        for (ClassEnum c : values()) {
            if (StringUtil.equals(c.code, code)) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值：" + code);
    }
}
