/**
 * xcwlkj.com Inc
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.web;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.xcwlkj.resourcecenter.service.DictdataFeignApi;
import com.xcwlkj.base.BaseFeignClient;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.resourcecenter.model.dto.dictdata.GetDataInfoDTO;
import org.springframework.validation.annotation.Validated;
import com.xcwlkj.resourcecenter.model.vo.dictdata.GetDataInfoVO;
import com.xcwlkj.resourcecenter.service.JcxxDictDataService;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * Dictdata接口
 *
 * <AUTHOR>
 * @version $Id: DictdataFeignClient.java, v 0.1 2022年10月12日 10时08分 xcwlkj.com Exp $
 */
@RestController
public class DictdataFeignClient extends BaseFeignClient implements DictdataFeignApi {

    @Resource
    private JcxxDictDataService jcxxDictDataService;


    /**
     * @see com.xcwlkj.resourcecenter.service.DictdataFeignApi#getDataInfo(com.xcwlkj.resourcecenter.model.dto.dictdata.GetDataInfoDTO)
     */
    @Override
    public Wrapper<GetDataInfoVO> getDataInfo(@RequestBody @Validated GetDataInfoDTO dto) {
        logger.info("获取字典数据信息GetDataInfoDTO={}", dto);
        GetDataInfoVO result = jcxxDictDataService.getDataInfo(dto);
        logger.info("getDataInfo - 获取字典数据信息. [OK] GetDataInfoVO={}", result);
        return WrapMapper.ok(result);
    }
}



