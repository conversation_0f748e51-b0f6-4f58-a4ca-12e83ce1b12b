/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * jcxx_dict_data
 *
 * <AUTHOR>
 * @version $Id: JcxxDictData.java, v 0.1 2022年09月27日 14时44分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "jcxx_dict_data")
public class JcxxDictData implements Serializable {
    /**
     * 序列id
     */
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @Id
    @Column(name = "id")
    private String id;
    /**
     * 字典标签
     */
    @Column(name = "dict_label")
    private String dictLabel;
    /**
     * 字典键值
     */
    @Column(name = "dict_value")
    private String dictValue;
    /**
     * 字典类型id
     */
    @Column(name = "dict_type_id")
    private String dictTypeId;
    /**
     * 字典类型
     */
    @Column(name = "dict_type")
    private String dictType;
    /**
     * 样式属性
     */
    @Column(name = "css_class")
    private String cssClass;
    /**
     * 表格回显样式
     */
    @Column(name = "list_class")
    private String listClass;
    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 是否默认
     */
    @Column(name = "is_default")
    private Integer isDefault;
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;
    /**
     * 最后操作时间
     */
    @Column(name = "last_operator_time")
    private Date lastOperatorTime;
    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;
    /**
     * 创建人ID
     */
    @Column(name = "creator_id")
    private String creatorId;
    /**
     * 最近操作人
     */
    @Column(name = "last_operator")
    private String lastOperator;
    /**
     * 最后操作人ID
     */
    @Column(name = "last_operator_id")
    private String lastOperatorId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


