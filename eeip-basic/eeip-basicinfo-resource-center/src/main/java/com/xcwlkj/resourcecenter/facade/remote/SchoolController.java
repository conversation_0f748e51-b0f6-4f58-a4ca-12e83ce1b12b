/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.facade.remote;

import com.xcwlkj.base.BaseController;
import com.xcwlkj.resourcecenter.model.dto.school.DetailsV1DTO;
import com.xcwlkj.resourcecenter.model.req.school.DetailsReqModel;
import com.xcwlkj.resourcecenter.model.resp.school.DetailsRespModel;
import com.xcwlkj.resourcecenter.model.vo.school.DetailsV1VO;
import com.xcwlkj.resourcecenter.service.JcxxXxjbxxService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * School控制层
 * <AUTHOR>
 * @version $Id: SchoolController.java, v 0.1 2024年11月06日 14时16分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("SchoolRemoteController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class SchoolController extends BaseController {

    @Resource
    private JcxxXxjbxxService jcxxXxjbxxService;
	
   /**
    * 学校信息
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/remote/resourcecenter/school/details")
    public Wrapper<DetailsRespModel> details(@Validated @RequestBody DetailsReqModel reqModel) {
		log.info("收到请求开始：[学校信息][/remote/resourcecenter/school/details]reqModel:"+reqModel.toString());

        DetailsV1VO detailsV1VO = jcxxXxjbxxService.detailsV1(new DetailsV1DTO());

        DetailsRespModel respModel = new DetailsRespModel();
        respModel.setXxdm(detailsV1VO.getXxdm());
        respModel.setXxmc(detailsV1VO.getXxmc());
        respModel.setMode(detailsV1VO.getMode());
		log.info("处理请求结束：[学校信息][/remote/resourcecenter/school/details]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

}