/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.facade.manager;

import com.xcwlkj.base.BaseController;
import com.xcwlkj.resourcecenter.model.dto.camera.CameraSipListDTO;
import com.xcwlkj.resourcecenter.model.dto.camera.CameraStatusCountDTO;
import com.xcwlkj.resourcecenter.model.dto.camera.CameraStatusListDTO;
import com.xcwlkj.resourcecenter.model.dto.camera.GetNodeInfoDTO;
import com.xcwlkj.resourcecenter.model.dto.camera.PlayDTO;
import com.xcwlkj.resourcecenter.model.req.camera.CameraSipListReqModel;
import com.xcwlkj.resourcecenter.model.req.camera.CameraStatusCountReqModel;
import com.xcwlkj.resourcecenter.model.req.camera.CameraStatusListReqModel;
import com.xcwlkj.resourcecenter.model.req.camera.GetNodeInfoReqModel;
import com.xcwlkj.resourcecenter.model.req.camera.PlayReqModel;
import com.xcwlkj.resourcecenter.model.resp.camera.CameraSipListRespModel;
import com.xcwlkj.resourcecenter.model.resp.camera.CameraStatusCountRespModel;
import com.xcwlkj.resourcecenter.model.resp.camera.CameraStatusListRespModel;
import com.xcwlkj.resourcecenter.model.resp.camera.GetNodeInfoRespModel;
import com.xcwlkj.resourcecenter.model.resp.camera.PlayRespModel;
import com.xcwlkj.resourcecenter.model.vo.camera.CameraSipListVO;
import com.xcwlkj.resourcecenter.model.vo.camera.CameraStatusCountVO;
import com.xcwlkj.resourcecenter.model.vo.camera.CameraStatusListVO;
import com.xcwlkj.resourcecenter.model.vo.camera.DefaultPlayListVO;
import com.xcwlkj.resourcecenter.model.vo.camera.GetNodeInfoVO;
import com.xcwlkj.resourcecenter.service.JcxxSbxxService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Camera控制层
 *
 * <AUTHOR>
 * @version $Id: CameraController.java, v 0.1 2023年05月17日 15时20分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("CameraManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class CameraController extends BaseController {
    @Resource
    private JcxxSbxxService jcxxSbxxService;

    /**
     * 摄像机状态列表
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/manager/resourcecenter/v1/cameraStatus/list")
    public Wrapper<CameraStatusListRespModel> cameraStatusList(@RequestBody CameraStatusListReqModel reqModel) {
        log.info("收到请求开始：[摄像机状态列表][/manager/resourcecenter/v1/cameraStatus/list]reqModel:" + reqModel.toString());
        CameraStatusListDTO dto = new CameraStatusListDTO();
        dto.setSbmc(reqModel.getSbmc());
//        dto.setSbzt(reqModel.getSbzt());
        dto.setLxzt(reqModel.getLxzt());
        dto.setUsage(reqModel.getUsage());
//        dto.setJgid(reqModel.getJgid());
//        dto.setJglx(reqModel.getJglx());
        dto.setJgid("91");
        dto.setJglx("xq");
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        CameraStatusListVO result = jcxxSbxxService.cameraStatusList(dto);

        CameraStatusListRespModel respModel = new CameraStatusListRespModel();
        respModel.setTotalNum(result.getTotalNum());
        respModel.setSuspendNum(result.getSuspendNum());
        respModel.setRunningNum(result.getRunningNum());
//        respModel.setOnlineNum(result.getOnlineNum());
//        respModel.setOfflineNum(result.getOfflineNum());
//        respModel.setNotEnabledNum(result.getNotEnabledNum());
        respModel.setSbList(result.getSbList());
        respModel.setTotal(result.getTotal());
        log.info("处理请求结束：[摄像机状态列表][/manager/resourcecenter/v1/cameraStatus/list]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 摄像机状态统计
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/manager/resourcecenter/v1/cameraStatus/count")
    public Wrapper<CameraStatusCountRespModel> cameraStatusCount(@RequestBody CameraStatusCountReqModel reqModel) {
        log.info("收到请求开始：[摄像机状态统计][/manager/resourcecenter/v1/cameraStatus/count]reqModel:" + reqModel.toString());
        CameraStatusCountDTO dto = new CameraStatusCountDTO();
        dto.setUsage(reqModel.getUsage());
        dto.setJgid(reqModel.getJgid());
        dto.setJglx(reqModel.getJglx());
        // 北科大本校校区号91
//        dto.setJgid("91");
//        dto.setJglx("xq");
        CameraStatusCountVO result = jcxxSbxxService.cameraStatusCount(dto);

        CameraStatusCountRespModel respModel = new CameraStatusCountRespModel();
        respModel.setTotalNum(result.getTotalNum());
        respModel.setSuspendNum(result.getSuspendNum());
        respModel.setRunningNum(result.getRunningNum());
        respModel.setJxpgNum(result.getJxpgNum());
        respModel.setBzhkcNum(result.getBzhkcNum());
        log.info("处理请求结束：[摄像机状态统计][/manager/resourcecenter/v1/cameraStatus/count]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 摄像机状态列表
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/manager/resourcecenter/v1/camera/getNodeInfo")
    public Wrapper<GetNodeInfoRespModel> getNodeInfo(@RequestBody GetNodeInfoReqModel reqModel) {
        log.info("收到请求开始：[获取节点信息][/GetNodeInfoDTO/resourcecenter/v1/camera/getNodeInfo]reqModel:" + reqModel.toString());
        GetNodeInfoDTO dto = new GetNodeInfoDTO();
        dto.setSbxxbh(reqModel.getSbxxbh());
        GetNodeInfoVO result = jcxxSbxxService.getNodeInfo(dto);

        GetNodeInfoRespModel respModel = new GetNodeInfoRespModel();
        respModel.setSbxxbh(result.getSbxxbh());
        respModel.setSbmc(result.getSbmc());
        respModel.setSbip(result.getSbip());
        respModel.setSipdz(result.getSipdz());

        log.info("处理请求结束：[获取节点信息][/manager/resourcecenter/v1/camera/getNodeInfo]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 北科大摄像机列表
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/manager/resourcecenter/v1/camera/cameraSipList")
    public Wrapper<CameraSipListRespModel> cameraCameraSipList(@RequestBody CameraSipListReqModel reqModel) {
        log.info("收到请求开始：[北科大摄像机列表][/manager/resourcecenter/v1/camera/cameraSipList]reqModel:" + reqModel.toString());
        CameraSipListDTO dto = new CameraSipListDTO();
        dto.setType(reqModel.getType());
        dto.setJglx(reqModel.getJglx());
        dto.setJgid(reqModel.getJgid());
        dto.setCslx(reqModel.getCslx());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());

        CameraSipListVO result = jcxxSbxxService.getCameraSipList(dto);
        CameraSipListRespModel respModel = new CameraSipListRespModel();
        respModel.setSipList(result.getSipList());
        respModel.setTotalNum(result.getTotalNum());
        log.info("处理请求结束：[北科大摄像机列表][/manager/resourcecenter/v1/camera/cameraSipList]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 显示点播日志
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/resourcecenter/v1/camera/play")
    public Wrapper<PlayRespModel> play(@RequestBody PlayReqModel reqModel) {
		log.info("收到请求开始：[显示点播日志][/manager/resourcecenter/v1/camera/play]reqModel:"+reqModel.toString());
		PlayDTO dto = new PlayDTO();
        dto.setType(reqModel.getType());
        dto.setAddress(reqModel.getAddress());
        dto.setUrl(reqModel.getUrl());
        PlayRespModel respModel = new PlayRespModel();
		log.info("处理请求结束：[显示点播日志][/manager/resourcecenter/v1/camera/play]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 查询设置的默认播放列表
     */
    @PostMapping(value = "/manager/resourcecenter/v1/camera/getDefaultCameraSipList")
    public Wrapper<DefaultPlayListVO> getDefaultPlayList() {
        log.info("收到请求开始：[查询设置的默认播放列表][/manager/resourcecenter/v1/camera/getDefaultCameraSipList]");
        DefaultPlayListVO respModel = new DefaultPlayListVO();
        List<String> defaultPlatList = jcxxSbxxService.getDefaultPlatList();
        respModel.setSipList(defaultPlatList);

        log.info("处理请求结束：[查询设置的默认播放列表][/manager/resourcecenter/v1/camera/getDefaultCameraSipList]"
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(respModel);
    }
}