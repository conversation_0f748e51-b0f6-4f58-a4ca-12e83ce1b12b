/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.resourcecenter.model.domain.JcxxDictType;


/**
 * jcxx_dict_type数据库操作
 *
 * <AUTHOR>
 * @version $Id: InitJcxxDictTypeMapper.java, v 0.1 2022年09月27日 14时45分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface JcxxDictTypeMapper extends MyMapper<JcxxDictType> {

    /**
     * 分页查询jcxx_dict_type
     *
     * @param example
     * @return
     */
    List<JcxxDictType> pageList(JcxxDictType example);
}
