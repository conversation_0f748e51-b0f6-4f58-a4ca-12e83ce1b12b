/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.biz.unifyaccess.http.model.resp.CommandResultRespModel;
import com.xcwlkj.biz.unifyaccess.http.service.UnifyAccessService;
import com.xcwlkj.model.enums.CommandStatusEnum;
import com.xcwlkj.resourcecenter.mapper.SbCommandLogMapper;
import com.xcwlkj.resourcecenter.model.domain.SbCommandLog;
import com.xcwlkj.resourcecenter.model.dto.bpgl.CommandLogListDTO;
import com.xcwlkj.resourcecenter.model.enums.DevicePropertyEnum;
import com.xcwlkj.resourcecenter.model.vo.bpgl.CommandLogItemVO;
import com.xcwlkj.resourcecenter.model.vo.bpgl.CommandLogListVO;
import com.xcwlkj.resourcecenter.service.SbCommandLogService;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;


/**
 * 设备命令日志表服务
 * <AUTHOR>
 * @version $Id: SbCommandLogServiceImpl.java, v 0.1 2024年12月03日 15时13分 xcwlkj.com Exp $
 */
@Service("sbCommandLogService")
@Slf4j
public class SbCommandLogServiceImpl  implements SbCommandLogService  {

    @Resource
    private SbCommandLogMapper modelMapper;
    @Resource
    private UnifyAccessService unifyAccessService;

    @Override
    public void batchInit(List<String> deviceList, String msgId, String type, String ywlx, String message) {
        List<SbCommandLog> sbCommandLogs = new ArrayList<>();
        for (String xlh : deviceList) {
            SbCommandLog sbCommandLog = new SbCommandLog();
            sbCommandLog.setId(IdGenerateUtil.generateId());
            sbCommandLog.setSbxlh(xlh);
            sbCommandLog.setMsgId(msgId);
            sbCommandLog.setMessage(message);
            sbCommandLog.setType(type);
            sbCommandLog.setYwlx(ywlx);
            sbCommandLog.setStatus(CommandStatusEnum.SUCCESSFUL_DISTRIBUTION.getCode());
            sbCommandLog.setCreateTime(new Date());
            sbCommandLog.setSendTime(new Date());
            sbCommandLogs.add(sbCommandLog);
        }

        modelMapper.batchInsertOrUpdate(sbCommandLogs);
    }

    @Override
    public void batchUpdate(List<String> deviceList, String msgId, String type, Integer tryCount) {
        Map<String, CommandResultRespModel> resultMap = new HashMap<>();
        while (tryCount-- > 0){
            try {
                // 休眠500ms后查询
                Thread.sleep(500);
            } catch (InterruptedException interruptedException) {
                log.error("线程异常", interruptedException);
            }
            try {
                resultMap = unifyAccessService.batchSendCommandResult(msgId);
                if (resultMap.size() > 0 && checkStatus(resultMap)){
                    break;
                }
            }catch (Exception e){
                log.error("查询结果失败", e);
            }
        }

        List<SbCommandLog> sbCommandLogs = new ArrayList<>();
        for (String xlh : deviceList) {
            CommandResultRespModel resp = resultMap.get(xlh);
            SbCommandLog sbCommandLog = new SbCommandLog();
            sbCommandLog.setId(IdGenerateUtil.generateId());
            sbCommandLog.setSbxlh(xlh);
            sbCommandLog.setType(type);
            sbCommandLog.setMsgId(msgId);
            if (resp != null && resp.getStatus().equals(CommandStatusEnum.COMMAND_EXECUTED_SUCCESSFULLY.getCode())) {
                sbCommandLog.setStatus(resp.getStatus());
                sbCommandLog.setReply(resp.getReply());
                sbCommandLog.setNote(resp.getNote());
                sbCommandLog.setFinishTime(new Date());
            }else {
                sbCommandLog.setStatus(CommandStatusEnum.FAILED.getCode());
                sbCommandLog.setNote("超时");
                sbCommandLog.setFinishTime(new Date());
            }
            sbCommandLog.setUpdateTime(new Date());
            sbCommandLogs.add(sbCommandLog);
        }

        modelMapper.batchInsertOrUpdate(sbCommandLogs);

        if (resultMap.isEmpty()){
            throw new BusinessException("命令下发失败");
        }
    }

    /**
     * 检查命令下发的结果, 如果有尚未完成的返回false, 否则返回true
     * @param resultMap
     * @return
     */
    private boolean checkStatus(Map<String, CommandResultRespModel> resultMap){
        for (CommandResultRespModel result : resultMap.values()) {
            if (CommandStatusEnum.TO_BE_ISSUED.getCode().equals(result.getStatus()) || CommandStatusEnum.SUCCESSFUL_DISTRIBUTION.getCode().equals(result.getStatus()) ){
                return false;
            }
        }
        return true;
    }

    /** 
     * @see com.xcwlkj.resourcecenter.service.SbCommandLogService#commandLogList(com.xcwlkj.resourcecenter.model.dto.bpgl.CommandLogListDTO)
     */
	@Override
	public CommandLogListVO commandLogList(CommandLogListDTO dto) {
        CommandLogListVO result = new CommandLogListVO();
        PageHelper.startPage( dto.getPageNum(), dto.getPageSize());
        Example example = new Example(SbCommandLog.class);
        Example.Criteria cr = example.createCriteria();
        if(StringUtil.isNotBlank(dto.getMsgId())) {
            cr.andEqualTo("msgId",dto.getMsgId());
        }
        if(StringUtil.isNotBlank(dto.getType())) {
            cr.andEqualTo("type",dto.getType());
        }
        if(dto.getStatus() != null) {
            cr.andEqualTo("status",dto.getStatus());
        }
        if(StringUtil.isNotBlank(dto.getSbxlh())) {
            cr.andLike("sbxlh","%" + dto.getSbxlh() + "%");
        }
        example.orderBy("sendTime").desc();
        List<SbCommandLog> list = modelMapper.selectByExample(example);
        PageInfo<SbCommandLog> pageInfo = new PageInfo<SbCommandLog>(list);
        result.setTotalRows((int)pageInfo.getTotal());
        result.setCommandLogList(new ArrayList<>());
        for(SbCommandLog model : list) {
            CommandLogItemVO itemVO = new CommandLogItemVO();
            itemVO.setMsgId(model.getMsgId());
            itemVO.setSbxlh(model.getSbxlh());
            itemVO.setType(model.getType());
            itemVO.setStatus(model.getStatus());
            itemVO.setMessage(model.getMessage());
            itemVO.setReply(model.getReply());

            // 属性相关命令增加信息
            StringBuilder note = new StringBuilder(model.getNote());
            if (StringUtil.isNotBlank(model.getMessage())) {
                if (StringUtil.equals(model.getType(), "write_properties")) {
                    JSONObject jsonObject = JSONObject.parseObject(model.getMessage());
                    for (String properties : jsonObject.getJSONObject("properties").keySet()) {
                        DevicePropertyEnum devicePropertyEnum = DevicePropertyEnum.get(properties);
                        if (devicePropertyEnum != null) {
                            note.insert(0, "设置" + devicePropertyEnum.getDesc() + ",");
                        }
                    }
                } else if (StringUtil.equals(model.getType(), "read_properties")) {
                    JSONObject jsonObject = JSONObject.parseObject(model.getMessage());
                    for (String properties : jsonObject.getJSONArray("properties").toJavaList(String.class)) {
                        DevicePropertyEnum devicePropertyEnum = DevicePropertyEnum.get(properties);
                        if (devicePropertyEnum != null) {
                            note.insert(0, "读取" + devicePropertyEnum.getDesc() + ",");
                        }
                    }
                }
            }
            itemVO.setNote(note.toString());
            itemVO.setSendTime(DateUtil.formatDateTime(model.getSendTime()));
            itemVO.setFinishTime(DateUtil.formatDateTime(model.getFinishTime()));
            result.getCommandLogList().add(itemVO);
        }
        return result;
    }
}