<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>eeip-basicinfo-resource-center</artifactId>
	<packaging>jar</packaging>
	<name>eeip-basicinfo-resource-center</name>
	<url>http://www.xcwlkj.com</url>
	<description>hisome-资源中心</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<docker.host>http://docker.xccloud.com:12375</docker.host>
		<java.version>1.8</java.version>
	</properties>

	<!-- 父类信息 -->
	<parent>
		<groupId>com.hisome</groupId>
		<artifactId>eeip-basicinfo</artifactId>
		<version>0.0.5</version>
	</parent>

	<!-- 依赖明细 -->
	<dependencies>
		<!-- 对象转换工具 -->
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-jdk8</artifactId>
			<version>1.3.0.Final</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>1.3.0.Final</version>
		</dependency>
		<!-- 公共JAR -->
		<dependency>
			<groupId>com.xcwlkj</groupId>
			<artifactId>xc-common</artifactId>
			<version>0.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.xcwlkj</groupId>
			<artifactId>xc-remote-api</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.xcwlkj</groupId>
			<artifactId>xc-manager-api</artifactId>
			<version>1.0.0</version>
		</dependency>
		<!-- 公用服务API -->
		<dependency>
			<groupId>com.xcwlkj</groupId>
			<artifactId>xc-provider-pubc-api</artifactId>
			<version>0.0.5</version>
		</dependency>
		<!-- 数据服务 API -->
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>eeip-provider-basicinfo-api</artifactId>
			<version>0.0.5</version>
		</dependency>
		<!--注册中心客户端 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
		</dependency>
		<!-- 配置中心客户端 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
		</dependency>
		<!--自省和监控的集成功能 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<!-- 重试 -->
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
		</dependency>
		<!--数据库连接 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<!-- 阿里巴巴数据库连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<!--测试框架 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- 模版引擎 -->
		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>eeip-provider-identity-verify-api</artifactId>
			<version>0.0.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-mqtt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.paho</groupId>
			<artifactId>org.eclipse.paho.client.mqttv3</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<version>5.3.6</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.0.3</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<!--   
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.xcwlkj.basicinfo.sturegister.StuRegisterApplication</mainClass>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin> 
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>${docker-maven-plugin.version}</version>
				<configuration>
					<buildArgs>
						<JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
					</buildArgs>
					<imageName>${docker.image.prefix}/${project.artifactId}</imageName>
					<dockerDirectory>${project.basedir}/src/main/resources</dockerDirectory>
					<dockerHost>${docker.host}</dockerHost>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
			</plugin>
		</plugins>
		-->
		<plugins>
			<!-- 编译插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
			<!-- 资源配置插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
			</plugin>
			<!--配置生成源码包 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>${maven-source-plugin.version}</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
