# 空考位统计查询接口涉及的数据库表分析

## 接口概述

本文档分析了两个空考位统计查询接口涉及的数据库表：
1. **空考位统计查询** (`/manager/identity/cxtj/kkwtj`)
2. **空考位详情查询** (`/manager/identity/cxtj/kkwxq`)

## 核心数据库表

### 1. ks_kkw_msg (空考位消息表)
**表名**: `ks_kkw_msg`  
**实体类**: `KsKkwMsg`  
**作用**: 存储空考位消息的核心数据表

#### 主要字段：
- `ID` - 主键，消息唯一标识
- `KSJHBH` - 考试计划编号
- `CCM` - 场次码
- `BZHKDID` - 标准化考点ID
- `BZHKCID` - 标准化考场ID
- `KS_ZKZH` - 考生准考证号
- `KS_BPZWH` - 考生编排座位号
- `KS_SJZWH` - 考生实际座位号
- `LJKCH` - 逻辑考场号
- `KCH` - 考场号
- `RCBZ` - 入场备注（异常类型）
  - 1-误识别
  - 2-坐错他人位置
  - 3-实际未参加考试
  - 4-他人坐错位置
  - 5-人工核验
  - 6-缺考（空位）
  - 7-无编排
- `TIMESTAMP` - 时间戳
- `CZSJ` - 操作时间
- `SCZT` - 删除状态（0-正常，1-删除）

### 2. ks_kcxx (考试考场信息表)
**表名**: `ks_kcxx`  
**实体类**: `KsKcxx`  
**作用**: 存储考试考场的基本信息

#### 主要字段：
- `ID` - 主键
- `KCBH` - 考场编号
- `KCMC` - 考场名称
- `KSJHBH` - 考试计划编号
- `CCM` - 场次码
- `KMMC` - 科目名称
- `BZHKCMC` - 标准化考场名称
- `BZHKCID` - 标准化考场ID
- `LJKCBH` - 逻辑考场编号
- `KDBH` - 考点编号
- `BZHKDID` - 标准化考点ID
- `SCZT` - 删除状态

### 3. cs_jsjbxx (教室基本信息表)
**表名**: `cs_jsjbxx`  
**实体类**: `CsJsjbxx`  
**作用**: 存储教室（考场）的物理信息

#### 主要字段：
- `JSH` - 教室号（主键）
- `JSMC` - 教室名称
- `BZHKCID` - 标准化考场ID
- `XQH` - 校区号
- `JXLH` - 教学楼号
- `SZLC` - 所在楼层
- `ZWS` - 座位数
- `SCZT` - 删除状态

### 4. ks_ksrcxx (考生入场信息表)
**表名**: `ks_ksrcxx`  
**实体类**: `KsKsrcxx`  
**作用**: 存储考生入场相关信息

#### 主要字段：
- `KSRCBH` - 入场信息唯一编号（主键）
- `KSJHBH` - 考试计划编号
- `CCM` - 场次码
- `ZKZH` - 准考证号
- `KSXM` - 考生姓名
- `SFZH` - 身份证号
- `KSH` - 考生号
- `KCBH` - 考场编号
- `LJKCBH` - 逻辑考场编号
- `ZWH` - 座位号
- `SFRC` - 是否入场
- `RCSJ` - 入场时间
- `SCZT` - 删除状态

## SQL查询分析

### 空考位统计查询 (selectKkwTjLb)

```sql
SELECT DISTINCT
    kc.kcbh as kch,                    -- 考场号
    COALESCE(kc.bzhkcmc, js.jsmc) as csmc,  -- 场所名称
    CASE
        WHEN COUNT(msg.id) > 0 THEN '1'
        ELSE '0'
    END as sfsb,                       -- 是否上报
    DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj,  -- 上报时间
    kc.bzhkcid as bzhkcid,            -- 考场ID
    kc.ljkcbh as ljkcbh               -- 逻辑考场编号
FROM ks_kcxx kc
LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH
    AND msg.SCZT = '0'
    AND kc.ksjhbh = msg.KSJHBH
LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0'
WHERE kc.sczt = '0'
GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh
ORDER BY kc.kcbh
```

### 空考位统计摘要 (selectKkwTjInfo)

```sql
SELECT
    COUNT(DISTINCT kc.ljkcbh) as total,     -- 总考场数
    COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb,  -- 已上报
    COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb,      -- 未上报
    CONCAT(ROUND(COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / 
           NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2), '%') as reportRate   -- 上报比例
FROM ks_kcxx kc
LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH
    AND msg.SCZT = '0'
    AND kc.ksjhbh = msg.KSJHBH
WHERE kc.sczt = '0'
```

### 空考位详情查询 (selectKkwTjXq)

```sql
SELECT
    kc.ljkcbh as kch,                 -- 考场号
    COALESCE(kc.bzhkcmc, js.jsmc) as csmc,  -- 场所名称
    msg.KS_ZKZH as zkzh,             -- 准考证号
    ksxx.ksxm as xm,                 -- 姓名
    msg.KS_BPZWH as zwh,             -- 座位号
    CASE msg.RCBZ                    -- 异常类型转换
        WHEN '1' THEN '误识别'
        WHEN '2' THEN '坐错他人位置'
        WHEN '3' THEN '实际未参加考试'
        WHEN '4' THEN '他人坐错位置'
        WHEN '5' THEN '人工核验'
        WHEN '6' THEN '缺考（空位）'
        WHEN '7' THEN '无编排'
        ELSE '未知'
    END as yclx,
    DATE_FORMAT(msg.timestamp, '%Y-%m-%d %H:%i:%s') as zdsbsj,  -- 终端上报时间
    '0' as sfsb,                     -- 上报上级平台情况（暂时固定）
    NULL as sbsjsj                   -- 上报上级平台时间（暂时为空）
FROM ks_kcxx kc
INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH
    AND msg.SCZT = '0'
    AND kc.ksjhbh = msg.KSJHBH
    AND kc.ccm = msg.ccm
LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0'
LEFT JOIN ks_ksrcxx ksxx ON msg.KS_ZKZH = ksxx.zkzh
    AND ksxx.ksjhbh = msg.KSJHBH
    AND ksxx.sczt = '0'
WHERE kc.sczt = '0'
ORDER BY kc.ljkcbh, msg.KS_BPZWH
```

## 表关联关系

### 主要关联关系：
1. **ks_kcxx ↔ ks_kkw_msg**: 通过 `ljkcbh = LJKCH` 关联
2. **ks_kcxx ↔ cs_jsjbxx**: 通过 `bzhkcid = bzhkcid` 关联
3. **ks_kkw_msg ↔ ks_ksrcxx**: 通过 `KS_ZKZH = zkzh` 关联

### 关联逻辑：
- 考场信息表作为主表，关联空考位消息获取上报情况
- 通过教室信息表获取场所名称等物理信息
- 通过考生入场信息表获取考生基本信息

## 数据流向

1. **空考位消息产生**: 终端设备检测到空考位异常时，向 `ks_kkw_msg` 表插入记录
2. **统计查询**: 基于考场信息表，左关联空考位消息表统计上报情况
3. **详情查询**: 内关联空考位消息表，获取具体的异常详情信息
4. **场所信息补充**: 通过教室信息表补充场所名称等展示信息
5. **考生信息补充**: 通过考生入场信息表补充考生姓名等信息

## 索引建议

为了提高查询性能，建议在以下字段上建立索引：

### ks_kkw_msg 表：
- `(KSJHBH, CCM, SCZT)` - 复合索引
- `(LJKCH, SCZT)` - 复合索引
- `KS_ZKZH` - 单列索引

### ks_kcxx 表：
- `(KSJHBH, CCM, SCZT)` - 复合索引
- `LJKCBH` - 单列索引
- `BZHKCID` - 单列索引

### cs_jsjbxx 表：
- `(BZHKCID, SCZT)` - 复合索引

### ks_ksrcxx 表：
- `(ZKZH, KSJHBH, SCZT)` - 复合索引
