/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.dto.iacallback;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 分析报告回调dto
 * <AUTHOR>
 * @version $Id: TaskReportCallBackDTO.java, v 0.1 2021年05月27日 13时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TaskReportCallBackDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	private String ID;
	private String Type;
	private String TaskID;
	private ZnfxProgressStatus ProgressStatus;
	private String ReportTime;
	
}
