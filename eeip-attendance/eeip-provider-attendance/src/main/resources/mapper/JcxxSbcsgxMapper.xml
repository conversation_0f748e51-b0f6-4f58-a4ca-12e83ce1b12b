<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.attendance.mapper.JcxxSbcsgxMapper">
    <resultMap id="BaseResultMap" type="com.xcwlkj.attendance.model.domain.JcxxSbcsgx">
        <id column="fjh" jdbcType="VARCHAR" property="fjh"/>
        <id column="csbh" jdbcType="VARCHAR" property="csbh"/>
        <id column="sbbh" jdbcType="VARCHAR" property="sbbh"/>
        <result column="csmc" jdbcType="VARCHAR" property="csmc"/>
        <result column="cslx" jdbcType="VARCHAR" property="cslx"/>
        <result column="sbmc" jdbcType="VARCHAR" property="sbmc"/>
        <result column="sblx" jdbcType="VARCHAR" property="sblx"/>
        <result column="jdzx" jdbcType="VARCHAR" property="jdzx"/>
        <result column="ksgljgid" jdbcType="VARCHAR" property="ksgljgid"/>
        <result column="ssjgbh" jdbcType="VARCHAR" property="ssjgbh"/>

    </resultMap>
    <!-- 列信息 -->
    <sql id="Base_Column_List">
        fjh,
        csbh,
        sbbh,
        csmc,
        cslx,
        sbmc,
        sblx,
        jdzx,
        ksgljgid,
        ssjgbh

	</sql>

    <!-- where条件 -->
    <sql id="Base_Where_Condition">
        <if test="fjh != null and fjh != ''">
            AND fjh = #{fjh,jdbcType=VARCHAR}
        </if>
        <if test="csbh != null and csbh != ''">
            AND csbh = #{csbh,jdbcType=VARCHAR}
        </if>
        <if test="sbbh != null and sbbh != ''">
            AND sbbh = #{sbbh,jdbcType=VARCHAR}
        </if>
        <if test="csmc != null and csmc != ''">
            AND csmc = #{csmc,jdbcType=VARCHAR}
        </if>
        <if test="cslx != null and cslx != ''">
            AND cslx = #{cslx,jdbcType=VARCHAR}
        </if>
        <if test="sbmc != null and sbmc != ''">
            AND sbmc = #{sbmc,jdbcType=VARCHAR}
        </if>
        <if test="sblx != null and sblx != ''">
            AND sblx = #{sblx,jdbcType=VARCHAR}
        </if>
        <if test="jdzx != null and jdzx != ''">
            AND jdzx = #{jdzx,jdbcType=VARCHAR}
        </if>
        <if test="ksgljgid != null and ksgljgid != ''">
            AND ksgljgid = #{ksgljgid,jdbcType=VARCHAR}
        </if>
        <if test="ssjgbh != null and ssjgbh != ''">
            AND ssjgbh = #{ssjgbh,jdbcType=VARCHAR}
        </if>

    </sql>

    <!-- order by条件 -->
    <sql id="Base_OrderBy_Condition">
        <if test="orderBy != null and orderBy !=''">
            ORDER BY ${orderBy}
        </if>
    </sql>

    <!-- update条件 -->
    <sql id="Base_Set_Condition">
        <set>
            <if test="fjh != null ">
                fjh = #{fjh,jdbcType=VARCHAR},
            </if>
            <if test="csbh != null ">
                csbh = #{csbh,jdbcType=VARCHAR},
            </if>
            <if test="sbbh != null ">
                sbbh = #{sbbh,jdbcType=VARCHAR},
            </if>
            <if test="csmc != null ">
                csmc = #{csmc,jdbcType=VARCHAR},
            </if>
            <if test="cslx != null ">
                cslx = #{cslx,jdbcType=VARCHAR},
            </if>
            <if test="sbmc != null ">
                sbmc = #{sbmc,jdbcType=VARCHAR},
            </if>
            <if test="sblx != null ">
                sblx = #{sblx,jdbcType=VARCHAR},
            </if>
            <if test="jdzx != null ">
                jdzx = #{jdzx,jdbcType=VARCHAR},
            </if>
            <if test="ksgljgid != null ">
                ksgljgid = #{ksgljgid,jdbcType=VARCHAR},
            </if>
            <if test="ssjgbh != null ">
                ssjgbh = #{ssjgbh,jdbcType=VARCHAR}
            </if>

        </set>
    </sql>

    <select id="selectFjhBySipUri" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT DISTINCT
            b.csbh
        FROM
            jcxx_sxjkz a
        INNER JOIN
            jcxx_sbcsgx b
        ON
            a.sbxxbh = b.sbbh
        WHERE
            a.sipdz = #{sipUri}
    </select>
    <select id="jsglsbxxcx" resultType="com.xcwlkj.attendance.model.view.JsGlsbxxView"
            parameterType="map">
        SELECT
        a.csbh csbh,
        a.sbbh sbbh,
        b.sipdz sipuri
        FROM
        jcxx_sbcsgx a
        INNER JOIN
        jcxx_sxjkz b
        ON
        a.sbbh=b.sbxxbh
        WHERE
        a.csbh IN
        <foreach collection="jshlist" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="yt != null and yt != ''">
        	and yt=#{yt}
        </if>
        <!--        <if test="jsh != null and jsh != ''">
                    AND a.csbh = #{jsh}
                </if>-->
    </select>
	
	 <select id="jsglsbxxcxNotIn" resultType="com.xcwlkj.attendance.model.view.JsGlsbxxView"
            parameterType="map">
        SELECT
        a.csbh csbh,
        a.sbbh sbbh,
        b.sipdz sipuri
        FROM
        jcxx_sbcsgx a
        INNER JOIN
        jcxx_sxjkz b
        ON
        a.sbbh=b.sbxxbh
        WHERE
        1 = 1
        <if test="jshList != null and jshList.size() >0 ">
            AND a.csbh not in
            <foreach collection="jshList" open="(" close=")" separator="," item="jsh"> 
            	#{jsh}
            </foreach>
        </if>
        <if test="yt != null and yt != ''">
        	and yt=#{yt}
        </if>
    </select>
	
</mapper>
