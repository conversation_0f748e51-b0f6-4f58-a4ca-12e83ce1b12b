<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.attendance.mapper.ZnfxJsRstjMapper">
    <resultMap id="BaseResultMap" type="com.xcwlkj.attendance.model.domain.ZnfxJsRstj">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="jsh" jdbcType="VARCHAR" property="jsh"/>
        <result column="jsmc" jdbcType="VARCHAR" property="jsmc"/>
        <result column="tjrs" jdbcType="BIGINT" property="tjrs"/>
        <result column="xq" jdbcType="VARCHAR" property="xq"/>
        <result column="glbh" jdbcType="VARCHAR" property="glbh"/>
        <result column="kssj" jdbcType="TIMESTAMP" property="kssj"/>
        <result column="wcsj" jdbcType="TIMESTAMP" property="wcsj"/>
        <result column="jcjg" jdbcType="VARCHAR" property="jcjg"/>
        <result column="jtxx" jdbcType="VARCHAR" property="jtxx"/>

    </resultMap>
    <!-- 列信息 -->
    <sql id="Base_Column_List">
        id,
        jsh,
        jsmc,
        tjrs,
        xq,
        glbh,
        kssj,
        wcsj,
        jcjg,
        jtxx

	</sql>

    <!-- where条件 -->
    <sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=BIGINT}
        </if>
        <if test="jsh != null and jsh != ''">
            AND jsh = #{jsh,jdbcType=VARCHAR}
        </if>
        <if test="jsmc != null and jsmc != ''">
            AND jsmc = #{jsmc,jdbcType=VARCHAR}
        </if>
        <if test="tjrs != null and tjrs != ''">
            AND tjrs = #{tjrs,jdbcType=BIGINT}
        </if>
        <if test="xq != null and xq != ''">
            AND xq = #{xq,jdbcType=VARCHAR}
        </if>
        <if test="glbh != null and glbh != ''">
            AND glbh = #{glbh,jdbcType=VARCHAR}
        </if>
        <if test="kssj != null and kssj != ''">
            AND kssj = #{kssj,jdbcType=TIMESTAMP}
        </if>
        <if test="wcsj != null and wcsj != ''">
            AND wcsj = #{wcsj,jdbcType=TIMESTAMP}
        </if>
        <if test="jcjg != null and jcjg != ''">
            AND jcjg = #{jcjg,jdbcType=VARCHAR}
        </if>
        <if test="jtxx != null and jtxx != ''">
            AND jtxx = #{jtxx,jdbcType=VARCHAR}
        </if>

    </sql>

    <!-- order by条件 -->
    <sql id="Base_OrderBy_Condition">
        <if test="orderBy != null and orderBy !=''">
            ORDER BY ${orderBy}
        </if>
    </sql>

    <!-- update条件 -->
    <sql id="Base_Set_Condition">
        <set>
            <if test="id != null ">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="jsh != null ">
                jsh = #{jsh,jdbcType=VARCHAR},
            </if>
            <if test="jsmc != null ">
                jsmc = #{jsmc,jdbcType=VARCHAR},
            </if>
            <if test="tjrs != null ">
                tjrs = #{tjrs,jdbcType=BIGINT},
            </if>
            <if test="xq != null ">
                xq = #{xq,jdbcType=VARCHAR},
            </if>
            <if test="glbh != null ">
                glbh = #{glbh,jdbcType=VARCHAR},
            </if>
            <if test="kssj != null ">
                kssj = #{kssj,jdbcType=TIMESTAMP},
            </if>
            <if test="wcsj != null ">
                wcsj = #{wcsj,jdbcType=TIMESTAMP},
            </if>
            <if test="jcjg != null ">
                jcjg = #{jcjg,jdbcType=VARCHAR},
            </if>
            <if test="jtxx != null ">
                jtxx = #{jtxx,jdbcType=VARCHAR}
            </if>

        </set>
    </sql>
    <select id="selectRstjByJsh" resultType="com.xcwlkj.attendance.model.domain.ZnfxJsRstj">
        SELECT
        t1.*
        FROM
        znfx_js_rstj t1
        LEFT JOIN
        znfx_js_rstj t2
        ON
        t1.jsh = t2.jsh
        AND
        t1.wcsj <![CDATA[<]]> t2.wcsj
        WHERE
        t2.id IS NULL
        AND
        t1.jsh IN
        <foreach collection="jshList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
	
	<select id="findZxJsRstjView" resultType="com.xcwlkj.attendance.model.domain.ZnfxJsRstj">
		SELECT
			tmp.* 
		FROM
			( SELECT id, jsh, kssj, wcsj,jsmc,jcjg, jtxx, tjrs FROM znfx_js_rstj ORDER BY kssj DESC LIMIT 999 ) tmp 
		GROUP BY
			tmp.jsh
	</select>

</mapper>
