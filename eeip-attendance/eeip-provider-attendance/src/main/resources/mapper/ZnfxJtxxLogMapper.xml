<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.attendance.mapper.ZnfxJtxxLogMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.attendance.model.domain.ZnfxJtxxLog">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="sipdz" jdbcType="VARCHAR" property="sipdz" />
        <result column="sbmc" jdbcType="VARCHAR" property="sbmc" />
        <result column="jtxx" jdbcType="VARCHAR" property="jtxx" />
        <result column="llxy" jdbcType="VARCHAR" property="llxy" />
        <result column="cjsj" jdbcType="TIMESTAMP" property="cjsj" />
        <result column="wcsj" jdbcType="TIMESTAMP" property="wcsj" />
        <result column="rwid" jdbcType="VARCHAR" property="rwid" />
        <result column="rwzxjd" jdbcType="DECIMAL" property="rwzxjd" />
        <result column="yczzyy" jdbcType="VARCHAR" property="yczzyy" />
        <result column="ssqyid" jdbcType="VARCHAR" property="ssqyid" />
        <result column="ssqymc" jdbcType="VARCHAR" property="ssqymc" />
        <result column="qqcw" jdbcType="VARCHAR" property="qqcw" />
        <result column="qqzt" jdbcType="VARCHAR" property="qqzt" />
        <result column="jtgs" jdbcType="VARCHAR" property="jtgs" />
        <result column="jtkd" jdbcType="BIGINT" property="jtkd" />
        <result column="jtgd" jdbcType="BIGINT" property="jtgd" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        sipdz,
        sbmc,
        jtxx,
        llxy,
        cjsj,
        wcsj,
        rwid,
        rwzxjd,
        yczzyy,
        ssqyid,
        ssqymc,
        qqcw,
        qqzt,
        jtgs,
        jtkd,
        jtgd

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="sipdz != null and sipdz != ''">
            AND sipdz = #{sipdz,jdbcType=VARCHAR}
        </if>
        <if test="sbmc != null and sbmc != ''">
            AND sbmc = #{sbmc,jdbcType=VARCHAR}
        </if>
        <if test="jtxx != null and jtxx != ''">
            AND jtxx = #{jtxx,jdbcType=VARCHAR}
        </if>
        <if test="llxy != null and llxy != ''">
            AND llxy = #{llxy,jdbcType=VARCHAR}
        </if>
        <if test="cjsj != null and cjsj != ''">
            AND cjsj = #{cjsj,jdbcType=TIMESTAMP}
        </if>
        <if test="wcsj != null and wcsj != ''">
            AND wcsj = #{wcsj,jdbcType=TIMESTAMP}
        </if>
        <if test="rwid != null and rwid != ''">
            AND rwid = #{rwid,jdbcType=VARCHAR}
        </if>
        <if test="rwzxjd != null and rwzxjd != ''">
            AND rwzxjd = #{rwzxjd,jdbcType=DECIMAL}
        </if>
        <if test="yczzyy != null and yczzyy != ''">
            AND yczzyy = #{yczzyy,jdbcType=VARCHAR}
        </if>
        <if test="ssqyid != null and ssqyid != ''">
            AND ssqyid = #{ssqyid,jdbcType=VARCHAR}
        </if>
        <if test="ssqymc != null and ssqymc != ''">
            AND ssqymc = #{ssqymc,jdbcType=VARCHAR}
        </if>
        <if test="qqcw != null and qqcw != ''">
            AND qqcw = #{qqcw,jdbcType=VARCHAR}
        </if>
        <if test="qqzt != null and qqzt != ''">
            AND qqzt = #{qqzt,jdbcType=VARCHAR}
        </if>
        <if test="jtgs != null and jtgs != ''">
            AND jtgs = #{jtgs,jdbcType=VARCHAR}
        </if>
        <if test="jtkd != null and jtkd != ''">
            AND jtkd = #{jtkd,jdbcType=BIGINT}
        </if>
        <if test="jtgd != null and jtgd != ''">
            AND jtgd = #{jtgd,jdbcType=BIGINT}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="sipdz != null ">
            sipdz = #{sipdz,jdbcType=VARCHAR},
        </if>
        <if test="sbmc != null ">
            sbmc = #{sbmc,jdbcType=VARCHAR},
        </if>
        <if test="jtxx != null ">
            jtxx = #{jtxx,jdbcType=VARCHAR},
        </if>
        <if test="llxy != null ">
            llxy = #{llxy,jdbcType=VARCHAR},
        </if>
        <if test="cjsj != null ">
            cjsj = #{cjsj,jdbcType=TIMESTAMP},
        </if>
        <if test="wcsj != null ">
            wcsj = #{wcsj,jdbcType=TIMESTAMP},
        </if>
        <if test="rwid != null ">
            rwid = #{rwid,jdbcType=VARCHAR},
        </if>
        <if test="rwzxjd != null ">
            rwzxjd = #{rwzxjd,jdbcType=DECIMAL},
        </if>
        <if test="yczzyy != null ">
            yczzyy = #{yczzyy,jdbcType=VARCHAR},
        </if>
        <if test="ssqyid != null ">
            ssqyid = #{ssqyid,jdbcType=VARCHAR},
        </if>
        <if test="ssqymc != null ">
            ssqymc = #{ssqymc,jdbcType=VARCHAR},
        </if>
        <if test="qqcw != null ">
            qqcw = #{qqcw,jdbcType=VARCHAR},
        </if>
        <if test="qqzt != null ">
            qqzt = #{qqzt,jdbcType=VARCHAR},
        </if>
        <if test="jtgs != null ">
            jtgs = #{jtgs,jdbcType=VARCHAR},
        </if>
        <if test="jtkd != null ">
            jtkd = #{jtkd,jdbcType=BIGINT},
        </if>
        <if test="jtgd != null ">
            jtgd = #{jtgd,jdbcType=BIGINT}
        </if>

	</set>
	</sql>
	<insert id="insertList" parameterType="java.util.List">
		INSERT INTO `znfx_jtxx_log` 
			(`id`, `sipdz`, `sbmc`, `jtxx`, `llxy`, `cjsj`, `wcsj`, `rwid`, `rwzxjd`, `yczzyy`, `ssqyid`, `ssqymc`, `qqcw`, `qqzt`,jtgs,jtkd,jtgd)  
		VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
           #{item.id},
           #{item.sipdz},
           #{item.sbmc},
           #{item.jtxx},
           #{item.llxy},
           #{item.cjsj},
           #{item.wcsj},
           #{item.rwid},
           #{item.rwzxjd},
           #{item.yczzyy},
           #{item.ssqyid},
           #{item.ssqymc},
           #{item.qqcw},
           #{item.qqzt} 
           ,#{item.jtgs}
           ,#{item.jtkd}
           ,#{item.jtgd}
            )
        </foreach>
	</insert>
</mapper>
