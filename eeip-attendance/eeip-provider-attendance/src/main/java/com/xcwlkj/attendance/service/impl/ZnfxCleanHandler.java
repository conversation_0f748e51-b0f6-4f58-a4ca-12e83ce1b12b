package com.xcwlkj.attendance.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.attendance.model.domain.ZnfxJsRstj;
import com.xcwlkj.attendance.model.domain.ZnfxJtxx;
import com.xcwlkj.attendance.model.domain.ZnfxJtxxLog;
import com.xcwlkj.attendance.model.domain.ZnfxRstj;
import com.xcwlkj.attendance.model.view.ZnfxSxjView;
import com.xcwlkj.attendance.service.ZnfxJsRstjService;
import com.xcwlkj.attendance.service.ZnfxJtxxLogService;
import com.xcwlkj.attendance.service.ZnfxJtxxService;
import com.xcwlkj.attendance.service.ZnfxRstjService;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.common.DateField;
import com.xcwlkj.util.common.DateTime;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Component
@Slf4j
public class ZnfxCleanHandler {

	@Autowired
	private ZnfxRstjService znfxRstjService;
	@Autowired
	private ZnfxJtxxService znfxJtxxService;
	@Autowired
	private ZnfxJtxxLogService znfxJtxxLogService;
	@Autowired
	private ZnfxJsRstjService znfxJsRstjService;
	
	private int batchPageSize = 2000;
	@Value("${xc.znfx.expireDay:-1}")
	private int expireDay;
	
	public void cleanExpireData() {
		cleanRstjData();
		cleanJtxxAndLogData();
		cleanJsRstjData();
	}
	
	protected void cleanRstjData() {
		Date lastWeek= DateUtil.offset(new Date(), DateField.DAY_OF_WEEK, expireDay).toJdkDate();
		Example example = new Example(ZnfxRstj.class);
		example.createCriteria().andLessThan("createTime", lastWeek );
		example.selectProperties("jcjlbh","jcjtxx");
		Page page = PageHelper.startPage(1,batchPageSize);
		List<ZnfxRstj> subList = znfxRstjService.selectByExample(example);
		for(int i = 0;i<page.getPages();i++){
		   if(!CollectionUtils.isEmpty(subList)) {
			   List<String> jtList = Lists.newArrayList();
			   for(ZnfxRstj rstj:subList) {
				   if(StringUtils.isNotBlank(rstj.getJcjtxx())) {
					   jtList.add(rstj.getJcjtxx());
				   }
			   }
			   if(!CollectionUtils.isEmpty(jtList)) {
				   log.info("将删除的文件key==>"+jtList);
				   System.out.println("将删除的文件key"+jtList);
				   try {
					   XcDfsClient.delete(jtList.toArray((new String[jtList.size()])));
					} catch (Exception e) {
//						log.error
						e.printStackTrace();
					}
				   
			   }
		   }
		   PageHelper.startPage(i+2,batchPageSize,false);
		   subList = znfxRstjService.selectByExample(example);
		}   
		znfxRstjService.deleteByExample(example);
	}
	
	protected void cleanJtxxAndLogData() {
		Date lastWeek= DateUtil.offset(new Date(), DateField.DAY_OF_WEEK, expireDay).toJdkDate();
		
		//znfxjtxxlog 处理截图文件
		Example logExample = new Example(ZnfxJtxxLog.class);
		logExample.createCriteria().andLessThan("cjsj", lastWeek );
		logExample.selectProperties("id","jtxx");
		Page page = PageHelper.startPage(1,batchPageSize);
		List<ZnfxJtxxLog> subList = znfxJtxxLogService.selectByExample(logExample);
		for(int i = 0;i<page.getPages();i++){
		   if(!CollectionUtils.isEmpty(subList)) {
			   List<String> jtList = Lists.newArrayList();
			   for(ZnfxJtxxLog jtxx:subList) {
				   if(StringUtils.isNotBlank(jtxx.getJtxx())) {
					   jtList.add(jtxx.getJtxx());
				   }
			   }
			   if(!CollectionUtils.isEmpty(jtList)) {
				   log.info("ZnfxJtxxLog 将删除的文件key==>"+jtList);
				   System.out.println("ZnfxJtxxLog 将删除的文件key"+jtList);
				   try {
					   XcDfsClient.delete(jtList.toArray((new String[jtList.size()])));
					} catch (Exception e) {
						e.printStackTrace();
						log.error("ZnfxJtxxLog 删除文件异常["+e.getMessage()+"]", e);
					}
				   
			   }
		   }
		   PageHelper.startPage(i+2,batchPageSize,false);
		   subList = znfxJtxxLogService.selectByExample(logExample);
		} 
		znfxJtxxLogService.deleteByExample(logExample);
		
		//znfxjtxx不处理截图文件
		Example example = new Example(ZnfxJtxx.class);
		example.createCriteria().andLessThan("zhwcsj", lastWeek );
		znfxJtxxService.deleteByExample(example);
	}
	
	protected void cleanJsRstjData() {
		Date lastWeek= DateUtil.offset(new Date(), DateField.DAY_OF_WEEK, expireDay).toJdkDate();
		Example example = new Example(ZnfxJsRstj.class);
		example.createCriteria().andLessThan("kssj", lastWeek );
		znfxJsRstjService.deleteByExample(example);
	}
	
}
