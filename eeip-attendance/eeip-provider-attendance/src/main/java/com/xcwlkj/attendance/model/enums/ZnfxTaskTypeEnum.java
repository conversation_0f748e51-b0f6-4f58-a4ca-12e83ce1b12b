package com.xcwlkj.attendance.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/**
 * 智能分析任务类型枚举类
 */
public enum ZnfxTaskTypeEnum {


    PD("PD","有人无人检测", "ZnfxPDJob",1,1),
    HC("HC","人数统计", "ZnfxHCJob",1,1),
    VQD("VQD","视频质量检测", "ZnfxVQDJob",1,4),
    RECORD("RECORD","录像","recordJob",1,8),
    BYKC("BYKC","备用考场检测", "ZnfxBYKCJob",0,16),
    SS("SS","截图", "",1,2),
    ;

    private String code;

    private String msg;

    private String jobName;
    
    private int enableSchedule;
    
    private int enableVal;

    ZnfxTaskTypeEnum(String code, String msg, String jobName,int enableSchedule,int enableVal ) {
        this.code = code;
        this.msg = msg;
        this.jobName = jobName;
        this.enableSchedule = enableSchedule;
        this.enableVal = enableVal;
    }

    public static ZnfxTaskTypeEnum findByCode(String code) {
        for (ZnfxTaskTypeEnum item : ZnfxTaskTypeEnum.values()) {
            if (item.getCode().equalsIgnoreCase(code)) {
                return item;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+ code);
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
    public String getJobName() {
        return jobName;
    }

	public int getEnableSchedule() {
		return enableSchedule;
	}

	public int getEnableVal() {
		return enableVal;
	}
}
