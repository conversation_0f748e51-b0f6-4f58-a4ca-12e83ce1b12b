package com.xcwlkj.attendance.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.xcwlkj.attendance.model.enums.ZnfxHCTypeEnum;
import com.xcwlkj.dfs.model.vo.FileItemVO;
import com.xcwlkj.dfs.model.vo.TimePathVO;
import com.xcwlkj.dfs.util.XcDfsClient;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ZnfxUtils {
	
	@Value("${xc.xcDfs.prefixUrl}")
	private String dfsServer;
	
	public static String KEY_ZNFX_TASKLIST="znfx:task:list";
	
	public static String KEY_ZNFX_TASKCLASSROOM_PREFIX="znfx:taskClassroom";
	
	public static String KEY_ZNFX_TASKCOURSE_PREFIX="znfx:taskCourse";
	
	public static String KEY_ZNFX_ROOM_PREFIX="znfx:room";
	
	public static String KEY_ZNFX_HCTYPE_PREFIX="znfx:hcType";
	
	public static String KEY_ZNFX_TASKPARAM_PREFIX="znfx:taskParam";
	
	public static String KEY_ZNFX_TASKTYPELIST_PREFIX="znfx:tkTypeList";
	
	public static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"); 
	
	public static String KEY_ZNFX_GLOBAL_ID = "global";
	
	/**
	 * 学期学年
	 */
	public static String KEY_ZNFX_KBCONF_XNXQ="znfx:kbconf:xnxq";
	/**
	 * 作息节次
	 */
	public static String KEY_ZNFX_KBCONF_ZXJC="znfx:kbconf:zxjc";
	/**
	 * 当前课表数据
	 */
	public static String KEY_ZNFX_KBCONF_CURRENTKB="znfx:kbconf:currentkb";
	/**
	 * 课表检测配置
	 */
	public static String KEY_ZNFX_KBCONF_DETECT="znfx:kbconf:detect";
	
	
	public static boolean compareTime(LocalTime nowLocalTime ,Date st,Date et) {
		boolean result = false;
		if(st!=null&&et!=null) {
			LocalTime stLocalTime = dateToLocalTime(st);
			LocalTime etLocalTime = dateToLocalTime(et);
			if(nowLocalTime.isAfter(stLocalTime)&&nowLocalTime.isBefore(etLocalTime)) {
				result = true;
			}
		}else {
			log.warn("开始时间或结束时间为空[{}][{}]",st,et);
		}
		return result;
	}
	
	public static LocalTime dateToLocalTime(Date date) {
		Instant instant = date.toInstant();
		ZoneId zone = ZoneId.systemDefault();
		LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
		return localDateTime.toLocalTime();
	}
	
	public static String buildTaskScheduleId(String type,String taskId) {
		return KEY_ZNFX_TASKCLASSROOM_PREFIX+"_"+type+":"+taskId;
	}
	
	public static String buildTaskSchedule(String type) {
		return KEY_ZNFX_TASKCLASSROOM_PREFIX+"_"+type+":"+KEY_ZNFX_GLOBAL_ID;
	}
	
	public static String buildTaskParamScheduleId(String taskId) {
		return KEY_ZNFX_TASKPARAM_PREFIX+":"+taskId;
	}
	
	public static String buildTaskParamSchedule() {
		return KEY_ZNFX_TASKPARAM_PREFIX+":"+KEY_ZNFX_GLOBAL_ID;
	}
	
	public static String buildCourseScheduleId(String type,String taskId) {
		return KEY_ZNFX_TASKCOURSE_PREFIX+"_"+type+":"+taskId;
	}
	
	public static void main(String[] args) {
		LocalDateTime dateTime =   LocalDateTime.now();
		Date date = new Date();
//		System.out.println(date.getDay());
		System.out.println(dateTime.getDayOfWeek().getValue());
	}
	
	public static String fetchImgIdFromUri(String url) {
		String id = "";
		if(StringUtils.isNotBlank(url)) {
			if(StringUtils.contains(url, "?")) {
				String[] queryArray = StringUtils.split(url,"?");
				for(String query:queryArray) {
					String[] paramStr = StringUtils.split(query,"&");
					for(String str : paramStr) {
						if(StringUtils.contains(str, "id=")) {
							id= StringUtils.substringAfter(str, "id=");
						}
					}
					if(StringUtils.isNotBlank(id)) {
						return id;
					}
				}
			}else {
				id = url;
			}
		}
		return id;
	}
	
	public static String buildTaskTypeListKey(String type) {
		return KEY_ZNFX_TASKTYPELIST_PREFIX+":"+type;
	}
	
	/**
	 * 根据id获取http地址
	 * @param id
	 * @return
	 */
	public String buildFileHttpPathById(String id) {
		String url ="";
		if(StringUtils.isNotBlank(id)) {
			TimePathVO timePathVO = XcDfsClient.timePath(2L, 0,id);
		    List<FileItemVO> fileList = timePathVO.getFileList();
		    if(!CollectionUtils.isEmpty(fileList)) {
		    	url =dfsServer+ "/remote/file/" +timePathVO.getFileList().get(0).getUrl();
		    }else {
		    	log.info("id为[{}] 找不到文件[{}]",id,timePathVO.getFileList());
		    }
		}
		return url;
	}
	
	public static String buildCameraRedisKeyByUri(ZnfxHCTypeEnum hcType, String uri) {
		return KEY_ZNFX_HCTYPE_PREFIX+":"+hcType.getCode()+":"+formatUri(uri);
	}
	
	public static String formatUri(String uri) {
		String formatUri = StringUtils.replace(uri, ":", "_");
		return formatUri;
	}
	
	public static String buildRoomRedisKey(String jsh) {
		return KEY_ZNFX_ROOM_PREFIX+":"+jsh;
	}
}
