/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 设备场所关系表
 * 
 * <AUTHOR>
 * @version $Id: JcxxSbcsgx.java, v 0.1 2022年09月30日 09时59分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "jcxx_sbcsgx")
public class JcxxSbcsgx implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /**  */
    @Id
    @Column(name = "fjh")
    private String            fjh;
    /** 场所编号（教室号)
             */
    @Id
    @Column(name = "csbh")
    private String            csbh;
    /** 设备编号 */
    @Id
    @Column(name = "sbbh")
    private String            sbbh;
    /** 场所名称 */
    @Column(name = "csmc")
    private String            csmc;
    /** 场所类型 */
    @Column(name = "cslx")
    private String            cslx;
    /** 设备名称 */
    @Column(name = "sbmc")
    private String            sbmc;
    /** 设备类型 */
    @Column(name = "sblx")
    private String            sblx;
    /** 节点祖先 */
    @Column(name = "jdzx")
    private String            jdzx;
    /** 考试管理机构标识码 */
    @Column(name = "ksgljgid")
    private String            ksgljgid;
    /**  */
    @Column(name = "ssjgbh")
    private String            ssjgbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


