/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.attendance.facade.remote;

import com.xcwlkj.attendance.model.domain.ZnfxJsRstj;
import com.xcwlkj.attendance.model.domain.ZnfxRstj;
import com.xcwlkj.attendance.model.dto.rsxxtj.GjjshcxrsDTO;
import com.xcwlkj.attendance.model.dto.rsxxtj.JshlbItemDTO;
import com.xcwlkj.attendance.model.req.rsxxtj.GjjshcxrsReqModel;
import com.xcwlkj.attendance.model.resp.rsxxtj.GjjshcxrsRespModel;
import com.xcwlkj.attendance.model.vo.rsxxtj.QytjlbItemVO;
import com.xcwlkj.attendance.model.vo.rsxxtj.RstjlbItemVO;
import com.xcwlkj.attendance.service.ZnfxJsRstjService;
import com.xcwlkj.attendance.service.ZnfxRstjService;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Rsxxtj控制层
 *
 * <AUTHOR>
 * @version $Id: RsxxtjController.java, v 0.1 2022年10月18日 18时39分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("RsxxtjRemoteController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class RsxxtjController extends BaseController {

    @Resource
    private ZnfxJsRstjService znfxJsRstjService;
    @Resource
    private ZnfxRstjService znfxRstjService;
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 1-根据教室号查询统计人数
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/remote/attendance/rsxxtj/gjjshcxrs")
    public Wrapper<GjjshcxrsRespModel> gjjshcxrs(@RequestBody GjjshcxrsReqModel reqModel) {
        log.info("收到请求开始：[1-根据教室号查询统计人数][/remote/attendance/rsxxtj/gjjshcxrs]reqModel:" + reqModel.toString());
        GjjshcxrsRespModel responseModel = new GjjshcxrsRespModel();
        GjjshcxrsDTO dto = new GjjshcxrsDTO();
        List<RstjlbItemVO> rstjlb = new ArrayList<>();
        List<JshlbItemDTO> jshList = reqModel.getJshlb();
        List<String> jshlb = jshList.stream().map(JshlbItemDTO::getJsh).collect(Collectors.toList());
        List<ZnfxJsRstj> rstjList = znfxJsRstjService.selectRstjByJsh(jshlb);
        if (!CollectionUtils.isEmpty(rstjList)) {
            for (ZnfxJsRstj temp : rstjList) {
                if (StringUtils.isNotBlank(temp.getJcjg())) {
                    Double jcjg;
                    try {
                        jcjg = Double.valueOf(temp.getJcjg());
                    } catch (Exception e) {
                        log.error("----jcjg:" + temp.getJcjg());
                        continue;
                    }
                    if (jcjg > 0) {
                        RstjlbItemVO item = new RstjlbItemVO();
                        //教室号
                        item.setJsh(temp.getJsh());
                        //教室名称
                        item.setJsmc(temp.getJsmc());
                        //统计人数
                        item.setRs(temp.getTjrs() == null ? 0 : temp.getTjrs());
                        //检测时间
                        item.setJcsj(temp.getWcsj() == null ? "" : sdf.format(temp.getWcsj()));
                        //区域统计列表
                        List<QytjlbItemVO> qytjlb = new ArrayList<>();
                        if (StringUtils.isNotBlank(temp.getGlbh())) {
                            log.info("glbh对应znfx_rstj的任务编号:" + temp.getGlbh());
                            List<String> rstjbhs = Arrays.asList(temp.getGlbh().split(","));
                            Example rstjExample = new Example(ZnfxRstj.class);
                            Example.Criteria rstjCri = rstjExample.createCriteria();
                            rstjCri.andIn("jcjlbh", rstjbhs);
                            List<ZnfxRstj> znfxRstjs = znfxRstjService.selectByExample(rstjExample);
                            if (!CollectionUtils.isEmpty(znfxRstjs)) {
                                for (ZnfxRstj rstj : znfxRstjs) {
                                    QytjlbItemVO qytjlbItem = new QytjlbItemVO();
                                    //区域ID
                                    qytjlbItem.setQyid(rstj.getSsqyid());
                                    //人数
                                    qytjlbItem.setRs(rstj.getRs() == null ? 0 : rstj.getRs());
                                    //截图信息ID
                                    qytjlbItem.setJtxx(StringUtils.isBlank(rstj.getJcjtxx()) ? "" : rstj.getJcjtxx());
                                    qytjlb.add(qytjlbItem);
                                }
                            }
                        }
                        item.setQytjlb(qytjlb);
                        rstjlb.add(item);
                    }
                }
            }
        }
        responseModel.setRstjlb(rstjlb);
        return WrapMapper.ok(responseModel);
    }

}