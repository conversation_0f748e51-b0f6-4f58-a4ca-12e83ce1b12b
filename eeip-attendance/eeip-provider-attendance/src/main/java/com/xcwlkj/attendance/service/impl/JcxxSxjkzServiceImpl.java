/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.attendance.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.xcwlkj.attendance.mapper.JcxxSxjkzMapper;
import com.xcwlkj.attendance.model.domain.JcxxSxjkz;
import com.xcwlkj.attendance.model.view.ZnfxSxjView;
import com.xcwlkj.attendance.model.view.ZnfxSxjViewParam;
import com.xcwlkj.attendance.service.JcxxSxjkzService;


/**
 * 摄像机扩展表服务
 * <AUTHOR>
 * @version $Id: JcxxSxjkzServiceImpl.java, v 0.1 2022年10月08日 09时29分 xcwlkj.com Exp $
 */
@Service
public class JcxxSxjkzServiceImpl extends BaseServiceImpl<JcxxSxjkzMapper, JcxxSxjkz> implements JcxxSxjkzService  {

    @Resource
    private JcxxSxjkzMapper modelMapper;

	@Override
	public List<ZnfxSxjView> findZnfxSxjViewList(String sxjyt) {
		ZnfxSxjViewParam param = new ZnfxSxjViewParam();
		param.setYt(sxjyt);
		return modelMapper.findZnfxSxjViewListByParam(param);
	}
    
	@Override
	public List<ZnfxSxjView> findZnfxSxjViewListByIds(List<String> idList) {
		return modelMapper.findZnfxSxjViewList( idList,null);
	}

	@Override
	public List<ZnfxSxjView> findZnfxSxjViewListByJshs(List<String> jshList) {
		return modelMapper.findZnfxSxjViewList( null,jshList);
	}
}