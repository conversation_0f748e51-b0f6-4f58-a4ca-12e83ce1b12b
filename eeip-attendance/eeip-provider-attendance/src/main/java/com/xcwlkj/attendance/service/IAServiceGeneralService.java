package com.xcwlkj.attendance.service;

import com.xcwlkj.attendance.model.adapter.req.BaseIAServiceReq;
import com.xcwlkj.attendance.model.adapter.resp.BaseIAServiceResp;

/**
 * 通用service，适合各种请求
 * 如果有请求存在特殊处理，请单独接口完成
 * <AUTHOR>
 */
public interface IAServiceGeneralService {
	
	 /**
     *  通用执行
     * @param baseIAManagerReq 请求对象
     * @param clazz 类
     * @param <T>   BaseIAServiceReq
     * @param <U>   BaseIAServiceResp
     * @return
     */
    <T extends BaseIAServiceReq, U extends BaseIAServiceResp> U execute(T baseIAManagerReq,Class<U> clazz);
    
}
