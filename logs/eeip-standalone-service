[2m2025-07-29 10:33:39.389[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8aab4025] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:39.864[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-29 10:33:41.985[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
[2m2025-07-29 10:33:41.986[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
[2m2025-07-29 10:33:41.988[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m The following profiles are active: alone
[2m2025-07-29 10:33:47.757[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode!
[2m2025-07-29 10:33:47.762[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-29 10:33:48.036[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 247ms. Found 0 Redis repository interfaces.
[2m2025-07-29 10:33:48.186[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-29 10:33:48.372[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
[2m2025-07-29 10:33:48.806[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=5d30900d-6b54-3262-aac1-9e6172c58d56
[2m2025-07-29 10:33:48.826[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
[2m2025-07-29 10:33:48.836[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
[2m2025-07-29 10:33:48.846[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
[2m2025-07-29 10:33:48.938[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$cb891cbe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:48.940[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$84bb61ee] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.009[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$6454d54d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.301[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6e913d28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.351[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$4a341aa5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.396[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$86554c4b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.434[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.478[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.488[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$d982f857] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:49.526[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8aab4025] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-29 10:33:50.129[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 8888 (http)
[2m2025-07-29 10:33:50.276[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 8263 ms
[2m2025-07-29 10:33:53.462[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.ServletEndpointRegistrar  [0;39m [2m:[0;39m Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
[2m2025-07-29 10:33:53.563[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-29 10:33:53.656[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-29 10:33:53.761[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-29 10:33:53.766[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsServerUrl=http://************:8811
[2m2025-07-29 10:33:53.766[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsChannel=ZJKSZHPT
[2m2025-07-29 10:33:53.766[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
[2m2025-07-29 10:33:53.766[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
[2m2025-07-29 10:33:54.706[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.interceptor.SqlLogInterceptor  [0;39m [2m:[0;39m [打印SQL拦截器创建]noticeTime=5.0秒
[2m2025-07-29 10:33:57.187[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.c.AsyncTaskExecutorConfiguration  [0;39m [2m:[0;39m Creating Async Task Executor
[2m2025-07-29 10:33:57.187[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-29 10:33:58.726[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m SMS Bean IAcsClient Start
[2m2025-07-29 10:33:58.739[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m 加载SMS Bean IAcsClient OK
[2m2025-07-29 10:34:06.980[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化####
[2m2025-07-29 10:34:06.988[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化成功####
[2m2025-07-29 10:34:08.225[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.taskcenter.DefaultHandleFactory   [0;39m [2m:[0;39m 配置线程池工作线程数量[16]
[2m2025-07-29 10:34:08.317[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 开始检查平台类型和初始化上级平台DFS客户端...
[2m2025-07-29 10:34:08.541[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-29 10:34:08.556[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: defaultPlat(String)
[2m2025-07-29 10:34:08.572[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:08.576[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 当前平台类型为：HISOME，开始初始化上级平台DFS客户端
[2m2025-07-29 10:34:08.578[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-29 10:34:08.578[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServerUrl(String)
[2m2025-07-29 10:34:08.586[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:08.586[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-29 10:34:08.586[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServerChannel(String)
[2m2025-07-29 10:34:08.591[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:08.596[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-29 10:34:08.596[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServiceAppId(String)
[2m2025-07-29 10:34:08.603[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:08.603[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-29 10:34:08.603[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServiceAppSecret(String)
[2m2025-07-29 10:34:08.608[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:08.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 上级平台DFS客户端初始化成功
[2m2025-07-29 10:34:08.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 上级平台DFS客户端自动初始化完成
[2m2025-07-29 10:34:10.524[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动中....
[2m2025-07-29 10:34:10.526[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
[2m2025-07-29 10:34:10.526[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:10.531[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-29 10:34:10.549[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[teskTask-jobTest111]
[2m2025-07-29 10:34:10.549[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
[2m2025-07-29 10:34:10.549[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
[2m2025-07-29 10:34:10.549[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
[2m2025-07-29 10:34:10.549[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
[2m2025-07-29 10:34:10.549[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
[2m2025-07-29 10:34:10.551[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
[2m2025-07-29 10:34:10.551[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
[2m2025-07-29 10:34:10.551[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
[2m2025-07-29 10:34:10.551[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]
[2m2025-07-29 10:34:10.554[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动完成
[2m2025-07-29 10:34:12.091[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Initializing ExecutorService 'taskScheduler'
[2m2025-07-29 10:34:12.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@15fdf386],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@77635a],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@1c612ef],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@235e73b0],]
[2m2025-07-29 10:34:12.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Registering Sleuth Hystrix Concurrency Strategy.
[2m2025-07-29 10:34:17.306[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
[2m2025-07-29 10:34:17.306[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
[2m2025-07-29 10:34:17.306[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/+/+/event/+]
[2m2025-07-29 10:34:17.331[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XxlJobConfig [0;39m [2m:[0;39m >>>>>>>>>>> xxl-job config init.
[2m2025-07-29 10:34:18.571[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.f.FreeMarkerAutoConfiguration   [0;39m [2m:[0;39m Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[2m2025-07-29 10:34:19.862[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 21 endpoint(s) beneath base path '/actuator'
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 1 subscriber(s).
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started _org.springframework.integration.errorLogger
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.handler.serviceActivator
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m started mqttGateway
[2m2025-07-29 10:34:21.281[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m started mqttGateway
[2m2025-07-29 10:34:21.311[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-29 10:34:24.203[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
[2m2025-07-29 10:34:24.586[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m started inbound
[2m2025-07-29 10:34:24.586[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Starting Quartz Scheduler now
[2m2025-07-29 10:34:24.686[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 8888 (http) with context path ''
[2m2025-07-29 10:34:24.686[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 8888
[2m2025-07-29 10:34:24.971[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m Started StandaloneApplication in 47.121 seconds (JVM running for 47.873)
[2m2025-07-29 10:34:24.999[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-alone.yml加载配置信息
[2m2025-07-29 10:34:24.999[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-alone.yml
[2m2025-07-29 10:34:25.001[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-alone.yml加载配置信息【成功】
[2m2025-07-29 10:34:25.046[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.cache.AbstractRedisDataCache [0;39m [2m:[0;39m [PkgDataFileServices]cleanup cache finished
[2m2025-07-29 10:34:25.056[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
[2m2025-07-29 10:34:25.056[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
[2m2025-07-29 10:34:25.072[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:25.072[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
[2m2025-07-29 10:34:25.072[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:25.086[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m <==    Updates: 295
[2m2025-07-29 10:34:25.088[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
[2m2025-07-29 10:34:25.088[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
[2m2025-07-29 10:34:25.088[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
[2m2025-07-29 10:34:25.101[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.z.r.z.ZookeeperRegistryCenter     [0;39m [2m:[0;39m Elastic job: zookeeper registry center init, server lists is: **************:2181.
[2m2025-07-29 10:34:25.378[0;39m [32m INFO [,d41f02514a88ec38,d4688b6efe61f1fe,false][0;39m [33m[eeip-standalone-service,d41f02514a88ec38,d4688b6efe61f1fe,93040cb160d98063,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465069","timestamp":1753756465069,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=d41f02514a88ec38, spanId=d41f02514a88ec38, nativeHeaders={spanTraceId=[d41f02514a88ec38], spanId=[d41f02514a88ec38], spanSampled=[0]}, mqtt_duplicate=false, id=b01cdcec-d75d-62c0-f66c-e9b42fca1cc1, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:34:25.383[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:34:25.383[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465069","timestamp":1753756465069,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:34:25.396[0;39m [32m INFO [,8175c43097ba401e,d771f4325a254181,false][0;39m [33m[eeip-standalone-service,8175c43097ba401e,d771f4325a254181,b14f380df4345cf8,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465084","timestamp":1753756465084,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=8175c43097ba401e, spanId=8175c43097ba401e, nativeHeaders={spanTraceId=[8175c43097ba401e], spanId=[8175c43097ba401e], spanSampled=[0]}, mqtt_duplicate=false, id=95d5bbe6-85f9-1f94-d3a1-51e0184ef59f, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:34:25.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:34:25.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465084","timestamp":1753756465084,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:34:25.416[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
[2m2025-07-29 10:34:25.421[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-29 10:34:25.421[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####开始缓存商户配置信息####
[2m2025-07-29 10:34:25.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####缓存商户配置信息缓存成功####
[2m2025-07-29 10:34:25.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-29 10:34:25.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-29 10:34:25.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####开始缓存配置信息####
[2m2025-07-29 10:34:25.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####缓存配置信息缓存成功####
[2m2025-07-29 10:34:25.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-29 10:34:25.429[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:34:25.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-29 10:34:25.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-29 10:34:25.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-29 10:34:25.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####开始缓存公共服务配置信息####
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####缓存公共服务配置信息缓存成功####
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####开始缓存听评课配置信息####
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####缓存听评课配置信息缓存成功####
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-29 10:34:25.431[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-29 10:34:25.431[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:34:25.421(Timestamp), 2025-07-29 10:34:25.421(Timestamp)
[2m2025-07-29 10:34:25.446[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####开始缓存校级身份核验平台配置信息####
[2m2025-07-29 10:34:25.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:25.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:34:25.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:34:25.421(Timestamp), 2025-07-29 10:34:25.421(Timestamp)
[2m2025-07-29 10:34:25.463[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:25.463[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:34:25.463[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:34:25.470[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:25.472[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:34:25.473[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:34:25.480[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:25.480[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:34:25.480[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:25.475(Timestamp), 228(Integer)
[2m2025-07-29 10:34:25.496[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:34:25.496[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:25.48(Timestamp), 228(Integer)
[2m2025-07-29 10:34:25.499[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:25.509[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:26.210[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
[2m2025-07-29 10:34:26.210[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化####
[2m2025-07-29 10:34:26.210[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:34:26.210[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:34:26.210[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:34:26.210[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:34:26.210[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
[2m2025-07-29 10:34:26.210[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==> Parameters: 1(String)
[2m2025-07-29 10:34:26.220[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-29 10:34:26.220[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
[2m2025-07-29 10:34:26.220[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:26.231[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 58
[2m2025-07-29 10:34:26.991[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
[2m2025-07-29 10:34:26.991[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:26.997[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 12
[2m2025-07-29 10:34:27.159[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
[2m2025-07-29 10:34:27.159[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:27.169[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 106
[2m2025-07-29 10:34:28.614[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
[2m2025-07-29 10:34:28.614[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:28.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-29 10:34:28.669[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
[2m2025-07-29 10:34:28.669[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:28.677[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-29 10:34:28.749[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
[2m2025-07-29 10:34:28.749[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:28.756[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-29 10:34:28.806[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
[2m2025-07-29 10:34:28.806[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:28.816[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 20
[2m2025-07-29 10:34:29.081[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
[2m2025-07-29 10:34:29.081[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.091[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-29 10:34:29.148[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
[2m2025-07-29 10:34:29.148[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.158[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-29 10:34:29.266[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
[2m2025-07-29 10:34:29.266[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.281[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-29 10:34:29.341[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化成功####
[2m2025-07-29 10:34:29.341[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化####
[2m2025-07-29 10:34:29.341[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-29 10:34:29.343[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化成功####
[2m2025-07-29 10:34:29.343[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化####
[2m2025-07-29 10:34:29.346[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
[2m2025-07-29 10:34:29.346[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 2025-07-29 10:34:29.345(Timestamp), 1(Integer)
[2m2025-07-29 10:34:29.356[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.361[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化成功####
[2m2025-07-29 10:34:29.361[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化####
[2m2025-07-29 10:34:29.361[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m dbName:[eeip_alone]
[2m2025-07-29 10:34:29.361[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.361[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)
[2m2025-07-29 10:34:29.366[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.366[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.366[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
[2m2025-07-29 10:34:29.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
[2m2025-07-29 10:34:29.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
[2m2025-07-29 10:34:29.390[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.390[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
[2m2025-07-29 10:34:29.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
[2m2025-07-29 10:34:29.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
[2m2025-07-29 10:34:29.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
[2m2025-07-29 10:34:29.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
[2m2025-07-29 10:34:29.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.429[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.429[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
[2m2025-07-29 10:34:29.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)
[2m2025-07-29 10:34:29.444[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.444[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.444[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)
[2m2025-07-29 10:34:29.450[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.450[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.450[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)
[2m2025-07-29 10:34:29.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)
[2m2025-07-29 10:34:29.466[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.466[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-29 10:34:29.466[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.480[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.480[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-29 10:34:29.481[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.492[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.492[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-29 10:34:29.492[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.506[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.506[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
[2m2025-07-29 10:34:29.508[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.517[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.517[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-29 10:34:29.517[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.533[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.533[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-29 10:34:29.533[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.546[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.546[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-29 10:34:29.546[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.556[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.556[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-29 10:34:29.556[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.571[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.574[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-29 10:34:29.574[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.586[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.586[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
[2m2025-07-29 10:34:29.586[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
[2m2025-07-29 10:34:29.596[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-29 10:34:29.596[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.596[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
[2m2025-07-29 10:34:29.601[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.601[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.601[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
[2m2025-07-29 10:34:29.611[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.611[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.611[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
[2m2025-07-29 10:34:29.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
[2m2025-07-29 10:34:29.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
[2m2025-07-29 10:34:29.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:34:29.636[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.636[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.641[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
[2m2025-07-29 10:34:29.646[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.646[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
[2m2025-07-29 10:34:29.646[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==> Parameters: 同步状态(String)
[2m2025-07-29 10:34:29.664[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:34:29.664[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
[2m2025-07-29 10:34:29.664[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.666[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
[2m2025-07-29 10:34:29.671[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.671[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.671[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)
[2m2025-07-29 10:34:29.676[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.676[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.676[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)
[2m2025-07-29 10:34:29.686[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.686[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.686[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ydsb_sbkszs(String), report_flag(String)
[2m2025-07-29 10:34:29.692[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.692[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-29 10:34:29.692[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ydsb_sbkszs(String), report_time(String)
[2m2025-07-29 10:34:29.699[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:29.702[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化成功####
[2m2025-07-29 10:34:29.702[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####缓存校级身份核验平台配置信息缓存成功####
[2m2025-07-29 10:34:29.702[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-29 10:34:30.138[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[      Thread-64][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_RSTJ_XXTS,启动成功！
[2m2025-07-29 10:34:30.640[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-29 10:34:30.671[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 31 ms
[2m2025-07-29 10:34:31.249[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-29 10:34:31.451[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: label not found
[2m2025-07-29 10:34:33.115[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[      Thread-63][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_ATTENDANCE_JTXX,启动成功！
[2m2025-07-29 10:34:40.376[0;39m [32m INFO [,914397e0163fe4f1,604b1de3cd2c9adc,false][0;39m [33m[eeip-standalone-service,914397e0163fe4f1,604b1de3cd2c9adc,7a8aa05a0bfc9e54,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480066","timestamp":1753756480066,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=914397e0163fe4f1, spanId=914397e0163fe4f1, nativeHeaders={spanTraceId=[914397e0163fe4f1], spanId=[914397e0163fe4f1], spanSampled=[0]}, mqtt_duplicate=false, id=bfb3816d-96f3-5dd2-cc52-8bdf29d785a6, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:34:40.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480066","timestamp":1753756480066,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:34:40.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:34:40.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:34:40.376[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:34:40.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:34:40.379[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:34:40.376(Timestamp), 2025-07-29 10:34:40.376(Timestamp)
[2m2025-07-29 10:34:40.388[0;39m [32m INFO [,ee102f43c1349ccb,1680d82a17eca07f,false][0;39m [33m[eeip-standalone-service,ee102f43c1349ccb,1680d82a17eca07f,c9376e79fef76b16,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480071","timestamp":1753756480071,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=ee102f43c1349ccb, spanId=ee102f43c1349ccb, nativeHeaders={spanTraceId=[ee102f43c1349ccb], spanId=[ee102f43c1349ccb], spanSampled=[0]}, mqtt_duplicate=false, id=9ba2ce79-7077-a079-9851-a9e2724d167e, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:34:40.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:34:40.390[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:34:40.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480071","timestamp":1753756480071,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:34:40.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:34:40.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:40.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:34:40.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:34:40.39(Timestamp), 2025-07-29 10:34:40.39(Timestamp)
[2m2025-07-29 10:34:40.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:40.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:34:40.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:34:40.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:40.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:34:40.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:34:40.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:40.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:34:40.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:40.418(Timestamp), 228(Integer)
[2m2025-07-29 10:34:40.442[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:40.442[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:34:40.442[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:40.426(Timestamp), 228(Integer)
[2m2025-07-29 10:34:40.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:55.399[0;39m [32m INFO [,ecb8580b2e8e8167,deb6c862f22af967,false][0;39m [33m[eeip-standalone-service,ecb8580b2e8e8167,deb6c862f22af967,ea44d4ccfe288eb5,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495066","timestamp":1753756495066,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=ecb8580b2e8e8167, spanId=ecb8580b2e8e8167, nativeHeaders={spanTraceId=[ecb8580b2e8e8167], spanId=[ecb8580b2e8e8167], spanSampled=[0]}, mqtt_duplicate=false, id=d642b9fb-bf5c-613d-2b47-1a2a44bf6c39, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:34:55.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:34:55.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495066","timestamp":1753756495066,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:34:55.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:34:55.399[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:34:55.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:34:55.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:34:55.399(Timestamp), 2025-07-29 10:34:55.399(Timestamp)
[2m2025-07-29 10:34:55.415[0;39m [32m INFO [,fa56615ed5e5641a,873da8a09512a6ab,false][0;39m [33m[eeip-standalone-service,fa56615ed5e5641a,873da8a09512a6ab,d9974ed3362b4dd3,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-6][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495072","timestamp":1753756495072,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=fa56615ed5e5641a, spanId=fa56615ed5e5641a, nativeHeaders={spanTraceId=[fa56615ed5e5641a], spanId=[fa56615ed5e5641a], spanSampled=[0]}, mqtt_duplicate=false, id=5dbaa5c4-69b7-d3bb-ba03-4d5e3171a305, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:34:55.415[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:34:55.415[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:34:55.415[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495072","timestamp":1753756495072,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:34:55.415[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:34:55.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:34:55.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:55.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:34:55.415(Timestamp), 2025-07-29 10:34:55.415(Timestamp)
[2m2025-07-29 10:34:55.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:34:55.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:34:55.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:55.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:34:55.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:55.424(Timestamp), 228(Integer)
[2m2025-07-29 10:34:55.432[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:55.433[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:34:55.433[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:34:55.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:34:55.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:34:55.442[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:34:55.444[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:55.441(Timestamp), 228(Integer)
[2m2025-07-29 10:34:55.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-29 10:35:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-29 10:35:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-29 10:35:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-29 10:35:00.030[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-29 10:35:00.031[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-29 10:35:00.031[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：12毫秒
[2m2025-07-29 10:35:00.033[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:00.033[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072910350002269592420511413249(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 12(Integer), 2025-07-29 10:35:00.019(Timestamp)
[2m2025-07-29 10:35:00.043[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：24毫秒
[2m2025-07-29 10:35:00.044[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:00.045[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072910350002269592420511413248(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 24(Integer), 2025-07-29 10:35:00.019(Timestamp)
[2m2025-07-29 10:35:00.606[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-29 10:35:10.381[0;39m [32m INFO [,883de860c5e4714d,7c7edf5fb81ac10d,false][0;39m [33m[eeip-standalone-service,883de860c5e4714d,7c7edf5fb81ac10d,f1ec1f3c71819ded,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-7][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510066","timestamp":1753756510066,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=883de860c5e4714d, spanId=883de860c5e4714d, nativeHeaders={spanTraceId=[883de860c5e4714d], spanId=[883de860c5e4714d], spanSampled=[0]}, mqtt_duplicate=false, id=4441b3a6-b8a9-e8ca-e1b6-7935dc1640ca, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510066","timestamp":1753756510066,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:10.381[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:10.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:10.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:10.381(Timestamp), 2025-07-29 10:35:10.381(Timestamp)
[2m2025-07-29 10:35:10.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:10.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:10.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:10.421[0;39m [32m INFO [,31d3b33c0bbd6c55,af16ab2445763999,false][0;39m [33m[eeip-standalone-service,31d3b33c0bbd6c55,af16ab2445763999,339be59ea0b43148,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-8][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510076","timestamp":1753756510076,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=31d3b33c0bbd6c55, spanId=31d3b33c0bbd6c55, nativeHeaders={spanTraceId=[31d3b33c0bbd6c55], spanId=[31d3b33c0bbd6c55], spanSampled=[0]}, mqtt_duplicate=false, id=be86ce1d-6fa6-a797-23e2-f0bbfc32fe14, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:10.421[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:10.421[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:10.421[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510076","timestamp":1753756510076,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:10.421[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:10.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:10.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:10.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:10.421(Timestamp), 2025-07-29 10:35:10.421(Timestamp)
[2m2025-07-29 10:35:10.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:10.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:10.421(Timestamp), 228(Integer)
[2m2025-07-29 10:35:10.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:10.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:10.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:10.439[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:10.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:10.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:10.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:10.446(Timestamp), 228(Integer)
[2m2025-07-29 10:35:10.461[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:14.638[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) 
[2m2025-07-29 10:35:14.639[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2025-07-29 10:35:14(String)
[2m2025-07-29 10:35:14.652[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-29 10:35:25.353[0;39m [32m INFO [,f63bee15d56496d9,42d13f1423adb9b2,false][0;39m [33m[eeip-standalone-service,f63bee15d56496d9,42d13f1423adb9b2,2af0c5bff85c9886,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-9][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525068","timestamp":1753756525068,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=f63bee15d56496d9, spanId=f63bee15d56496d9, nativeHeaders={spanTraceId=[f63bee15d56496d9], spanId=[f63bee15d56496d9], spanSampled=[0]}, mqtt_duplicate=false, id=b7af9b67-a602-f41b-b5dd-587efbff74db, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:25.353[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:25.353[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:25.353[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525068","timestamp":1753756525068,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:25.353[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:25.353[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:25.353[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:25.353(Timestamp), 2025-07-29 10:35:25.353(Timestamp)
[2m2025-07-29 10:35:25.369[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:25.369[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:25.369[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:25.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:25.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:25.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:25.4(Timestamp), 228(Integer)
[2m2025-07-29 10:35:25.417[0;39m [32m INFO [,2251f70f4e74d115,db0f2e0a0594dd50,true][0;39m [33m[eeip-standalone-service,2251f70f4e74d115,db0f2e0a0594dd50,b36f5a53f859c94d,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[TaskExecutor-10][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525080","timestamp":1753756525080,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=2251f70f4e74d115, spanId=2251f70f4e74d115, nativeHeaders={spanTraceId=[2251f70f4e74d115], spanId=[2251f70f4e74d115], spanSampled=[1]}, mqtt_duplicate=false, id=5eace3d9-866f-a0d5-0cee-5d3aa9c15a37, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:25.418[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:25.418[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:25.418[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525080","timestamp":1753756525080,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:25.418[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:25.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:25.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:25.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:25.418(Timestamp), 2025-07-29 10:35:25.418(Timestamp)
[2m2025-07-29 10:35:25.432[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:25.432[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:25.432[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:25.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:25.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:25.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:25.44(Timestamp), 228(Integer)
[2m2025-07-29 10:35:25.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:40.440[0;39m [32m INFO [,965aa817badce62c,b8a2927e8ce38865,true][0;39m [33m[eeip-standalone-service,965aa817badce62c,b8a2927e8ce38865,491eeb510555464f,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540070","timestamp":1753756540070,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=965aa817badce62c, spanId=965aa817badce62c, nativeHeaders={spanTraceId=[965aa817badce62c], spanId=[965aa817badce62c], spanSampled=[1]}, mqtt_duplicate=false, id=1ed2ef82-793a-2203-de5e-4a5617523237, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:40.440[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:40.440[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540070","timestamp":1753756540070,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:40.440[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:40.440[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:40.442[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:40.442[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:40.442(Timestamp), 2025-07-29 10:35:40.442(Timestamp)
[2m2025-07-29 10:35:40.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:40.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:40.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:40.462[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:40.462[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:40.466[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:40.462(Timestamp), 228(Integer)
[2m2025-07-29 10:35:40.478[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:40.491[0;39m [32m INFO [,784d7c5d7bce83b5,e89b34b947f16f56,false][0;39m [33m[eeip-standalone-service,784d7c5d7bce83b5,e89b34b947f16f56,d541b359382c186a,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540144","timestamp":1753756540144,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=784d7c5d7bce83b5, spanId=784d7c5d7bce83b5, nativeHeaders={spanTraceId=[784d7c5d7bce83b5], spanId=[784d7c5d7bce83b5], spanSampled=[0]}, mqtt_duplicate=false, id=a6ab6d2b-27dd-70de-1ed3-968d97319dbf, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:40.491[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:40.491[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:40.491[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540144","timestamp":1753756540144,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:40.491[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:40.493[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:40.493[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:40.491(Timestamp), 2025-07-29 10:35:40.491(Timestamp)
[2m2025-07-29 10:35:40.510[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:40.510[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:40.510[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:40.516[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:40.518[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:40.518[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:40.516(Timestamp), 228(Integer)
[2m2025-07-29 10:35:40.533[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:55.354[0;39m [32m INFO [,94971c7c09d5c394,40929835fda8f23d,false][0;39m [33m[eeip-standalone-service,94971c7c09d5c394,40929835fda8f23d,321abbdafa390879,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555066","timestamp":1753756555066,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=94971c7c09d5c394, spanId=94971c7c09d5c394, nativeHeaders={spanTraceId=[94971c7c09d5c394], spanId=[94971c7c09d5c394], spanSampled=[0]}, mqtt_duplicate=false, id=e2c8e6d8-7a8d-e98e-0395-2e22b89651a5, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:55.354[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:55.354[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:55.354[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555066","timestamp":1753756555066,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:55.354[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:55.356[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:55.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:55.356(Timestamp), 2025-07-29 10:35:55.356(Timestamp)
[2m2025-07-29 10:35:55.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:55.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:55.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:55.378[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:55.378[0;39m [32m INFO [,066550fbb06eb0e3,962c459697620e1c,false][0;39m [33m[eeip-standalone-service,066550fbb06eb0e3,962c459697620e1c,e2f01513dc29a55d,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555080","timestamp":1753756555080,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=066550fbb06eb0e3, spanId=066550fbb06eb0e3, nativeHeaders={spanTraceId=[066550fbb06eb0e3], spanId=[066550fbb06eb0e3], spanSampled=[0]}, mqtt_duplicate=false, id=ff8ecc1d-9bcd-369b-01e6-67274d7eb470, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:35:55.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:35:55.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:35:55.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555080","timestamp":1753756555080,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:35:55.378[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:35:55.378[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:55.378[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:55.378(Timestamp), 228(Integer)
[2m2025-07-29 10:35:55.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:35:55.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:55.378(Timestamp), 2025-07-29 10:35:55.378(Timestamp)
[2m2025-07-29 10:35:55.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:35:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:35:55.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:35:55.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:35:55.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:35:55.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:55.403(Timestamp), 228(Integer)
[2m2025-07-29 10:35:55.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:10.356[0;39m [32m INFO [,e879b01906a0ec17,e45f3ca733a74893,true][0;39m [33m[eeip-standalone-service,e879b01906a0ec17,e45f3ca733a74893,80874a0649696da9,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570069","timestamp":1753756570069,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=e879b01906a0ec17, spanId=e879b01906a0ec17, nativeHeaders={spanTraceId=[e879b01906a0ec17], spanId=[e879b01906a0ec17], spanSampled=[1]}, mqtt_duplicate=false, id=6a0818c4-f2fb-3be4-cd4e-b0bb65bb177c, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:10.356[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:10.356[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:10.356[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570069","timestamp":1753756570069,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:10.356[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:10.356[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:10.356[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:10.356(Timestamp), 2025-07-29 10:36:10.356(Timestamp)
[2m2025-07-29 10:36:10.372[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:10.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:10.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:10.381[0;39m [32m INFO [,7e13d908eb029c9c,ce91ccb39ad93d4c,false][0;39m [33m[eeip-standalone-service,7e13d908eb029c9c,ce91ccb39ad93d4c,f818e0a24abcbc67,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-6][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570083","timestamp":1753756570083,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=7e13d908eb029c9c, spanId=7e13d908eb029c9c, nativeHeaders={spanTraceId=[7e13d908eb029c9c], spanId=[7e13d908eb029c9c], spanSampled=[0]}, mqtt_duplicate=false, id=d0b47d9f-28e3-0472-5def-ec4553079c5d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570083","timestamp":1753756570083,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:10.381[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:10.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:10.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:10.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:10.381(Timestamp), 228(Integer)
[2m2025-07-29 10:36:10.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:10.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:10.381(Timestamp), 2025-07-29 10:36:10.381(Timestamp)
[2m2025-07-29 10:36:10.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:10.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:10.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:10.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:10.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:10.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:10.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:10.407(Timestamp), 228(Integer)
[2m2025-07-29 10:36:10.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:25.352[0;39m [32m INFO [,f388a15c19904fdb,a0da49bc1f775326,false][0;39m [33m[eeip-standalone-service,f388a15c19904fdb,a0da49bc1f775326,f7132e24cacedbe0,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-7][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585066","timestamp":1753756585066,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=f388a15c19904fdb, spanId=f388a15c19904fdb, nativeHeaders={spanTraceId=[f388a15c19904fdb], spanId=[f388a15c19904fdb], spanSampled=[0]}, mqtt_duplicate=false, id=1e593594-274a-b99b-e4ce-8e0ba711b7cd, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:25.352[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:25.356[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585066","timestamp":1753756585066,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:25.356[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:25.356[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:25.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:25.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:25.356(Timestamp), 2025-07-29 10:36:25.356(Timestamp)
[2m2025-07-29 10:36:25.372[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:25.372[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:25.372[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:25.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:25.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:25.381[0;39m [32m INFO [,7175d7a1c9c3d3d8,1400af579790e0d3,false][0;39m [33m[eeip-standalone-service,7175d7a1c9c3d3d8,1400af579790e0d3,837af88724d9f573,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-8][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585076","timestamp":1753756585076,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=7175d7a1c9c3d3d8, spanId=7175d7a1c9c3d3d8, nativeHeaders={spanTraceId=[7175d7a1c9c3d3d8], spanId=[7175d7a1c9c3d3d8], spanSampled=[0]}, mqtt_duplicate=false, id=81cf551f-1884-9453-d92b-5bb432905d7b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:25.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:25.38(Timestamp), 228(Integer)
[2m2025-07-29 10:36:25.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:25.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:25.381[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:25.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585076","timestamp":1753756585076,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:25.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:25.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:25.382(Timestamp), 2025-07-29 10:36:25.382(Timestamp)
[2m2025-07-29 10:36:25.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:25.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:25.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:25.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:25.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:25.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:25.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:25.405(Timestamp), 228(Integer)
[2m2025-07-29 10:36:25.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:40.387[0;39m [32m INFO [,4c30ec0a407e42be,a03c636fc76f46a2,false][0;39m [33m[eeip-standalone-service,4c30ec0a407e42be,a03c636fc76f46a2,5438bd1f5036c2a7,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-9][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600106","timestamp":1753756600106,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=4c30ec0a407e42be, spanId=4c30ec0a407e42be, nativeHeaders={spanTraceId=[4c30ec0a407e42be], spanId=[4c30ec0a407e42be], spanSampled=[0]}, mqtt_duplicate=false, id=67d7c4ed-6300-e19f-f0cb-e790064fd17e, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:40.388[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:40.388[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600106","timestamp":1753756600106,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:40.388[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:40.388[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:40.388[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:40.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:40.388(Timestamp), 2025-07-29 10:36:40.388(Timestamp)
[2m2025-07-29 10:36:40.395[0;39m [32m INFO [,80563cc2fb6e81b5,97f8b59ed98a483e,false][0;39m [33m[eeip-standalone-service,80563cc2fb6e81b5,97f8b59ed98a483e,7345b7108b4a3a16,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[TaskExecutor-10][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600112","timestamp":1753756600112,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=80563cc2fb6e81b5, spanId=80563cc2fb6e81b5, nativeHeaders={spanTraceId=[80563cc2fb6e81b5], spanId=[80563cc2fb6e81b5], spanSampled=[0]}, mqtt_duplicate=false, id=7be5ba4e-b1da-f789-1e4c-cbb87240135c, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:40.395[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:40.395[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600112","timestamp":1753756600112,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:40.395[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:40.395[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:40.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:40.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:40.395(Timestamp), 2025-07-29 10:36:40.395(Timestamp)
[2m2025-07-29 10:36:40.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:40.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:40.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:40.411[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:40.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:40.412(Timestamp), 228(Integer)
[2m2025-07-29 10:36:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:40.420[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:40.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:40.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:40.421(Timestamp), 228(Integer)
[2m2025-07-29 10:36:40.428[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:40.435[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:55.372[0;39m [32m INFO [,e9629a0a916d11aa,53625b5290b36134,false][0;39m [33m[eeip-standalone-service,e9629a0a916d11aa,53625b5290b36134,8149bf063eabd80a,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615095","timestamp":1753756615095,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=e9629a0a916d11aa, spanId=e9629a0a916d11aa, nativeHeaders={spanTraceId=[e9629a0a916d11aa], spanId=[e9629a0a916d11aa], spanSampled=[0]}, mqtt_duplicate=false, id=abf3da8e-ec6a-440c-9970-8155098df9c5, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:55.373[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:55.373[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:55.373[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615095","timestamp":1753756615095,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:55.373[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:55.374[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:55.374[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:55.374(Timestamp), 2025-07-29 10:36:55.374(Timestamp)
[2m2025-07-29 10:36:55.395[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:55.398[0;39m [32m INFO [,2c3fd11346f0fd25,5f326d06863e86c6,false][0;39m [33m[eeip-standalone-service,2c3fd11346f0fd25,5f326d06863e86c6,1ce979fbc358561a,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615101","timestamp":1753756615101,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=2c3fd11346f0fd25, spanId=2c3fd11346f0fd25, nativeHeaders={spanTraceId=[2c3fd11346f0fd25], spanId=[2c3fd11346f0fd25], spanSampled=[0]}, mqtt_duplicate=false, id=2551de88-3dd5-4bc8-d574-03af9aa3e992, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:36:55.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:36:55.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:36:55.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615101","timestamp":1753756615101,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:36:55.399[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:36:55.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:36:55.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:55.399(Timestamp), 2025-07-29 10:36:55.399(Timestamp)
[2m2025-07-29 10:36:55.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:55.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:55.403(Timestamp), 228(Integer)
[2m2025-07-29 10:36:55.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:55.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:36:55.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:36:55.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:36:55.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:36:55.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:36:55.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:55.424(Timestamp), 228(Integer)
[2m2025-07-29 10:36:55.447[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:10.381[0;39m [32m INFO [,f8038242ce6a550e,9cb7e29068d57018,false][0;39m [33m[eeip-standalone-service,f8038242ce6a550e,9cb7e29068d57018,041e645c3d083d4d,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630095","timestamp":1753756630095,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=f8038242ce6a550e, spanId=f8038242ce6a550e, nativeHeaders={spanTraceId=[f8038242ce6a550e], spanId=[f8038242ce6a550e], spanSampled=[0]}, mqtt_duplicate=false, id=5572d4f8-0c9a-b88d-865b-14cf38493694, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:10.381[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630095","timestamp":1753756630095,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:10.381[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:10.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:10.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:10.381(Timestamp), 2025-07-29 10:37:10.381(Timestamp)
[2m2025-07-29 10:37:10.393[0;39m [32m INFO [,e11a962f82935d21,eb87832ada852ab0,false][0;39m [33m[eeip-standalone-service,e11a962f82935d21,eb87832ada852ab0,2f7ee8da2b4f7a81,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630103","timestamp":1753756630103,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=e11a962f82935d21, spanId=e11a962f82935d21, nativeHeaders={spanTraceId=[e11a962f82935d21], spanId=[e11a962f82935d21], spanSampled=[0]}, mqtt_duplicate=false, id=e7c0f65e-0226-6afb-a33e-41d033a92141, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:10.394[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:10.394[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630103","timestamp":1753756630103,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:10.394[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:10.394[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:10.395[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:10.395[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:10.394(Timestamp), 2025-07-29 10:37:10.394(Timestamp)
[2m2025-07-29 10:37:10.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:10.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:10.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:10.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:10.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:10.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:10.405(Timestamp), 228(Integer)
[2m2025-07-29 10:37:10.410[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:10.411[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:10.411[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:10.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:10.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:10.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:10.417(Timestamp), 228(Integer)
[2m2025-07-29 10:37:10.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:10.433[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:25.377[0;39m [32m INFO [,93adf5e8753a7a0e,570e8ecd8fa0ceff,false][0;39m [33m[eeip-standalone-service,93adf5e8753a7a0e,570e8ecd8fa0ceff,322da8057f0a768b,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645096","timestamp":1753756645096,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=93adf5e8753a7a0e, spanId=93adf5e8753a7a0e, nativeHeaders={spanTraceId=[93adf5e8753a7a0e], spanId=[93adf5e8753a7a0e], spanSampled=[0]}, mqtt_duplicate=false, id=ca6b107f-0962-88c3-619f-952d1c658366, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:25.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:25.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:25.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645096","timestamp":1753756645096,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:25.378[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:25.379[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:25.379[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:25.378(Timestamp), 2025-07-29 10:37:25.378(Timestamp)
[2m2025-07-29 10:37:25.391[0;39m [32m INFO [,972e20a2dd15815e,c396817604c1ac56,false][0;39m [33m[eeip-standalone-service,972e20a2dd15815e,c396817604c1ac56,8193a9016e6586e1,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-6][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645102","timestamp":1753756645102,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=972e20a2dd15815e, spanId=972e20a2dd15815e, nativeHeaders={spanTraceId=[972e20a2dd15815e], spanId=[972e20a2dd15815e], spanSampled=[0]}, mqtt_duplicate=false, id=2a377ac9-4a43-9b5b-33b6-03a8c4a4124c, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:25.391[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:25.391[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:25.391[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:25.391[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645102","timestamp":1753756645102,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:25.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:25.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:25.391(Timestamp), 2025-07-29 10:37:25.391(Timestamp)
[2m2025-07-29 10:37:25.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:25.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:25.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:25.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:25.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:25.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:25.401(Timestamp), 228(Integer)
[2m2025-07-29 10:37:25.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:25.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:25.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:25.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:25.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:25.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:25.415(Timestamp), 228(Integer)
[2m2025-07-29 10:37:25.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:25.430[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:40.377[0;39m [32m INFO [,99b6efcbf526ec2c,c6cb611fb24fbab7,true][0;39m [33m[eeip-standalone-service,99b6efcbf526ec2c,c6cb611fb24fbab7,89c634c27f9a5b92,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-7][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660091","timestamp":1753756660092,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=99b6efcbf526ec2c, spanId=99b6efcbf526ec2c, nativeHeaders={spanTraceId=[99b6efcbf526ec2c], spanId=[99b6efcbf526ec2c], spanSampled=[1]}, mqtt_duplicate=false, id=744caa3a-2d5b-a621-d0c2-80963d265400, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:40.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660091","timestamp":1753756660092,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:40.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:40.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:40.377[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:40.378[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:40.378[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:40.377(Timestamp), 2025-07-29 10:37:40.377(Timestamp)
[2m2025-07-29 10:37:40.390[0;39m [32m INFO [,e2d6a07f53515462,b8dba173a418b837,false][0;39m [33m[eeip-standalone-service,e2d6a07f53515462,b8dba173a418b837,ea43eed6f3d0d3ff,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-8][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660100","timestamp":1753756660100,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=e2d6a07f53515462, spanId=e2d6a07f53515462, nativeHeaders={spanTraceId=[e2d6a07f53515462], spanId=[e2d6a07f53515462], spanSampled=[0]}, mqtt_duplicate=false, id=550a587e-5c67-483f-2c5c-d5b5d64d2cbc, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:40.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:40.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:40.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660100","timestamp":1753756660100,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:40.390[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:40.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:40.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:40.391(Timestamp), 2025-07-29 10:37:40.391(Timestamp)
[2m2025-07-29 10:37:40.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:40.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:40.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:40.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:40.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:40.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:40.401(Timestamp), 228(Integer)
[2m2025-07-29 10:37:40.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:40.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:40.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:40.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:40.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:40.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:40.414(Timestamp), 228(Integer)
[2m2025-07-29 10:37:40.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:40.430[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:55.396[0;39m [32m INFO [,17c45ebb7ebdfb90,5a26b52da0fd3bf6,false][0;39m [33m[eeip-standalone-service,17c45ebb7ebdfb90,5a26b52da0fd3bf6,8af2db704b8b4fb8,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-9][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675111","timestamp":1753756675111,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=17c45ebb7ebdfb90, spanId=17c45ebb7ebdfb90, nativeHeaders={spanTraceId=[17c45ebb7ebdfb90], spanId=[17c45ebb7ebdfb90], spanSampled=[0]}, mqtt_duplicate=false, id=bad43363-bfe1-ded3-7cc0-21cd2236158f, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:55.396[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:55.396[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675111","timestamp":1753756675111,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:55.396[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:55.396[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:55.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:55.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:55.396(Timestamp), 2025-07-29 10:37:55.396(Timestamp)
[2m2025-07-29 10:37:55.409[0;39m [32m INFO [,ffcdfaeee05665b0,ab34309ce3957ba3,false][0;39m [33m[eeip-standalone-service,ffcdfaeee05665b0,ab34309ce3957ba3,6a099963e5896867,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[TaskExecutor-10][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675121","timestamp":1753756675121,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=ffcdfaeee05665b0, spanId=ffcdfaeee05665b0, nativeHeaders={spanTraceId=[ffcdfaeee05665b0], spanId=[ffcdfaeee05665b0], spanSampled=[0]}, mqtt_duplicate=false, id=d0e432ec-8090-ad2e-3525-16bcf1bff8c4, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:37:55.409[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:37:55.409[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:37:55.409[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675121","timestamp":1753756675121,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:37:55.410[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:37:55.410[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:37:55.410[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:55.41(Timestamp), 2025-07-29 10:37:55.41(Timestamp)
[2m2025-07-29 10:37:55.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:55.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:55.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:55.420[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:55.420[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:55.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:55.42(Timestamp), 228(Integer)
[2m2025-07-29 10:37:55.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:55.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:37:55.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:37:55.433[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:37:55.434[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:37:55.434[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:55.434(Timestamp), 228(Integer)
[2m2025-07-29 10:37:55.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:37:55.452[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:10.388[0;39m [32m INFO [,c9689aa54d1e9758,f7da6526bb4a415b,false][0;39m [33m[eeip-standalone-service,c9689aa54d1e9758,f7da6526bb4a415b,df607730ebf6c5ba,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690103","timestamp":1753756690103,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=c9689aa54d1e9758, spanId=c9689aa54d1e9758, nativeHeaders={spanTraceId=[c9689aa54d1e9758], spanId=[c9689aa54d1e9758], spanSampled=[0]}, mqtt_duplicate=false, id=04196f8c-0716-0a22-0d07-dfc754a2aeb6, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:10.389[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690103","timestamp":1753756690103,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:10.389[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:10.389[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:10.389[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:10.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:10.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:10.389(Timestamp), 2025-07-29 10:38:10.389(Timestamp)
[2m2025-07-29 10:38:10.400[0;39m [32m INFO [,a6e0f396461d4146,0c9e089a080ee6dc,false][0;39m [33m[eeip-standalone-service,a6e0f396461d4146,0c9e089a080ee6dc,1b481f8af4439abb,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690113","timestamp":1753756690113,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=a6e0f396461d4146, spanId=a6e0f396461d4146, nativeHeaders={spanTraceId=[a6e0f396461d4146], spanId=[a6e0f396461d4146], spanSampled=[0]}, mqtt_duplicate=false, id=04c59a5a-fe80-8bb8-f779-c53b9c7a1ef6, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:10.400[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690113","timestamp":1753756690113,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:10.400[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:10.400[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:10.400[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:10.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:10.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:10.401(Timestamp), 2025-07-29 10:38:10.401(Timestamp)
[2m2025-07-29 10:38:10.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:10.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:10.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:10.411[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:10.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:10.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:10.411(Timestamp), 228(Integer)
[2m2025-07-29 10:38:10.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:10.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:10.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:10.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:10.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:10.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:10.424(Timestamp), 228(Integer)
[2m2025-07-29 10:38:10.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:10.438[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:25.378[0;39m [32m INFO [,1b2951f30da215e7,a54c87914dea8966,true][0;39m [33m[eeip-standalone-service,1b2951f30da215e7,a54c87914dea8966,35c9ebac2fb8bea9,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705091","timestamp":1753756705091,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=1b2951f30da215e7, spanId=1b2951f30da215e7, nativeHeaders={spanTraceId=[1b2951f30da215e7], spanId=[1b2951f30da215e7], spanSampled=[1]}, mqtt_duplicate=false, id=bb810849-4ddc-3dd5-2a79-83b25adc0507, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:25.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:25.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705091","timestamp":1753756705091,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:25.378[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:25.378[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:25.379[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:25.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:25.379(Timestamp), 2025-07-29 10:38:25.379(Timestamp)
[2m2025-07-29 10:38:25.390[0;39m [32m INFO [,4f5b9c71f21bb8bc,09690b90ec09c9cf,false][0;39m [33m[eeip-standalone-service,4f5b9c71f21bb8bc,09690b90ec09c9cf,32365b42851cab81,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705103","timestamp":1753756705103,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=4f5b9c71f21bb8bc, spanId=4f5b9c71f21bb8bc, nativeHeaders={spanTraceId=[4f5b9c71f21bb8bc], spanId=[4f5b9c71f21bb8bc], spanSampled=[0]}, mqtt_duplicate=false, id=58fd46af-1dbf-d987-1a37-4c9d5f616215, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:25.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:25.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:25.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705103","timestamp":1753756705103,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:25.391[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:25.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:25.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:25.391(Timestamp), 2025-07-29 10:38:25.391(Timestamp)
[2m2025-07-29 10:38:25.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:25.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:25.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:25.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:25.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:25.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:25.402(Timestamp), 228(Integer)
[2m2025-07-29 10:38:25.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:25.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:25.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:25.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:25.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:25.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:25.414(Timestamp), 228(Integer)
[2m2025-07-29 10:38:25.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:25.430[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:40.379[0;39m [32m INFO [,6258e45834f527b9,e525c389b7970e6d,false][0;39m [33m[eeip-standalone-service,6258e45834f527b9,e525c389b7970e6d,08ecc37b94906560,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720096","timestamp":1753756720096,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=6258e45834f527b9, spanId=6258e45834f527b9, nativeHeaders={spanTraceId=[6258e45834f527b9], spanId=[6258e45834f527b9], spanSampled=[0]}, mqtt_duplicate=false, id=46cb50b2-d9dc-ff1d-7d6d-88ddbdb97f5a, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:40.379[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:40.379[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:40.379[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720096","timestamp":1753756720096,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:40.379[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:40.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:40.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:40.38(Timestamp), 2025-07-29 10:38:40.38(Timestamp)
[2m2025-07-29 10:38:40.393[0;39m [32m INFO [,7ee201979e0f7119,95809e0403e15e52,false][0;39m [33m[eeip-standalone-service,7ee201979e0f7119,95809e0403e15e52,d8d5c7020edb0e11,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-6][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720104","timestamp":1753756720104,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=7ee201979e0f7119, spanId=7ee201979e0f7119, nativeHeaders={spanTraceId=[7ee201979e0f7119], spanId=[7ee201979e0f7119], spanSampled=[0]}, mqtt_duplicate=false, id=34345058-2895-783a-46ad-4686c4bba921, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:40.394[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:40.394[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720104","timestamp":1753756720104,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:40.394[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:40.394[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:40.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:40.395[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:40.394(Timestamp), 2025-07-29 10:38:40.394(Timestamp)
[2m2025-07-29 10:38:40.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:40.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:40.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:40.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:40.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:40.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:40.404(Timestamp), 228(Integer)
[2m2025-07-29 10:38:40.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:40.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:40.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:40.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:40.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:40.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:40.416(Timestamp), 228(Integer)
[2m2025-07-29 10:38:40.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:40.432[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:55.379[0;39m [32m INFO [,e072185558b31f70,ca82ccb8ffd0c46f,false][0;39m [33m[eeip-standalone-service,e072185558b31f70,ca82ccb8ffd0c46f,b44ea1e87f6d9384,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-7][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735094","timestamp":1753756735094,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=e072185558b31f70, spanId=e072185558b31f70, nativeHeaders={spanTraceId=[e072185558b31f70], spanId=[e072185558b31f70], spanSampled=[0]}, mqtt_duplicate=false, id=0d06f754-f383-777b-acae-5f1a0474829d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:55.379[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:55.379[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:55.379[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:55.379[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735094","timestamp":1753756735094,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:55.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:55.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:55.38(Timestamp), 2025-07-29 10:38:55.38(Timestamp)
[2m2025-07-29 10:38:55.395[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:55.397[0;39m [32m INFO [,f9da7cf538e171ea,d1a7ea2e56de1aaa,false][0;39m [33m[eeip-standalone-service,f9da7cf538e171ea,d1a7ea2e56de1aaa,eeb07b3b93582abb,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-8][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735099","timestamp":1753756735099,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=f9da7cf538e171ea, spanId=f9da7cf538e171ea, nativeHeaders={spanTraceId=[f9da7cf538e171ea], spanId=[f9da7cf538e171ea], spanSampled=[0]}, mqtt_duplicate=false, id=00646adb-6411-e378-23a1-4edc5035fcfe, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:38:55.397[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:38:55.397[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735099","timestamp":1753756735099,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:38:55.397[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:38:55.397[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:38:55.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:38:55.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:55.397(Timestamp), 2025-07-29 10:38:55.397(Timestamp)
[2m2025-07-29 10:38:55.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:55.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:55.403(Timestamp), 228(Integer)
[2m2025-07-29 10:38:55.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:55.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:38:55.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:38:55.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:38:55.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:38:55.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:38:55.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:55.425(Timestamp), 228(Integer)
[2m2025-07-29 10:38:55.441[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:10.375[0;39m [32m INFO [,399918f71a991017,1284c53314950ae6,false][0;39m [33m[eeip-standalone-service,399918f71a991017,1284c53314950ae6,43f4bd8fc35aa939,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-9][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750088","timestamp":1753756750088,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=399918f71a991017, spanId=399918f71a991017, nativeHeaders={spanTraceId=[399918f71a991017], spanId=[399918f71a991017], spanSampled=[0]}, mqtt_duplicate=false, id=7996c202-e301-a023-607b-bd04b8a8f77e, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:10.375[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:10.375[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:10.375[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750088","timestamp":1753756750088,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:10.375[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:10.375[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:10.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:10.375(Timestamp), 2025-07-29 10:39:10.375(Timestamp)
[2m2025-07-29 10:39:10.389[0;39m [32m INFO [,ee8cacb6272f1036,0be89e413b469bfe,false][0;39m [33m[eeip-standalone-service,ee8cacb6272f1036,0be89e413b469bfe,dc9e8c880ca433ac,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[TaskExecutor-10][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750094","timestamp":1753756750094,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=ee8cacb6272f1036, spanId=ee8cacb6272f1036, nativeHeaders={spanTraceId=[ee8cacb6272f1036], spanId=[ee8cacb6272f1036], spanSampled=[0]}, mqtt_duplicate=false, id=25db1e8a-1a06-e1ce-584b-c5f48edd854b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:10.389[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750094","timestamp":1753756750094,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:10.389[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:10.389[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:10.389[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:10.390[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:10.390[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:10.39(Timestamp), 2025-07-29 10:39:10.39(Timestamp)
[2m2025-07-29 10:39:10.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:10.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:10.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:10.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:10.399[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:10.399[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:10.398(Timestamp), 228(Integer)
[2m2025-07-29 10:39:10.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:10.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:10.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:10.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:10.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:10.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:10.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:10.414(Timestamp), 228(Integer)
[2m2025-07-29 10:39:10.428[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:25.376[0;39m [32m INFO [,889db82b60b78b54,825ede94aad86063,false][0;39m [33m[eeip-standalone-service,889db82b60b78b54,825ede94aad86063,59685b4167ddba45,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765097","timestamp":1753756765097,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=889db82b60b78b54, spanId=889db82b60b78b54, nativeHeaders={spanTraceId=[889db82b60b78b54], spanId=[889db82b60b78b54], spanSampled=[0]}, mqtt_duplicate=false, id=f1344d09-f14a-7a6c-4186-e2b86159ce89, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:25.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765097","timestamp":1753756765097,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:25.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:25.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:25.377[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:25.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:25.378[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:25.377(Timestamp), 2025-07-29 10:39:25.377(Timestamp)
[2m2025-07-29 10:39:25.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:25.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:25.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:25.406[0;39m [32m INFO [,973e93ca333cd22b,f2656246f7d87c10,false][0;39m [33m[eeip-standalone-service,973e93ca333cd22b,f2656246f7d87c10,9ff80fcf74169848,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765105","timestamp":1753756765105,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=973e93ca333cd22b, spanId=973e93ca333cd22b, nativeHeaders={spanTraceId=[973e93ca333cd22b], spanId=[973e93ca333cd22b], spanSampled=[0]}, mqtt_duplicate=false, id=0a3c06db-9209-39c5-8e58-9d71032899dd, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:25.406[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:25.406[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:25.406[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765105","timestamp":1753756765105,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:25.406[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:25.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:25.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:25.406(Timestamp), 2025-07-29 10:39:25.406(Timestamp)
[2m2025-07-29 10:39:25.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:25.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:25.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:25.415(Timestamp), 228(Integer)
[2m2025-07-29 10:39:25.430[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:25.430[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:25.431[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:25.438[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:25.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:25.441[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:25.441[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:25.44(Timestamp), 228(Integer)
[2m2025-07-29 10:39:25.459[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:40.377[0;39m [32m INFO [,e0946ee3bc231f75,f2eecf2cd0b44e02,false][0;39m [33m[eeip-standalone-service,e0946ee3bc231f75,f2eecf2cd0b44e02,954a9a45ec94ef45,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780094","timestamp":1753756780094,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=e0946ee3bc231f75, spanId=e0946ee3bc231f75, nativeHeaders={spanTraceId=[e0946ee3bc231f75], spanId=[e0946ee3bc231f75], spanSampled=[0]}, mqtt_duplicate=false, id=ddedbc4d-6517-3fb6-f6f5-e57686eb7c27, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:40.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780094","timestamp":1753756780094,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:40.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:40.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:40.377[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:40.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:40.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:40.377(Timestamp), 2025-07-29 10:39:40.377(Timestamp)
[2m2025-07-29 10:39:40.386[0;39m [32m INFO [,8f37492d7feb0d35,7280fe9d4c76efe9,false][0;39m [33m[eeip-standalone-service,8f37492d7feb0d35,7280fe9d4c76efe9,df9ece5cec8927de,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780103","timestamp":1753756780103,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=8f37492d7feb0d35, spanId=8f37492d7feb0d35, nativeHeaders={spanTraceId=[8f37492d7feb0d35], spanId=[8f37492d7feb0d35], spanSampled=[0]}, mqtt_duplicate=false, id=187d02e8-1476-62bc-6de9-244b3aecb06a, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:40.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:40.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780103","timestamp":1753756780103,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:40.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:40.386[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:40.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:40.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:40.386(Timestamp), 2025-07-29 10:39:40.386(Timestamp)
[2m2025-07-29 10:39:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:40.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:40.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:40.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:40.401(Timestamp), 228(Integer)
[2m2025-07-29 10:39:40.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:40.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:40.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:40.413(Timestamp), 228(Integer)
[2m2025-07-29 10:39:40.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:40.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:54.810[0;39m [32m INFO [,70b24ba175f27566,7b72f414adc70ade,false][0;39m [33m[eeip-standalone-service,70b24ba175f27566,7b72f414adc70ade,6f3f23c44d583034,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"devType":"175","jxxx_id":"134","device_id":"*********","getPkgMode":"2","csbh":"003","file_downdload_status_list":[{"type":"jkjx","version":"1","status":"1"}],"task_id":"2507291039541399702973768007680"},"sign":"","messageId":"downfile_1753756794008_*********","timestamp":1753756794008,"token":"9140d3d9e3cb44678a50c0a2ab57a255"}, headers={mqtt_receivedRetained=false, spanTraceId=70b24ba175f27566, spanId=70b24ba175f27566, nativeHeaders={spanTraceId=[70b24ba175f27566], spanId=[70b24ba175f27566], spanSampled=[0]}, mqtt_duplicate=false, id=a6217ecd-88e3-9056-2965-ffb2b028830b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:54.810[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:54.810[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:54.810[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:54.827[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理开始
[2m2025-07-29 10:39:54.836[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) 
[2m2025-07-29 10:39:54.836[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025-07-29 10:39:54.836(Timestamp), 1(Integer), 10(Integer), 134(String), 003(String)
[2m2025-07-29 10:39:54.868[0;39m [32m INFO [,8fbfe40841956e31,858d378e65d135cc,false][0;39m [33m[eeip-standalone-service,8fbfe40841956e31,858d378e65d135cc,d7384f838357ac35,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-6][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"devType":"175","jxxx_id":"134","device_id":"*********","getPkgMode":"2","csbh":"2011201","file_downdload_status_list":[{"type":"jkjx","version":"1","status":"1"}],"task_id":"2507291039541399702973768007680"},"sign":"","messageId":"downfile_1753756794537_*********","timestamp":1753756794537,"token":"fc58ac462df643ccb9ae391381aa10f2"}, headers={mqtt_receivedRetained=false, spanTraceId=8fbfe40841956e31, spanId=8fbfe40841956e31, nativeHeaders={spanTraceId=[8fbfe40841956e31], spanId=[8fbfe40841956e31], spanSampled=[0]}, mqtt_duplicate=false, id=b5e87891-e969-3708-9d92-cf86b96572d7, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:54.868[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:54.868[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:54.868[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:54.869[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理开始
[2m2025-07-29 10:39:54.870[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) 
[2m2025-07-29 10:39:54.870[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025-07-29 10:39:54.869(Timestamp), 1(Integer), 10(Integer), 134(String), 2011201(String)
[2m2025-07-29 10:39:54.918[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:54.918[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:54.920[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) 
[2m2025-07-29 10:39:54.920[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) 
[2m2025-07-29 10:39:54.920[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2507291039541399702973768007680(String), 1(Integer)
[2m2025-07-29 10:39:54.920[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2507291039541399702973768007680(String), 1(Integer)
[2m2025-07-29 10:39:54.929[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-29 10:39:54.930[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-29 10:39:54.931[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) 
[2m2025-07-29 10:39:54.931[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) 
[2m2025-07-29 10:39:54.931[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)
[2m2025-07-29 10:39:54.931[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)
[2m2025-07-29 10:39:54.940[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-29 10:39:54.940[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理结束
[2m2025-07-29 10:39:54.940[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-29 10:39:54.940[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理结束
[2m2025-07-29 10:39:55.148[0;39m [32m INFO [,056b0b84d75fe61b,f9dd1917607abb07,false][0;39m [33m[eeip-standalone-service,056b0b84d75fe61b,f9dd1917607abb07,2b11fc56a4f606e9,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-7][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"sign":"","token":"a50c28b373e54fb09e04752e0a54a3e8","data":{"device_id":"*********","jxxx_id":"134","file_downdload_status_list":[{"status":"1","version":"1","type":"jkjx"}],"getPkgMode":"2","task_id":"2507291039541399702973768007680","devType":"175","csbh":"********"},"timestamp":1753756794828,"messageId":"downfile_1753756794828_*********"}, headers={mqtt_receivedRetained=false, spanTraceId=056b0b84d75fe61b, spanId=056b0b84d75fe61b, nativeHeaders={spanTraceId=[056b0b84d75fe61b], spanId=[056b0b84d75fe61b], spanSampled=[0]}, mqtt_duplicate=false, id=b384fd03-a0ef-ad04-80e2-d012663eec87, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:55.148[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:55.148[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:55.148[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:55.148[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理开始
[2m2025-07-29 10:39:55.152[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) 
[2m2025-07-29 10:39:55.152[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025-07-29 10:39:55.148(Timestamp), 1(Integer), 10(Integer), 134(String), ********(String)
[2m2025-07-29 10:39:55.168[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:55.168[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) 
[2m2025-07-29 10:39:55.168[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2507291039541399702973768007680(String), 1(Integer)
[2m2025-07-29 10:39:55.177[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-29 10:39:55.177[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) 
[2m2025-07-29 10:39:55.177[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)
[2m2025-07-29 10:39:55.186[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:55.186[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理结束
[2m2025-07-29 10:39:55.376[0;39m [32m INFO [,28641e75ec447df0,fb8d0d506d9bfc91,false][0;39m [33m[eeip-standalone-service,28641e75ec447df0,fb8d0d506d9bfc91,c306f4530d5716a5,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-8][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795096","timestamp":1753756795096,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=28641e75ec447df0, spanId=28641e75ec447df0, nativeHeaders={spanTraceId=[28641e75ec447df0], spanId=[28641e75ec447df0], spanSampled=[0]}, mqtt_duplicate=false, id=4b1d8cc6-57c7-a6cc-c65c-001b0834afb0, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:55.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:55.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:55.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795096","timestamp":1753756795096,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:55.376[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:55.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:55.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:55.376(Timestamp), 2025-07-29 10:39:55.376(Timestamp)
[2m2025-07-29 10:39:55.386[0;39m [32m INFO [,5349c07179292276,67349798a0992d3f,false][0;39m [33m[eeip-standalone-service,5349c07179292276,67349798a0992d3f,a18f6159563670e9,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-9][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795105","timestamp":1753756795105,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=5349c07179292276, spanId=5349c07179292276, nativeHeaders={spanTraceId=[5349c07179292276], spanId=[5349c07179292276], spanSampled=[0]}, mqtt_duplicate=false, id=6c1f62b5-f63f-a246-a4f0-b0d731bed04a, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:55.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795105","timestamp":1753756795105,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:39:55.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:55.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:55.386[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:39:55.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:39:55.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:55.386(Timestamp), 2025-07-29 10:39:55.386(Timestamp)
[2m2025-07-29 10:39:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:55.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:55.404(Timestamp), 228(Integer)
[2m2025-07-29 10:39:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:39:55.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:39:55.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:39:55.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:39:55.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:55.413(Timestamp), 228(Integer)
[2m2025-07-29 10:39:55.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:55.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:55.631[0;39m [32m INFO [,0dd7523f0e6dfe08,6efa36876c19a735,false][0;39m [33m[eeip-standalone-service,0dd7523f0e6dfe08,6efa36876c19a735,549cd25ef5bae07d,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[TaskExecutor-10][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"sign":"","token":"64784c0e435a497eb0bd277de3f7e1eb","data":{"device_id":"*********","jxxx_id":"134","file_downdload_status_list":[{"status":"1","version":"1","type":"jkjx"}],"getPkgMode":"2","task_id":"2507291039541399702973768007680","devType":"175","csbh":"K3307820091100011"},"timestamp":1753756795406,"messageId":"downfile_1753756795406_*********"}, headers={mqtt_receivedRetained=false, spanTraceId=0dd7523f0e6dfe08, spanId=0dd7523f0e6dfe08, nativeHeaders={spanTraceId=[0dd7523f0e6dfe08], spanId=[0dd7523f0e6dfe08], spanSampled=[0]}, mqtt_duplicate=false, id=420d5672-a734-661b-8fc4-d37fb15a674d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]
[2m2025-07-29 10:39:55.631[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:55.631[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:39:55.631[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题...
[2m2025-07-29 10:39:55.631[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理开始
[2m2025-07-29 10:39:55.635[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) 
[2m2025-07-29 10:39:55.636[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025-07-29 10:39:55.631(Timestamp), 1(Integer), 10(Integer), 134(String), K3307820091100011(String)
[2m2025-07-29 10:39:55.652[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:55.652[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) 
[2m2025-07-29 10:39:55.652[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2507291039541399702973768007680(String), 1(Integer)
[2m2025-07-29 10:39:55.660[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-29 10:39:55.660[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) 
[2m2025-07-29 10:39:55.660[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)
[2m2025-07-29 10:39:55.673[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.selectByExample               [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-29 10:39:55.675[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE jx_distribute_task SET t_progress = ?,complete = ?,complete_time = ? WHERE ( ( id = ? ) ) 
[2m2025-07-29 10:39:55.675[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 100.00(String), 10(Integer), 2025-07-29 10:39:55.673(Timestamp), 2507291039541399702973768007680(String)
[2m2025-07-29 10:39:55.690[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.m.J.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:39:55.690[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.i.service.impl.JxsjxzztEventHandler [0;39m [2m:[0;39m 教学数据下载状态上报处理结束
[2m2025-07-29 10:40:00.002[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110440401808639620095703040
[2m2025-07-29 10:40:00.002[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110111540401808639620095703040
[2m2025-07-29 10:40:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-29 10:40:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：2502130940211339531603881230336
[2m2025-07-29 10:40:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-29 10:40:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-29 10:40:00.007[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新
[2m2025-07-29 10:40:00.007[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m =======开始查询和更新设备在线状态=========
[2m2025-07-29 10:40:00.007[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) 
[2m2025-07-29 10:40:00.007[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-29 10:40:00.012[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1, pageIndex=0)]
[2m2025-07-29 10:40:00.012[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-29 10:40:00.012[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-29 10:40:00.012[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：8毫秒
[2m2025-07-29 10:40:00.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:00.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072910400002269594936967984128(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 8(Integer), 2025-07-29 10:40:00.004(Timestamp)
[2m2025-07-29 10:40:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 16
[2m2025-07-29 10:40:00.022[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-
[2m2025-07-29 10:40:00.063[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM sb_sbxx WHERE ((sczt = ? AND sblx IN (?, ?, ?, ?))) 
[2m2025-07-29 10:40:00.063[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m ==> Parameters: 0(String), 170(String), 171(String), 172(String), 175(String)
[2m2025-07-29 10:40:00.068[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753756799777, result=JetlinksPageRespModel(pageIndex=0, pageSize=1, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807)]))
[2m2025-07-29 10:40:00.068[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1000, pageIndex=0)]
[2m2025-07-29 10:40:00.072[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:00.083[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/remote/access/device/listByDeviceNames]参数[DeviceStatusReqModel(deviceNames=[])]
[2m2025-07-29 10:40:00.100[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753756799809, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])
[2m2025-07-29 10:40:00.100[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753756799816, result=JetlinksPageRespModel(pageIndex=0, pageSize=1000, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807), JetlinksDeviceFirewareRespModel(id=00024720d16a, deviceName=36路办公室超脑, productId=HISOME-DX000, version=2025.05.24.2002), JetlinksDeviceFirewareRespModel(id=*********, deviceName=考场电子班牌, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=ATN240303000100YT55YBF, deviceName=ATN240303000100YT55YBF, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.3_test3), JetlinksDeviceFirewareRespModel(id=ddcdb3ac5810283f, deviceName=ddcdb3ac5810283f, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.8), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ027590, deviceName=柴行楼教学楼103_DENCGW829_C04MB10BDJ027590, productId=CENCGW100_C, version=2025.01.27), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ07548e, deviceName=柴行楼柴行楼zq002_DENCGW829_C04MB10BDJ07548e, productId=CENCGW100_C, version=2025.03.18), JetlinksDeviceFirewareRespModel(id=*********, deviceName=DI思源楼1201考务*********, productId=PROD-DZBP, version=1.3.3), JetlinksDeviceFirewareRespModel(id=*********, deviceName=DI思源楼1203考场*********, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=G04754401, deviceName=G04754401, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754402, deviceName=G04754402, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754403, deviceName=G04754403, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754404, deviceName=G04754404, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754405, deviceName=G04754405, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=*********, deviceName=致远楼致远楼2105_*********, productId=PROD-DZBP, version=1.5.6_c), JetlinksDeviceFirewareRespModel(id=G04754407, deviceName=G04754407, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754408, deviceName=G04754408, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754409, deviceName=G04754409, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G16733827, deviceName=志勤楼教学楼207_G16733827, productId=PROD-DZBP, version=1.1.5), JetlinksDeviceFirewareRespModel(id=*********, deviceName=电子班牌测试, productId=PROD-DZBP, version=1.3.3_c), JetlinksDeviceFirewareRespModel(id=KEP20240707001, deviceName=黄河交通学院_身份核验移动终端2, productId=HISOME_ANDROID, version=20241224-1122#2.1.8), JetlinksDeviceFirewareRespModel(id=PID388E2207007544, deviceName=像素设备, productId=PROD-DZBP, version=1.1.6)]))
[2m2025-07-29 10:40:00.105[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 
[2m2025-07-29 10:40:00.108[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.108[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.05.20.1807(String), 2025-07-29 10:40:00.1(Timestamp), (String), 0(Integer), 0(String), 00024700113e(String)
[2m2025-07-29 10:40:00.108[0;39m [32m INFO [,fe096f00724fe4c7,77955fa48de7b755,true][0;39m [33m[eeip-standalone-service,fe096f00724fe4c7,77955fa48de7b755,c5ca07d0b4f78ff2,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"device_id":"*********","run_mode":"teach","TIMESTAMP":1753756799805},"sign":"","messageId":"putDeviceRunMode_1753756799805_*********","timestamp":1753756799805,"token":"fc58ac462df643ccb9ae391381aa10f2"}, headers={mqtt_receivedRetained=false, spanTraceId=fe096f00724fe4c7, spanId=fe096f00724fe4c7, nativeHeaders={spanTraceId=[fe096f00724fe4c7], spanId=[fe096f00724fe4c7], spanSampled=[1]}, mqtt_duplicate=false, id=3a4e479e-d39c-795f-3d86-f435989dd804, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_device_run_mode, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:00.108[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:00.108[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m 接收到班牌设备当前运行模式变更: {"data":{"device_id":"*********","run_mode":"teach","TIMESTAMP":1753756799805},"sign":"","messageId":"putDeviceRunMode_1753756799805_*********","timestamp":1753756799805,"token":"fc58ac462df643ccb9ae391381aa10f2"}
[2m2025-07-29 10:40:00.108[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_device_run_mode] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:00.108[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_device_run_mode]无法处理
[2m2025-07-29 10:40:00.110[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list
[2m2025-07-29 10:40:00.114[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m 班牌设备当前运行模式变更信息: {"device_id":"*********","run_mode":"EDU","tIMESTAMP":"2025-07-29 10:40:00"}
[2m2025-07-29 10:40:00.121[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.121[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.121[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.05.24.2002(String), 2025-07-29 10:40:00.121(Timestamp), (String), 0(Integer), 0(String), 00024720d16a(String)
[2m2025-07-29 10:40:00.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.136[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.136[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6(String), 2025-07-29 10:40:00.135(Timestamp), (String), 0(Integer), 0(String), *********(String)
[2m2025-07-29 10:40:00.141[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=Wrapper(code=200, message=success, result={deviceList=[{deviceName=00024700113e, status=0}, {deviceName=00024720d16a, status=0}, {deviceName=1231241157, status=0}, {deviceName=*********, status=1}, {deviceName=ATN240303000100YT55YBF, status=0}, {deviceName=CENCGW100_S01MA00BBA000D1000E, status=0}, {deviceName=CENCGW100_SIVb770d1a6a1ec5b44, status=1}, {deviceName=cillum, status=0}, {deviceName=ddcdb3ac5810283f, status=0}, {deviceName=DENCGW829_C04MB10BDJ027590, status=1}, {deviceName=DENCGW829_C04MB10BDJ07548e, status=0}, {deviceName=*********, status=1}, {deviceName=*********, status=1}, {deviceName=*********, status=1}, {deviceName=G16733827, status=0}, {deviceName=*********, status=1}, {deviceName=ipsumadk, status=0}, {deviceName=KEP20240707001, status=1}, {deviceName=PID388E2207007544, status=0}, {deviceName=S30SZA2023140181, status=0}, {deviceName=test0012, status=0}, {deviceName=test01-xlh, status=0}, {deviceName=test091903, status=0}, {deviceName=testYdzd004, status=0}, {deviceName=testYdzdXlh001, status=0}, {deviceName=testYdzdXlh002, status=0}], totalRows=26})
[2m2025-07-29 10:40:00.141[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.m.J.updateYxmsByXlh               [0;39m [2m:[0;39m ==>  Preparing: update jcxx_dzbpkz SET yxms = ?, xgsj = ? where xlh = ? 
[2m2025-07-29 10:40:00.141[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.m.J.updateYxmsByXlh               [0;39m [2m:[0;39m ==> Parameters: EDU(String), 2025-07-29 10:40:00(String), *********(String)
[2m2025-07-29 10:40:00.146[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/*********/properties/read, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/*********/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/*********/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))
[2m2025-07-29 10:40:00.146[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：144毫秒
[2m2025-07-29 10:40:00.151[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.151[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:00.151[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072910400002269594936951206913(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 144(Integer), 2025-07-29 10:40:00.002(Timestamp)
[2m2025-07-29 10:40:00.159[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.m.J.updateYxmsByXlh               [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.166[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,zhzxsj = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? ) ) ) 
[2m2025-07-29 10:40:00.166[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1(String), 2025-07-29 10:40:00.146(Timestamp), 0(Integer), *********(String), CENCGW100_SIVb770d1a6a1ec5b44(String), DENCGW829_C04MB10BDJ027590(String), *********(String), *********(String), *********(String), *********(String), KEP20240707001(String)
[2m2025-07-29 10:40:00.187[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.187[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.3_test3(String), 2025-07-29 10:40:00.151(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ATN240303000100YT55YBF(String)
[2m2025-07-29 10:40:00.190[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 40
[2m2025-07-29 10:40:00.190[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) ) ) 
[2m2025-07-29 10:40:00.190[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 0(String), 0(Integer), 00024700113e(String), 00024720d16a(String), 1231241157(String), ATN240303000100YT55YBF(String), CENCGW100_S01MA00BBA000D1000E(String), cillum(String), ddcdb3ac5810283f(String), DENCGW829_C04MB10BDJ07548e(String), G16733827(String), ipsumadk(String), PID388E2207007544(String), S30SZA2023140181(String), test0012(String), test01-xlh(String), test091903(String), testYdzd004(String), testYdzdXlh001(String), testYdzdXlh002(String)
[2m2025-07-29 10:40:00.203[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.203[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.203[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.8(String), 2025-07-29 10:40:00.203(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ddcdb3ac5810283f(String)
[2m2025-07-29 10:40:00.207[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 31
[2m2025-07-29 10:40:00.207[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m getCssbZxzt - 开始获取场所设备在线状态.
[2m2025-07-29 10:40:00.216[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM sb_sbxx sb LEFT JOIN sb_sbcsgx gx ON sb.sbxxbh = gx.sbbh WHERE sb.sczt = '0' AND sb.sblx = '171' AND gx.csbh IS NOT NULL AND sb.xlh IS NOT NULL 
[2m2025-07-29 10:40:00.216[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-29 10:40:00.218[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.218[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.218[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:00.224[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.01.27(String), 2025-07-29 10:40:00.218(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ027590(String)
[2m2025-07-29 10:40:00.224[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,ksjhbh,ccm,kmm,ccmc,kmmc,kssj,jssj,yxcdsj,kssbqksj,ksrcsj,zc,kspch,cjsj,xgsj,xn,xq,scztw FROM ks_kscc WHERE ( ( scztw = ? and kssj <= ? and jssj >= ? ) ) 
[2m2025-07-29 10:40:00.224[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 2025-07-29 10:40:00.224(Timestamp), 2025-07-29 10:40:00.224(Timestamp)
[2m2025-07-29 10:40:00.231[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-29 10:40:00.231[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m getCssbZxzt - 获取场所设备在线状态. [OK]
[2m2025-07-29 10:40:00.231[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m ========设备在线状态更新完成=========
[2m2025-07-29 10:40:00.231[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110440401808639620095703040  总共耗时：229毫秒
[2m2025-07-29 10:40:00.231[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:00.231[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072910400002269594936951206912(String), 23110110440401808639620095703040(String), jobSbzxztGx(String), sbzxztCxAndGxTaskService(String), 0(Integer), 229(Integer), 2025-07-29 10:40:00.002(Timestamp)
[2m2025-07-29 10:40:00.236[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.239[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.239[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.03.18(String), 2025-07-29 10:40:00.236(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ07548e(String)
[2m2025-07-29 10:40:00.256[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.256[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.256[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.3.3(String), 2025-07-29 10:40:00.256(Timestamp), (String), 0(Integer), 0(String), *********(String)
[2m2025-07-29 10:40:00.270[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.275[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.275[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6(String), 2025-07-29 10:40:00.27(Timestamp), (String), 0(Integer), 0(String), *********(String)
[2m2025-07-29 10:40:00.291[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.291[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.291[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.291(Timestamp), (String), 0(Integer), 0(String), G04754401(String)
[2m2025-07-29 10:40:00.305[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.305[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.306[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.305(Timestamp), (String), 0(Integer), 0(String), G04754402(String)
[2m2025-07-29 10:40:00.316[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.316[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.316[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.316(Timestamp), (String), 0(Integer), 0(String), G04754403(String)
[2m2025-07-29 10:40:00.332[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.332[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.332[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.332(Timestamp), (String), 0(Integer), 0(String), G04754404(String)
[2m2025-07-29 10:40:00.346[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.346[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.346[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.346(Timestamp), (String), 0(Integer), 0(String), G04754405(String)
[2m2025-07-29 10:40:00.361[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.361[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.361[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6_c(String), 2025-07-29 10:40:00.361(Timestamp), (String), 0(Integer), 0(String), *********(String)
[2m2025-07-29 10:40:00.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.377(Timestamp), (String), 0(Integer), 0(String), G04754407(String)
[2m2025-07-29 10:40:00.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.392(Timestamp), (String), 0(Integer), 0(String), G04754408(String)
[2m2025-07-29 10:40:00.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-29 10:40:00.407(Timestamp), (String), 0(Integer), 0(String), G04754409(String)
[2m2025-07-29 10:40:00.423[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.423[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.423[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1.5(String), 2025-07-29 10:40:00.423(Timestamp), (String), 0(Integer), 0(String), G16733827(String)
[2m2025-07-29 10:40:00.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.439[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.439[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.3.3_c(String), 2025-07-29 10:40:00.436(Timestamp), (String), 0(Integer), 0(String), *********(String)
[2m2025-07-29 10:40:00.455[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.456[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.8(String), 2025-07-29 10:40:00.455(Timestamp), 20241224-1122(String), 0(Integer), 0(String), KEP20240707001(String)
[2m2025-07-29 10:40:00.471[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:00.471[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-29 10:40:00.471[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1.6(String), 2025-07-29 10:40:00.471(Timestamp), (String), 0(Integer), 0(String), PID388E2207007544(String)
[2m2025-07-29 10:40:00.486[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-29 10:40:00.486[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新10条
[2m2025-07-29 10:40:00.486[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新 OK, 共计更新10条
[2m2025-07-29 10:40:00.486[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：2502130940211339531603881230336  总共耗时：482毫秒
[2m2025-07-29 10:40:00.486[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:00.486[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072910400002269594936967984129(String), 2502130940211339531603881230336(String), 设备固件版本更新(String), deviceVersionUpdateTaskService(String), (String), 0(Integer), 482(Integer), 2025-07-29 10:40:00.004(Timestamp)
[2m2025-07-29 10:40:10.376[0;39m [32m INFO [,a1cd301d5f03dcd0,b4479d83269d9e6f,false][0;39m [33m[eeip-standalone-service,a1cd301d5f03dcd0,b4479d83269d9e6f,0dad865c5a0cc2b5,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810092","timestamp":1753756810092,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=a1cd301d5f03dcd0, spanId=a1cd301d5f03dcd0, nativeHeaders={spanTraceId=[a1cd301d5f03dcd0], spanId=[a1cd301d5f03dcd0], spanSampled=[0]}, mqtt_duplicate=false, id=00ff218e-221c-a4d2-a2ba-e96c3941cb19, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:10.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:10.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810092","timestamp":1753756810092,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:10.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:10.376[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:10.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:10.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:10.376(Timestamp), 2025-07-29 10:40:10.376(Timestamp)
[2m2025-07-29 10:40:10.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:10.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:10.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:10.393[0;39m [32m INFO [,8224c0f8b89cbb6f,567ca4e95171f2d5,false][0;39m [33m[eeip-standalone-service,8224c0f8b89cbb6f,567ca4e95171f2d5,8ab08776eb9082e9,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810099","timestamp":1753756810099,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=8224c0f8b89cbb6f, spanId=8224c0f8b89cbb6f, nativeHeaders={spanTraceId=[8224c0f8b89cbb6f], spanId=[8224c0f8b89cbb6f], spanSampled=[0]}, mqtt_duplicate=false, id=83c17688-7e92-713e-f20d-39192436bd78, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:10.393[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:10.393[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:10.393[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810099","timestamp":1753756810099,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:10.393[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:10.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:10.393[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:10.393(Timestamp), 2025-07-29 10:40:10.393(Timestamp)
[2m2025-07-29 10:40:10.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:10.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:10.400[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:10.4(Timestamp), 228(Integer)
[2m2025-07-29 10:40:10.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:10.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:10.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:10.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:10.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:10.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:10.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:10.418(Timestamp), 228(Integer)
[2m2025-07-29 10:40:10.433[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:25.366[0;39m [32m INFO [,4f5829c740d61d89,6ed5eb3b8fd28f3e,true][0;39m [33m[eeip-standalone-service,4f5829c740d61d89,6ed5eb3b8fd28f3e,f725445a9a5ba561,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825093","timestamp":1753756825093,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=4f5829c740d61d89, spanId=4f5829c740d61d89, nativeHeaders={spanTraceId=[4f5829c740d61d89], spanId=[4f5829c740d61d89], spanSampled=[1]}, mqtt_duplicate=false, id=ef8ca6d8-0fac-b9bc-a5a3-2f442ab33d8b, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:25.367[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:25.367[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825093","timestamp":1753756825093,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:25.367[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:25.368[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:25.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:25.369[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:25.368(Timestamp), 2025-07-29 10:40:25.368(Timestamp)
[2m2025-07-29 10:40:25.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:25.384[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:25.385[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:25.386[0;39m [32m INFO [,3186a570687dcdb3,05c88503fbbd895f,false][0;39m [33m[eeip-standalone-service,3186a570687dcdb3,05c88503fbbd895f,3578bb4fecbbe0e5,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825098","timestamp":1753756825098,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=3186a570687dcdb3, spanId=3186a570687dcdb3, nativeHeaders={spanTraceId=[3186a570687dcdb3], spanId=[3186a570687dcdb3], spanSampled=[0]}, mqtt_duplicate=false, id=b27da244-3e71-2bef-6d66-aed55d45cb73, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:25.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:25.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:25.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825098","timestamp":1753756825098,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:25.386[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:25.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:25.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:25.386(Timestamp), 2025-07-29 10:40:25.386(Timestamp)
[2m2025-07-29 10:40:25.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:25.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:25.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:25.391(Timestamp), 228(Integer)
[2m2025-07-29 10:40:25.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:25.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:25.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:25.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:25.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:25.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:25.410[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:25.409(Timestamp), 228(Integer)
[2m2025-07-29 10:40:25.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:40.376[0;39m [32m INFO [,0489bce5d369ab2b,7d188bd74b72cff5,false][0;39m [33m[eeip-standalone-service,0489bce5d369ab2b,7d188bd74b72cff5,d0b882d7fa4f6ed2,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-6][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840090","timestamp":1753756840090,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=0489bce5d369ab2b, spanId=0489bce5d369ab2b, nativeHeaders={spanTraceId=[0489bce5d369ab2b], spanId=[0489bce5d369ab2b], spanSampled=[0]}, mqtt_duplicate=false, id=77a53fb2-b5d6-dc70-4158-aaa90ce0553d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:40.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:40.376[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:40.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840090","timestamp":1753756840090,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:40.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:40.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:40.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:40.376(Timestamp), 2025-07-29 10:40:40.376(Timestamp)
[2m2025-07-29 10:40:40.388[0;39m [32m INFO [,ac3ccf82a6ceada8,aeab42654568a5db,false][0;39m [33m[eeip-standalone-service,ac3ccf82a6ceada8,aeab42654568a5db,664c42f368721b6b,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-7][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840099","timestamp":1753756840099,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=ac3ccf82a6ceada8, spanId=ac3ccf82a6ceada8, nativeHeaders={spanTraceId=[ac3ccf82a6ceada8], spanId=[ac3ccf82a6ceada8], spanSampled=[0]}, mqtt_duplicate=false, id=2cdb1e09-5d4d-de0a-54e2-6046caec642f, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:40.388[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840099","timestamp":1753756840099,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:40.388[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:40.388[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:40.388[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:40.388[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:40.388[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:40.388(Timestamp), 2025-07-29 10:40:40.388(Timestamp)
[2m2025-07-29 10:40:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:40.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:40.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:40.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:40.398(Timestamp), 228(Integer)
[2m2025-07-29 10:40:40.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:40.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:40.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:40.411[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:40.411[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:40.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:40.411(Timestamp), 228(Integer)
[2m2025-07-29 10:40:40.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:40.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:55.392[0;39m [32m INFO [,81384fcc1c62d1b4,b701e948876a182f,false][0;39m [33m[eeip-standalone-service,81384fcc1c62d1b4,b701e948876a182f,726887f73317a325,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-8][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855105","timestamp":1753756855105,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=81384fcc1c62d1b4, spanId=81384fcc1c62d1b4, nativeHeaders={spanTraceId=[81384fcc1c62d1b4], spanId=[81384fcc1c62d1b4], spanSampled=[0]}, mqtt_duplicate=false, id=fe8a289e-5e52-d274-1d25-40c8675be1b7, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:55.392[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:55.392[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855105","timestamp":1753756855105,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:55.392[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:55.392[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:55.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:55.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:55.392(Timestamp), 2025-07-29 10:40:55.392(Timestamp)
[2m2025-07-29 10:40:55.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:55.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:55.408[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:55.413[0;39m [32m INFO [,a9a22b2cb7be7769,d17a305906dcaf4e,false][0;39m [33m[eeip-standalone-service,a9a22b2cb7be7769,d17a305906dcaf4e,c2823e4ff713946b,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-9][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855116","timestamp":1753756855116,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=a9a22b2cb7be7769, spanId=a9a22b2cb7be7769, nativeHeaders={spanTraceId=[a9a22b2cb7be7769], spanId=[a9a22b2cb7be7769], spanSampled=[0]}, mqtt_duplicate=false, id=5b869cb5-22ff-88aa-5048-83d8f4b1839b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:40:55.413[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855116","timestamp":1753756855116,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:40:55.413[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:40:55.413[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:40:55.413[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:40:55.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:40:55.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:55.413(Timestamp), 2025-07-29 10:40:55.413(Timestamp)
[2m2025-07-29 10:40:55.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:55.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:55.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:55.417(Timestamp), 228(Integer)
[2m2025-07-29 10:40:55.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:55.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:40:55.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:40:55.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:40:55.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:40:55.438[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:40:55.438[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:55.436(Timestamp), 228(Integer)
[2m2025-07-29 10:40:55.450[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:10.366[0;39m [32m INFO [,0ecd2ee876458769,763ba44f3d43e9b2,false][0;39m [33m[eeip-standalone-service,0ecd2ee876458769,763ba44f3d43e9b2,919b5ad35bbeb9b3,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[TaskExecutor-10][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756870090","timestamp":1753756870090,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=0ecd2ee876458769, spanId=0ecd2ee876458769, nativeHeaders={spanTraceId=[0ecd2ee876458769], spanId=[0ecd2ee876458769], spanSampled=[0]}, mqtt_duplicate=false, id=eed3b5e8-5199-9f1f-e3ca-82c3c21ce65d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:41:10.366[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:41:10.366[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:41:10.370[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756870090","timestamp":1753756870090,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:41:10.370[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:41:10.370[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:41:10.370[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:41:10.37(Timestamp), 2025-07-29 10:41:10.37(Timestamp)
[2m2025-07-29 10:41:10.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:10.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:41:10.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:41:10.390[0;39m [32m INFO [,72c6f60a50123897,300f5677e9a5dacc,true][0;39m [33m[eeip-standalone-service,72c6f60a50123897,300f5677e9a5dacc,75331733807ce1ec,true][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756870098","timestamp":1753756870098,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=72c6f60a50123897, spanId=72c6f60a50123897, nativeHeaders={spanTraceId=[72c6f60a50123897], spanId=[72c6f60a50123897], spanSampled=[1]}, mqtt_duplicate=false, id=a67fe27b-d422-245a-09d3-b803627e3a12, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:41:10.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:41:10.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756870098","timestamp":1753756870098,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:41:10.390[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:41:10.390[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:41:10.390[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:41:10.390[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:41:10.39(Timestamp), 2025-07-29 10:41:10.39(Timestamp)
[2m2025-07-29 10:41:10.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:41:10.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:41:10.391[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:41:10.391(Timestamp), 228(Integer)
[2m2025-07-29 10:41:10.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:10.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:41:10.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:41:10.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:10.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:41:10.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:41:10.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:41:10.413(Timestamp), 228(Integer)
[2m2025-07-29 10:41:10.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:25.366[0;39m [32m INFO [,bd5d93edbc7d8382,56933cdb6ea2ae48,false][0;39m [33m[eeip-standalone-service,bd5d93edbc7d8382,56933cdb6ea2ae48,66130211a15f106e,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756885092","timestamp":1753756885092,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=bd5d93edbc7d8382, spanId=bd5d93edbc7d8382, nativeHeaders={spanTraceId=[bd5d93edbc7d8382], spanId=[bd5d93edbc7d8382], spanSampled=[0]}, mqtt_duplicate=false, id=af6a8ff2-d6e9-3cbd-397e-79d6a57fa405, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:41:25.366[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:41:25.366[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756885092","timestamp":1753756885092,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:41:25.366[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:41:25.366[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:41:25.366[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:41:25.366[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:41:25.366(Timestamp), 2025-07-29 10:41:25.366(Timestamp)
[2m2025-07-29 10:41:25.384[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:25.384[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:41:25.384[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:41:25.385[0;39m [32m INFO [,5aea2bd8e5510bd3,bc1118187c3a68e9,false][0;39m [33m[eeip-standalone-service,5aea2bd8e5510bd3,bc1118187c3a68e9,86c87b6a11d2e68f,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756885100","timestamp":1753756885100,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=5aea2bd8e5510bd3, spanId=5aea2bd8e5510bd3, nativeHeaders={spanTraceId=[5aea2bd8e5510bd3], spanId=[5aea2bd8e5510bd3], spanSampled=[0]}, mqtt_duplicate=false, id=751b886d-bd18-d48c-2731-5faf9c9f37f4, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:41:25.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:41:25.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:41:25.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756885100","timestamp":1753756885100,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:41:25.386[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:41:25.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:41:25.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:41:25.386(Timestamp), 2025-07-29 10:41:25.386(Timestamp)
[2m2025-07-29 10:41:25.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:41:25.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:41:25.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:41:25.389(Timestamp), 228(Integer)
[2m2025-07-29 10:41:25.399[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:25.399[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:41:25.399[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:41:25.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:25.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:41:25.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:41:25.406[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:41:25.406(Timestamp), 228(Integer)
[2m2025-07-29 10:41:25.424[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:40.371[0;39m [32m INFO [,33f35132dd390f9c,a3be3ea446de701a,false][0;39m [33m[eeip-standalone-service,33f35132dd390f9c,a3be3ea446de701a,86f9fe7cbc0cf34c,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756900091","timestamp":1753756900091,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=33f35132dd390f9c, spanId=33f35132dd390f9c, nativeHeaders={spanTraceId=[33f35132dd390f9c], spanId=[33f35132dd390f9c], spanSampled=[0]}, mqtt_duplicate=false, id=a78deaa0-3446-4773-c8e7-6a9f994399b9, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:41:40.371[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:41:40.371[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755352808","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756900091","timestamp":1753756900091,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:41:40.371[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:41:40.371[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:41:40.373[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:41:40.373[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:41:40.371(Timestamp), 2025-07-29 10:41:40.371(Timestamp)
[2m2025-07-29 10:41:40.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:40.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:41:40.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:41:40.386[0;39m [32m INFO [,78235a2d974b1d6f,17d207c370ab52e4,false][0;39m [33m[eeip-standalone-service,78235a2d974b1d6f,17d207c370ab52e4,fc0dca117956c6fc,false][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756900097","timestamp":1753756900097,"token":"46fae610bd7246e48604603f1cce21bf"}, headers={mqtt_receivedRetained=false, spanTraceId=78235a2d974b1d6f, spanId=78235a2d974b1d6f, nativeHeaders={spanTraceId=[78235a2d974b1d6f], spanId=[78235a2d974b1d6f], spanSampled=[0]}, mqtt_duplicate=false, id=3102af6e-2405-1b12-5285-d8042cc746ae, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]
[2m2025-07-29 10:41:40.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题...
[2m2025-07-29 10:41:40.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:*********
[2m2025-07-29 10:41:40.386[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m 接收到教师考勤信息: {"data":{"pkbbh":"2507281604421399422327325720695","device_id":"*********","curriculum_id":"wmzd(kz)","time":"1753755448074","job_number":"JSGH202410105","place_id":"2011203","pkkssj":"2025-07-29 10:30:00","pkjssj":"2025-07-29 12:10:00"},"sign":"","messageId":"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756900097","timestamp":1753756900097,"token":"46fae610bd7246e48604603f1cce21bf"}
[2m2025-07-29 10:41:40.386[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m 设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理
[2m2025-07-29 10:41:40.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) 
[2m2025-07-29 10:41:40.386[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m ==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:41:40.386(Timestamp), 2025-07-29 10:41:40.386(Timestamp)
[2m2025-07-29 10:41:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:41:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:41:40.392[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:41:40.392(Timestamp), 228(Integer)
[2m2025-07-29 10:41:40.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqDkjlMapper.insert        [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:40.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? 
[2m2025-07-29 10:41:40.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)
[2m2025-07-29 10:41:40.407[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-29 10:41:40.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.mapper.KqJskqMapper.selectOne     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-29 10:41:40.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? 
[2m2025-07-29 10:41:40.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m ==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:41:40.412(Timestamp), 228(Integer)
[2m2025-07-29 10:41:40.428[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m24360[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.a.m.KqJskqMapper.updateByPrimaryKey [0;39m [2m:[0;39m <==    Updates: 1
