{"@timestamp":"2025-07-29T02:33:39.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8aab4025] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:39.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-29T02:33:41.985Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-29T02:33:41.986Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-29T02:33:41.988Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-29T02:33:47.757Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-29T02:33:47.762Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-29T02:33:48.036Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 247ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-29T02:33:48.186Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-29T02:33:48.372Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-29T02:33:48.806Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=5d30900d-6b54-3262-aac1-9e6172c58d56"}
{"@timestamp":"2025-07-29T02:33:48.826Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-29T02:33:48.836Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-29T02:33:48.846Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-29T02:33:48.938Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$cb891cbe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:48.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$84bb61ee] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.009Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$6454d54d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.301Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6e913d28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.351Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$4a341aa5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.396Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$86554c4b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.434Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.478Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.488Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$d982f857] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:49.526Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8aab4025] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:33:50.129Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-29T02:33:50.276Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 8263 ms"}
{"@timestamp":"2025-07-29T02:33:53.462Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-29T02:33:53.563Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-29T02:33:53.656Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-29T02:33:53.761Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-29T02:33:53.766Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-29T02:33:53.766Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-29T02:33:53.766Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-29T02:33:53.766Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-29T02:33:54.706Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-29T02:33:57.187Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-29T02:33:57.187Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-29T02:33:58.726Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-29T02:33:58.739Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-29T02:34:06.980Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T02:34:06.988Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T02:34:08.225Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T02:34:08.317Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T02:34:08.541Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-29T02:34:08.556Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-29T02:34:08.572Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:08.576Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-29T02:34:08.578Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-29T02:34:08.578Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-29T02:34:08.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:08.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-29T02:34:08.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-29T02:34:08.591Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:08.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-29T02:34:08.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-29T02:34:08.603Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:08.603Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-29T02:34:08.603Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-29T02:34:08.608Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:08.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-29T02:34:08.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-29T02:34:10.524Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-29T02:34:10.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-29T02:34:10.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:10.531Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-29T02:34:10.549Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-29T02:34:10.549Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-29T02:34:10.549Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-29T02:34:10.549Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-29T02:34:10.549Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-29T02:34:10.549Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-29T02:34:10.551Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-29T02:34:10.551Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-29T02:34:10.551Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-29T02:34:10.551Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]"}
{"@timestamp":"2025-07-29T02:34:10.554Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-29T02:34:12.091Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-29T02:34:12.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@15fdf386],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@77635a],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@1c612ef],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@235e73b0],]"}
{"@timestamp":"2025-07-29T02:34:12.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-29T02:34:17.306Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-29T02:34:17.306Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-29T02:34:17.306Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-29T02:34:17.331Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-29T02:34:18.571Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T02:34:19.862Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-29T02:34:21.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-29T02:34:21.311Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-29T02:34:24.203Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-29T02:34:24.586Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-29T02:34:24.586Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-29T02:34:24.686Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-29T02:34:24.686Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-29T02:34:24.971Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 47.121 seconds (JVM running for 47.873)"}
{"@timestamp":"2025-07-29T02:34:24.999Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-29T02:34:24.999Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-alone.yml"}
{"@timestamp":"2025-07-29T02:34:25.001Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-29T02:34:25.046Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-29T02:34:25.056Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-29T02:34:25.056Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-29T02:34:25.072Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:25.072Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-29T02:34:25.072Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:25.086Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 295"}
{"@timestamp":"2025-07-29T02:34:25.088Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-29T02:34:25.088Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-29T02:34:25.088Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-29T02:34:25.101Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-29T02:34:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"d41f02514a88ec38","span":"d4688b6efe61f1fe","parent":"93040cb160d98063","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465069\",\"timestamp\":1753756465069,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=d41f02514a88ec38, spanId=d41f02514a88ec38, nativeHeaders={spanTraceId=[d41f02514a88ec38], spanId=[d41f02514a88ec38], spanSampled=[0]}, mqtt_duplicate=false, id=b01cdcec-d75d-62c0-f66c-e9b42fca1cc1, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:34:25.383Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:34:25.383Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465069\",\"timestamp\":1753756465069,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:34:25.396Z","severity":"INFO","service":"eeip-standalone-service","trace":"8175c43097ba401e","span":"d771f4325a254181","parent":"b14f380df4345cf8","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465084\",\"timestamp\":1753756465084,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=8175c43097ba401e, spanId=8175c43097ba401e, nativeHeaders={spanTraceId=[8175c43097ba401e], spanId=[8175c43097ba401e], spanSampled=[0]}, mqtt_duplicate=false, id=95d5bbe6-85f9-1f94-d3a1-51e0184ef59f, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:34:25.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:34:25.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756465084\",\"timestamp\":1753756465084,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:34:25.416Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-29T02:34:25.421Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-29T02:34:25.421Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-29T02:34:25.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-29T02:34:25.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-29T02:34:25.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-29T02:34:25.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-29T02:34:25.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-29T02:34:25.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-29T02:34:25.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:34:25.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-29T02:34:25.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-29T02:34:25.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-29T02:34:25.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-29T02:34:25.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:34:25.421(Timestamp), 2025-07-29 10:34:25.421(Timestamp)"}
{"@timestamp":"2025-07-29T02:34:25.446Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-29T02:34:25.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:25.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:34:25.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:34:25.421(Timestamp), 2025-07-29 10:34:25.421(Timestamp)"}
{"@timestamp":"2025-07-29T02:34:25.463Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:25.463Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:34:25.463Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:34:25.470Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:25.472Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:34:25.473Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:34:25.480Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:25.480Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:34:25.480Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:25.475(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:34:25.496Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:34:25.496Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:25.48(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:34:25.499Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:25.509Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-29T02:34:26.210Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-29T02:34:26.220Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-29T02:34:26.220Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-29T02:34:26.220Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:26.231Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-29T02:34:26.991Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-29T02:34:26.991Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:26.997Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-29T02:34:27.159Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-29T02:34:27.159Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:27.169Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-29T02:34:28.614Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-29T02:34:28.614Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:28.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-29T02:34:28.669Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-29T02:34:28.669Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:28.677Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-29T02:34:28.749Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-29T02:34:28.749Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:28.756Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-29T02:34:28.806Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-29T02:34:28.806Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:28.816Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-29T02:34:29.081Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-29T02:34:29.081Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.091Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-29T02:34:29.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-29T02:34:29.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.158Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-29T02:34:29.266Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-29T02:34:29.266Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.281Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-29T02:34:29.341Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-29T02:34:29.341Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-29T02:34:29.341Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-29T02:34:29.343Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-29T02:34:29.343Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-29T02:34:29.346Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-29T02:34:29.346Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-29 10:34:29.345(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-29T02:34:29.356Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.361Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-29T02:34:29.361Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-29T02:34:29.361Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-29T02:34:29.361Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.361Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-29T02:34:29.366Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.366Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.366Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-29T02:34:29.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-29T02:34:29.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-29T02:34:29.390Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.390Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-29T02:34:29.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-29T02:34:29.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-29T02:34:29.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-29T02:34:29.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-29T02:34:29.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-29T02:34:29.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-29T02:34:29.444Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.444Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.444Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-29T02:34:29.450Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.450Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.450Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-29T02:34:29.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-29T02:34:29.466Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.466Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-29T02:34:29.466Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.480Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.480Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-29T02:34:29.481Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.492Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.492Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-29T02:34:29.492Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.506Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.506Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-29T02:34:29.508Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.517Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.517Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-29T02:34:29.517Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.533Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.533Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-29T02:34:29.533Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.546Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.546Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-29T02:34:29.546Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.556Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.556Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-29T02:34:29.556Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.571Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.574Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-29T02:34:29.574Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-29T02:34:29.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-29T02:34:29.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-29T02:34:29.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-29T02:34:29.601Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.601Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.601Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-29T02:34:29.611Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.611Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.611Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-29T02:34:29.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-29T02:34:29.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-29T02:34:29.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:34:29.636Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.636Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.641Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-29T02:34:29.646Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.646Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-29T02:34:29.646Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-29T02:34:29.664Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:34:29.664Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-29T02:34:29.664Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.666Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-29T02:34:29.671Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.671Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.671Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-29T02:34:29.676Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.676Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.676Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-29T02:34:29.686Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.686Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.686Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ydsb_sbkszs(String), report_flag(String)"}
{"@timestamp":"2025-07-29T02:34:29.692Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.692Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-29T02:34:29.692Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ydsb_sbkszs(String), report_time(String)"}
{"@timestamp":"2025-07-29T02:34:29.699Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:29.702Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-29T02:34:29.702Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-29T02:34:29.702Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-29T02:34:30.138Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"Thread-64","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_RSTJ_XXTS,启动成功！"}
{"@timestamp":"2025-07-29T02:34:30.640Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"RMI TCP Connection(4)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-29T02:34:30.671Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"RMI TCP Connection(4)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Completed initialization in 31 ms"}
{"@timestamp":"2025-07-29T02:34:31.249Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"RMI TCP Connection(3)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-29T02:34:31.451Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"RMI TCP Connection(3)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-29T02:34:33.115Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"Thread-63","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_ATTENDANCE_JTXX,启动成功！"}
{"@timestamp":"2025-07-29T02:34:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"914397e0163fe4f1","span":"604b1de3cd2c9adc","parent":"7a8aa05a0bfc9e54","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480066\",\"timestamp\":1753756480066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=914397e0163fe4f1, spanId=914397e0163fe4f1, nativeHeaders={spanTraceId=[914397e0163fe4f1], spanId=[914397e0163fe4f1], spanSampled=[0]}, mqtt_duplicate=false, id=bfb3816d-96f3-5dd2-cc52-8bdf29d785a6, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:34:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480066\",\"timestamp\":1753756480066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:34:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:34:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:34:40.376Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:34:40.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:34:40.379Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:34:40.376(Timestamp), 2025-07-29 10:34:40.376(Timestamp)"}
{"@timestamp":"2025-07-29T02:34:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"ee102f43c1349ccb","span":"1680d82a17eca07f","parent":"c9376e79fef76b16","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480071\",\"timestamp\":1753756480071,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=ee102f43c1349ccb, spanId=ee102f43c1349ccb, nativeHeaders={spanTraceId=[ee102f43c1349ccb], spanId=[ee102f43c1349ccb], spanSampled=[0]}, mqtt_duplicate=false, id=9ba2ce79-7077-a079-9851-a9e2724d167e, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:34:40.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:34:40.390Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:34:40.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756480071\",\"timestamp\":1753756480071,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:34:40.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:34:40.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:40.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:34:40.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:34:40.39(Timestamp), 2025-07-29 10:34:40.39(Timestamp)"}
{"@timestamp":"2025-07-29T02:34:40.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:40.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:34:40.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:34:40.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:40.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:34:40.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:34:40.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:40.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:34:40.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:40.418(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:34:40.442Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:40.442Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:34:40.442Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:40.426(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:34:40.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:55.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"ecb8580b2e8e8167","span":"deb6c862f22af967","parent":"ea44d4ccfe288eb5","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495066\",\"timestamp\":1753756495066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=ecb8580b2e8e8167, spanId=ecb8580b2e8e8167, nativeHeaders={spanTraceId=[ecb8580b2e8e8167], spanId=[ecb8580b2e8e8167], spanSampled=[0]}, mqtt_duplicate=false, id=d642b9fb-bf5c-613d-2b47-1a2a44bf6c39, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:34:55.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:34:55.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495066\",\"timestamp\":1753756495066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:34:55.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:34:55.399Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:34:55.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:34:55.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:34:55.399(Timestamp), 2025-07-29 10:34:55.399(Timestamp)"}
{"@timestamp":"2025-07-29T02:34:55.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"fa56615ed5e5641a","span":"873da8a09512a6ab","parent":"d9974ed3362b4dd3","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-6","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495072\",\"timestamp\":1753756495072,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=fa56615ed5e5641a, spanId=fa56615ed5e5641a, nativeHeaders={spanTraceId=[fa56615ed5e5641a], spanId=[fa56615ed5e5641a], spanSampled=[0]}, mqtt_duplicate=false, id=5dbaa5c4-69b7-d3bb-ba03-4d5e3171a305, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:34:55.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:34:55.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:34:55.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756495072\",\"timestamp\":1753756495072,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:34:55.415Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:34:55.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:34:55.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:55.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:34:55.415(Timestamp), 2025-07-29 10:34:55.415(Timestamp)"}
{"@timestamp":"2025-07-29T02:34:55.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:34:55.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:34:55.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:55.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:34:55.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:55.424(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:34:55.432Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:55.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:34:55.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:34:55.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:34:55.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:34:55.442Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:34:55.444Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:34:55.441(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:34:55.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-29T02:35:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-29T02:35:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-29T02:35:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-29T02:35:00.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-29T02:35:00.031Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-29T02:35:00.031Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：12毫秒"}
{"@timestamp":"2025-07-29T02:35:00.033Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:00.033Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072910350002269592420511413249(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 12(Integer), 2025-07-29 10:35:00.019(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:00.043Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：24毫秒"}
{"@timestamp":"2025-07-29T02:35:00.044Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:00.045Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072910350002269592420511413248(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 24(Integer), 2025-07-29 10:35:00.019(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:00.606Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-29T02:35:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"883de860c5e4714d","span":"7c7edf5fb81ac10d","parent":"f1ec1f3c71819ded","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-7","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510066\",\"timestamp\":1753756510066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=883de860c5e4714d, spanId=883de860c5e4714d, nativeHeaders={spanTraceId=[883de860c5e4714d], spanId=[883de860c5e4714d], spanSampled=[0]}, mqtt_duplicate=false, id=4441b3a6-b8a9-e8ca-e1b6-7935dc1640ca, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510066\",\"timestamp\":1753756510066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:10.381Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:10.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:10.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:10.381(Timestamp), 2025-07-29 10:35:10.381(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:10.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:10.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:10.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"INFO","service":"eeip-standalone-service","trace":"31d3b33c0bbd6c55","span":"af16ab2445763999","parent":"339be59ea0b43148","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-8","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510076\",\"timestamp\":1753756510076,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=31d3b33c0bbd6c55, spanId=31d3b33c0bbd6c55, nativeHeaders={spanTraceId=[31d3b33c0bbd6c55], spanId=[31d3b33c0bbd6c55], spanSampled=[0]}, mqtt_duplicate=false, id=be86ce1d-6fa6-a797-23e2-f0bbfc32fe14, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756510076\",\"timestamp\":1753756510076,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:10.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:10.421(Timestamp), 2025-07-29 10:35:10.421(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:10.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:10.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:10.421(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:10.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:10.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:10.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:10.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:10.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:10.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:10.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:10.446(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:10.461Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:14.638Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) "}
{"@timestamp":"2025-07-29T02:35:14.639Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==> Parameters: 2025-07-29 10:35:14(String)"}
{"@timestamp":"2025-07-29T02:35:14.652Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-29T02:35:25.353Z","severity":"INFO","service":"eeip-standalone-service","trace":"f63bee15d56496d9","span":"42d13f1423adb9b2","parent":"2af0c5bff85c9886","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-9","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525068\",\"timestamp\":1753756525068,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=f63bee15d56496d9, spanId=f63bee15d56496d9, nativeHeaders={spanTraceId=[f63bee15d56496d9], spanId=[f63bee15d56496d9], spanSampled=[0]}, mqtt_duplicate=false, id=b7af9b67-a602-f41b-b5dd-587efbff74db, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:25.353Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:25.353Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:25.353Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525068\",\"timestamp\":1753756525068,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:25.353Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:25.353Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:25.353Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:25.353(Timestamp), 2025-07-29 10:35:25.353(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:25.369Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:25.369Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:25.369Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:25.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:25.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:25.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:25.4(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:25.417Z","severity":"INFO","service":"eeip-standalone-service","trace":"2251f70f4e74d115","span":"db0f2e0a0594dd50","parent":"b36f5a53f859c94d","exportable":"true","pid":"24360","thread":"ThreadPoolTaskExecutor-10","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525080\",\"timestamp\":1753756525080,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=2251f70f4e74d115, spanId=2251f70f4e74d115, nativeHeaders={spanTraceId=[2251f70f4e74d115], spanId=[2251f70f4e74d115], spanSampled=[1]}, mqtt_duplicate=false, id=5eace3d9-866f-a0d5-0cee-5d3aa9c15a37, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:25.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:25.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:25.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756525080\",\"timestamp\":1753756525080,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:25.418Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:25.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:25.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:25.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:25.418(Timestamp), 2025-07-29 10:35:25.418(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:25.432Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:25.432Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:25.432Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:25.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:25.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:25.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:25.44(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:25.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:40.440Z","severity":"INFO","service":"eeip-standalone-service","trace":"965aa817badce62c","span":"b8a2927e8ce38865","parent":"491eeb510555464f","exportable":"true","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540070\",\"timestamp\":1753756540070,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=965aa817badce62c, spanId=965aa817badce62c, nativeHeaders={spanTraceId=[965aa817badce62c], spanId=[965aa817badce62c], spanSampled=[1]}, mqtt_duplicate=false, id=1ed2ef82-793a-2203-de5e-4a5617523237, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:40.440Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:40.440Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540070\",\"timestamp\":1753756540070,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:40.440Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:40.440Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:40.442Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:40.442Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:40.442(Timestamp), 2025-07-29 10:35:40.442(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:40.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:40.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:40.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:40.462Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:40.462Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:40.466Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:40.462(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:40.478Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:40.491Z","severity":"INFO","service":"eeip-standalone-service","trace":"784d7c5d7bce83b5","span":"e89b34b947f16f56","parent":"d541b359382c186a","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540144\",\"timestamp\":1753756540144,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=784d7c5d7bce83b5, spanId=784d7c5d7bce83b5, nativeHeaders={spanTraceId=[784d7c5d7bce83b5], spanId=[784d7c5d7bce83b5], spanSampled=[0]}, mqtt_duplicate=false, id=a6ab6d2b-27dd-70de-1ed3-968d97319dbf, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:40.491Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:40.491Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:40.491Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756540144\",\"timestamp\":1753756540144,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:40.491Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:40.493Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:40.493Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:40.491(Timestamp), 2025-07-29 10:35:40.491(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:40.510Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:40.510Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:40.510Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:40.516Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:40.518Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:40.518Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:40.516(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:40.533Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:55.354Z","severity":"INFO","service":"eeip-standalone-service","trace":"94971c7c09d5c394","span":"40929835fda8f23d","parent":"321abbdafa390879","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555066\",\"timestamp\":1753756555066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=94971c7c09d5c394, spanId=94971c7c09d5c394, nativeHeaders={spanTraceId=[94971c7c09d5c394], spanId=[94971c7c09d5c394], spanSampled=[0]}, mqtt_duplicate=false, id=e2c8e6d8-7a8d-e98e-0395-2e22b89651a5, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:55.354Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:55.354Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:55.354Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555066\",\"timestamp\":1753756555066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:55.354Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:55.356Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:55.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:35:55.356(Timestamp), 2025-07-29 10:35:55.356(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:55.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:55.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:55.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"066550fbb06eb0e3","span":"962c459697620e1c","parent":"e2f01513dc29a55d","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555080\",\"timestamp\":1753756555080,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=066550fbb06eb0e3, spanId=066550fbb06eb0e3, nativeHeaders={spanTraceId=[066550fbb06eb0e3], spanId=[066550fbb06eb0e3], spanSampled=[0]}, mqtt_duplicate=false, id=ff8ecc1d-9bcd-369b-01e6-67274d7eb470, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756555080\",\"timestamp\":1753756555080,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:55.378Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:55.378(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:55.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:35:55.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:35:55.378(Timestamp), 2025-07-29 10:35:55.378(Timestamp)"}
{"@timestamp":"2025-07-29T02:35:55.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:35:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:35:55.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:35:55.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:35:55.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:35:55.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:35:55.403(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:35:55.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:10.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"e879b01906a0ec17","span":"e45f3ca733a74893","parent":"80874a0649696da9","exportable":"true","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570069\",\"timestamp\":1753756570069,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=e879b01906a0ec17, spanId=e879b01906a0ec17, nativeHeaders={spanTraceId=[e879b01906a0ec17], spanId=[e879b01906a0ec17], spanSampled=[1]}, mqtt_duplicate=false, id=6a0818c4-f2fb-3be4-cd4e-b0bb65bb177c, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:10.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:10.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:10.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570069\",\"timestamp\":1753756570069,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:10.356Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:10.356Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:10.356Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:10.356(Timestamp), 2025-07-29 10:36:10.356(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:10.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:10.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:10.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"7e13d908eb029c9c","span":"ce91ccb39ad93d4c","parent":"f818e0a24abcbc67","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-6","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570083\",\"timestamp\":1753756570083,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=7e13d908eb029c9c, spanId=7e13d908eb029c9c, nativeHeaders={spanTraceId=[7e13d908eb029c9c], spanId=[7e13d908eb029c9c], spanSampled=[0]}, mqtt_duplicate=false, id=d0b47d9f-28e3-0472-5def-ec4553079c5d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756570083\",\"timestamp\":1753756570083,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:10.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:10.381(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:10.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:10.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:10.381(Timestamp), 2025-07-29 10:36:10.381(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:10.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:10.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:10.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:10.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:10.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:10.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:10.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:10.407(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:10.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:25.352Z","severity":"INFO","service":"eeip-standalone-service","trace":"f388a15c19904fdb","span":"a0da49bc1f775326","parent":"f7132e24cacedbe0","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-7","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585066\",\"timestamp\":1753756585066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=f388a15c19904fdb, spanId=f388a15c19904fdb, nativeHeaders={spanTraceId=[f388a15c19904fdb], spanId=[f388a15c19904fdb], spanSampled=[0]}, mqtt_duplicate=false, id=1e593594-274a-b99b-e4ce-8e0ba711b7cd, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:25.352Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:25.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:25.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585066\",\"timestamp\":1753756585066,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:25.356Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:25.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:25.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:25.356(Timestamp), 2025-07-29 10:36:25.356(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:25.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:25.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:25.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:25.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:25.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:25.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"7175d7a1c9c3d3d8","span":"1400af579790e0d3","parent":"837af88724d9f573","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-8","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585076\",\"timestamp\":1753756585076,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=7175d7a1c9c3d3d8, spanId=7175d7a1c9c3d3d8, nativeHeaders={spanTraceId=[7175d7a1c9c3d3d8], spanId=[7175d7a1c9c3d3d8], spanSampled=[0]}, mqtt_duplicate=false, id=81cf551f-1884-9453-d92b-5bb432905d7b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:25.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:25.38(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:25.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:25.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:25.381Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:25.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756585076\",\"timestamp\":1753756585076,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:25.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:25.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:25.382(Timestamp), 2025-07-29 10:36:25.382(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:25.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:25.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:25.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:25.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:25.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:25.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:25.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:25.405(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:25.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:40.387Z","severity":"INFO","service":"eeip-standalone-service","trace":"4c30ec0a407e42be","span":"a03c636fc76f46a2","parent":"5438bd1f5036c2a7","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-9","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600106\",\"timestamp\":1753756600106,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=4c30ec0a407e42be, spanId=4c30ec0a407e42be, nativeHeaders={spanTraceId=[4c30ec0a407e42be], spanId=[4c30ec0a407e42be], spanSampled=[0]}, mqtt_duplicate=false, id=67d7c4ed-6300-e19f-f0cb-e790064fd17e, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600106\",\"timestamp\":1753756600106,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:40.388Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:40.388Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:40.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:40.388(Timestamp), 2025-07-29 10:36:40.388(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:40.395Z","severity":"INFO","service":"eeip-standalone-service","trace":"80563cc2fb6e81b5","span":"97f8b59ed98a483e","parent":"7345b7108b4a3a16","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-10","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600112\",\"timestamp\":1753756600112,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=80563cc2fb6e81b5, spanId=80563cc2fb6e81b5, nativeHeaders={spanTraceId=[80563cc2fb6e81b5], spanId=[80563cc2fb6e81b5], spanSampled=[0]}, mqtt_duplicate=false, id=7be5ba4e-b1da-f789-1e4c-cbb87240135c, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:40.395Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:40.395Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756600112\",\"timestamp\":1753756600112,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:40.395Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:40.395Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:40.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:40.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:40.395(Timestamp), 2025-07-29 10:36:40.395(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:40.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:40.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:40.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:40.411Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:40.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:40.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:40.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:40.412(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:40.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:40.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:40.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:40.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:40.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:40.421(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:40.428Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:40.435Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:55.372Z","severity":"INFO","service":"eeip-standalone-service","trace":"e9629a0a916d11aa","span":"53625b5290b36134","parent":"8149bf063eabd80a","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615095\",\"timestamp\":1753756615095,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=e9629a0a916d11aa, spanId=e9629a0a916d11aa, nativeHeaders={spanTraceId=[e9629a0a916d11aa], spanId=[e9629a0a916d11aa], spanSampled=[0]}, mqtt_duplicate=false, id=abf3da8e-ec6a-440c-9970-8155098df9c5, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:55.373Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:55.373Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:55.373Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615095\",\"timestamp\":1753756615095,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:55.373Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:55.374Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:55.374Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:36:55.374(Timestamp), 2025-07-29 10:36:55.374(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:55.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:55.398Z","severity":"INFO","service":"eeip-standalone-service","trace":"2c3fd11346f0fd25","span":"5f326d06863e86c6","parent":"1ce979fbc358561a","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615101\",\"timestamp\":1753756615101,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=2c3fd11346f0fd25, spanId=2c3fd11346f0fd25, nativeHeaders={spanTraceId=[2c3fd11346f0fd25], spanId=[2c3fd11346f0fd25], spanSampled=[0]}, mqtt_duplicate=false, id=2551de88-3dd5-4bc8-d574-03af9aa3e992, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:36:55.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:36:55.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:36:55.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756615101\",\"timestamp\":1753756615101,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:36:55.399Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:36:55.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:36:55.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:36:55.399(Timestamp), 2025-07-29 10:36:55.399(Timestamp)"}
{"@timestamp":"2025-07-29T02:36:55.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:55.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:55.403(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:55.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:55.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:36:55.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:36:55.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:36:55.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:36:55.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:36:55.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:36:55.424(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:36:55.447Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"f8038242ce6a550e","span":"9cb7e29068d57018","parent":"041e645c3d083d4d","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630095\",\"timestamp\":1753756630095,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=f8038242ce6a550e, spanId=f8038242ce6a550e, nativeHeaders={spanTraceId=[f8038242ce6a550e], spanId=[f8038242ce6a550e], spanSampled=[0]}, mqtt_duplicate=false, id=5572d4f8-0c9a-b88d-865b-14cf38493694, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:10.381Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630095\",\"timestamp\":1753756630095,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:10.381Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:10.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:10.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:10.381(Timestamp), 2025-07-29 10:37:10.381(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:10.393Z","severity":"INFO","service":"eeip-standalone-service","trace":"e11a962f82935d21","span":"eb87832ada852ab0","parent":"2f7ee8da2b4f7a81","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630103\",\"timestamp\":1753756630103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=e11a962f82935d21, spanId=e11a962f82935d21, nativeHeaders={spanTraceId=[e11a962f82935d21], spanId=[e11a962f82935d21], spanSampled=[0]}, mqtt_duplicate=false, id=e7c0f65e-0226-6afb-a33e-41d033a92141, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:10.394Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:10.394Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756630103\",\"timestamp\":1753756630103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:10.394Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:10.394Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:10.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:10.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:10.394(Timestamp), 2025-07-29 10:37:10.394(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:10.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:10.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:10.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:10.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:10.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:10.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:10.405(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:10.410Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:10.411Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:10.411Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:10.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:10.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:10.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:10.417(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:10.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:10.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:25.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"93adf5e8753a7a0e","span":"570e8ecd8fa0ceff","parent":"322da8057f0a768b","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645096\",\"timestamp\":1753756645096,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=93adf5e8753a7a0e, spanId=93adf5e8753a7a0e, nativeHeaders={spanTraceId=[93adf5e8753a7a0e], spanId=[93adf5e8753a7a0e], spanSampled=[0]}, mqtt_duplicate=false, id=ca6b107f-0962-88c3-619f-952d1c658366, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645096\",\"timestamp\":1753756645096,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:25.378Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:25.379Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:25.379Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:25.378(Timestamp), 2025-07-29 10:37:25.378(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:25.391Z","severity":"INFO","service":"eeip-standalone-service","trace":"972e20a2dd15815e","span":"c396817604c1ac56","parent":"8193a9016e6586e1","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-6","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645102\",\"timestamp\":1753756645102,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=972e20a2dd15815e, spanId=972e20a2dd15815e, nativeHeaders={spanTraceId=[972e20a2dd15815e], spanId=[972e20a2dd15815e], spanSampled=[0]}, mqtt_duplicate=false, id=2a377ac9-4a43-9b5b-33b6-03a8c4a4124c, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:25.391Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:25.391Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:25.391Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:25.391Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756645102\",\"timestamp\":1753756645102,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:25.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:25.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:25.391(Timestamp), 2025-07-29 10:37:25.391(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:25.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:25.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:25.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:25.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:25.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:25.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:25.401(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:25.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:25.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:25.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:25.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:25.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:25.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:25.415(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:25.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:25.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"99b6efcbf526ec2c","span":"c6cb611fb24fbab7","parent":"89c634c27f9a5b92","exportable":"true","pid":"24360","thread":"ThreadPoolTaskExecutor-7","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660091\",\"timestamp\":1753756660092,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=99b6efcbf526ec2c, spanId=99b6efcbf526ec2c, nativeHeaders={spanTraceId=[99b6efcbf526ec2c], spanId=[99b6efcbf526ec2c], spanSampled=[1]}, mqtt_duplicate=false, id=744caa3a-2d5b-a621-d0c2-80963d265400, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660091\",\"timestamp\":1753756660092,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:40.377Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:40.378Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:40.378Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:40.377(Timestamp), 2025-07-29 10:37:40.377(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:40.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"e2d6a07f53515462","span":"b8dba173a418b837","parent":"ea43eed6f3d0d3ff","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-8","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660100\",\"timestamp\":1753756660100,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=e2d6a07f53515462, spanId=e2d6a07f53515462, nativeHeaders={spanTraceId=[e2d6a07f53515462], spanId=[e2d6a07f53515462], spanSampled=[0]}, mqtt_duplicate=false, id=550a587e-5c67-483f-2c5c-d5b5d64d2cbc, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:40.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:40.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:40.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756660100\",\"timestamp\":1753756660100,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:40.390Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:40.391Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:40.391Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:40.391(Timestamp), 2025-07-29 10:37:40.391(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:40.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:40.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:40.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:40.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:40.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:40.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:40.401(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:40.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:40.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:40.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:40.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:40.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:40.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:40.414(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:40.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:40.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:55.396Z","severity":"INFO","service":"eeip-standalone-service","trace":"17c45ebb7ebdfb90","span":"5a26b52da0fd3bf6","parent":"8af2db704b8b4fb8","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-9","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675111\",\"timestamp\":1753756675111,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=17c45ebb7ebdfb90, spanId=17c45ebb7ebdfb90, nativeHeaders={spanTraceId=[17c45ebb7ebdfb90], spanId=[17c45ebb7ebdfb90], spanSampled=[0]}, mqtt_duplicate=false, id=bad43363-bfe1-ded3-7cc0-21cd2236158f, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:55.396Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:55.396Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675111\",\"timestamp\":1753756675111,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:55.396Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:55.396Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:55.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:55.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:37:55.396(Timestamp), 2025-07-29 10:37:55.396(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:55.409Z","severity":"INFO","service":"eeip-standalone-service","trace":"ffcdfaeee05665b0","span":"ab34309ce3957ba3","parent":"6a099963e5896867","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-10","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675121\",\"timestamp\":1753756675121,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=ffcdfaeee05665b0, spanId=ffcdfaeee05665b0, nativeHeaders={spanTraceId=[ffcdfaeee05665b0], spanId=[ffcdfaeee05665b0], spanSampled=[0]}, mqtt_duplicate=false, id=d0e432ec-8090-ad2e-3525-16bcf1bff8c4, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:37:55.409Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:37:55.409Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:37:55.409Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756675121\",\"timestamp\":1753756675121,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:37:55.410Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:37:55.410Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:37:55.410Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:37:55.41(Timestamp), 2025-07-29 10:37:55.41(Timestamp)"}
{"@timestamp":"2025-07-29T02:37:55.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:55.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:55.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:55.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:55.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:55.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:55.42(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:55.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:55.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:37:55.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:37:55.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:37:55.434Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:37:55.434Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:37:55.434(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:37:55.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:37:55.452Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:10.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"c9689aa54d1e9758","span":"f7da6526bb4a415b","parent":"df607730ebf6c5ba","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690103\",\"timestamp\":1753756690103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=c9689aa54d1e9758, spanId=c9689aa54d1e9758, nativeHeaders={spanTraceId=[c9689aa54d1e9758], spanId=[c9689aa54d1e9758], spanSampled=[0]}, mqtt_duplicate=false, id=04196f8c-0716-0a22-0d07-dfc754a2aeb6, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:10.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690103\",\"timestamp\":1753756690103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:10.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:10.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:10.389Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:10.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:10.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:10.389(Timestamp), 2025-07-29 10:38:10.389(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:10.400Z","severity":"INFO","service":"eeip-standalone-service","trace":"a6e0f396461d4146","span":"0c9e089a080ee6dc","parent":"1b481f8af4439abb","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690113\",\"timestamp\":1753756690113,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=a6e0f396461d4146, spanId=a6e0f396461d4146, nativeHeaders={spanTraceId=[a6e0f396461d4146], spanId=[a6e0f396461d4146], spanSampled=[0]}, mqtt_duplicate=false, id=04c59a5a-fe80-8bb8-f779-c53b9c7a1ef6, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:10.400Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756690113\",\"timestamp\":1753756690113,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:10.400Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:10.400Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:10.400Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:10.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:10.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:10.401(Timestamp), 2025-07-29 10:38:10.401(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:10.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:10.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:10.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:10.411Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:10.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:10.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:10.411(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:10.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:10.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:10.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:10.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:10.424Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:10.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:10.424(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:10.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:10.438Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"1b2951f30da215e7","span":"a54c87914dea8966","parent":"35c9ebac2fb8bea9","exportable":"true","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705091\",\"timestamp\":1753756705091,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=1b2951f30da215e7, spanId=1b2951f30da215e7, nativeHeaders={spanTraceId=[1b2951f30da215e7], spanId=[1b2951f30da215e7], spanSampled=[1]}, mqtt_duplicate=false, id=bb810849-4ddc-3dd5-2a79-83b25adc0507, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705091\",\"timestamp\":1753756705091,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:25.378Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:25.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:25.379Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:25.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:25.379(Timestamp), 2025-07-29 10:38:25.379(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:25.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"4f5b9c71f21bb8bc","span":"09690b90ec09c9cf","parent":"32365b42851cab81","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705103\",\"timestamp\":1753756705103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=4f5b9c71f21bb8bc, spanId=4f5b9c71f21bb8bc, nativeHeaders={spanTraceId=[4f5b9c71f21bb8bc], spanId=[4f5b9c71f21bb8bc], spanSampled=[0]}, mqtt_duplicate=false, id=58fd46af-1dbf-d987-1a37-4c9d5f616215, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:25.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:25.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:25.390Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756705103\",\"timestamp\":1753756705103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:25.391Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:25.391Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:25.391Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:25.391(Timestamp), 2025-07-29 10:38:25.391(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:25.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:25.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:25.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:25.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:25.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:25.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:25.402(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:25.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:25.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:25.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:25.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:25.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:25.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:25.414(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:25.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:25.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:40.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"6258e45834f527b9","span":"e525c389b7970e6d","parent":"08ecc37b94906560","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720096\",\"timestamp\":1753756720096,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=6258e45834f527b9, spanId=6258e45834f527b9, nativeHeaders={spanTraceId=[6258e45834f527b9], spanId=[6258e45834f527b9], spanSampled=[0]}, mqtt_duplicate=false, id=46cb50b2-d9dc-ff1d-7d6d-88ddbdb97f5a, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:40.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:40.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:40.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720096\",\"timestamp\":1753756720096,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:40.379Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:40.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:40.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:40.38(Timestamp), 2025-07-29 10:38:40.38(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:40.393Z","severity":"INFO","service":"eeip-standalone-service","trace":"7ee201979e0f7119","span":"95809e0403e15e52","parent":"d8d5c7020edb0e11","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-6","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720104\",\"timestamp\":1753756720104,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=7ee201979e0f7119, spanId=7ee201979e0f7119, nativeHeaders={spanTraceId=[7ee201979e0f7119], spanId=[7ee201979e0f7119], spanSampled=[0]}, mqtt_duplicate=false, id=34345058-2895-783a-46ad-4686c4bba921, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:40.394Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:40.394Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756720104\",\"timestamp\":1753756720104,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:40.394Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:40.394Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:40.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:40.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:40.394(Timestamp), 2025-07-29 10:38:40.394(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:40.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:40.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:40.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:40.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:40.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:40.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:40.404(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:40.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:40.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:40.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:40.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:40.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:40.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:40.416(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:40.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:40.432Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:55.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"e072185558b31f70","span":"ca82ccb8ffd0c46f","parent":"b44ea1e87f6d9384","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-7","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735094\",\"timestamp\":1753756735094,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=e072185558b31f70, spanId=e072185558b31f70, nativeHeaders={spanTraceId=[e072185558b31f70], spanId=[e072185558b31f70], spanSampled=[0]}, mqtt_duplicate=false, id=0d06f754-f383-777b-acae-5f1a0474829d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:55.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:55.379Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:55.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:55.379Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735094\",\"timestamp\":1753756735094,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:55.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:55.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:38:55.38(Timestamp), 2025-07-29 10:38:55.38(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:55.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:55.397Z","severity":"INFO","service":"eeip-standalone-service","trace":"f9da7cf538e171ea","span":"d1a7ea2e56de1aaa","parent":"eeb07b3b93582abb","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-8","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735099\",\"timestamp\":1753756735099,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=f9da7cf538e171ea, spanId=f9da7cf538e171ea, nativeHeaders={spanTraceId=[f9da7cf538e171ea], spanId=[f9da7cf538e171ea], spanSampled=[0]}, mqtt_duplicate=false, id=00646adb-6411-e378-23a1-4edc5035fcfe, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:38:55.397Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:38:55.397Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756735099\",\"timestamp\":1753756735099,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:38:55.397Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:38:55.397Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:38:55.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:38:55.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:38:55.397(Timestamp), 2025-07-29 10:38:55.397(Timestamp)"}
{"@timestamp":"2025-07-29T02:38:55.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:55.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:55.403(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:55.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:55.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:38:55.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:38:55.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:38:55.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:38:55.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:38:55.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:38:55.425(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:38:55.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:10.375Z","severity":"INFO","service":"eeip-standalone-service","trace":"399918f71a991017","span":"1284c53314950ae6","parent":"43f4bd8fc35aa939","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-9","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750088\",\"timestamp\":1753756750088,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=399918f71a991017, spanId=399918f71a991017, nativeHeaders={spanTraceId=[399918f71a991017], spanId=[399918f71a991017], spanSampled=[0]}, mqtt_duplicate=false, id=7996c202-e301-a023-607b-bd04b8a8f77e, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:10.375Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:10.375Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:10.375Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750088\",\"timestamp\":1753756750088,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:10.375Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:10.375Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:10.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:10.375(Timestamp), 2025-07-29 10:39:10.375(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:10.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"ee8cacb6272f1036","span":"0be89e413b469bfe","parent":"dc9e8c880ca433ac","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-10","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750094\",\"timestamp\":1753756750094,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=ee8cacb6272f1036, spanId=ee8cacb6272f1036, nativeHeaders={spanTraceId=[ee8cacb6272f1036], spanId=[ee8cacb6272f1036], spanSampled=[0]}, mqtt_duplicate=false, id=25db1e8a-1a06-e1ce-584b-c5f48edd854b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:10.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756750094\",\"timestamp\":1753756750094,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:10.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:10.389Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:10.389Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:10.390Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:10.390Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:10.39(Timestamp), 2025-07-29 10:39:10.39(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:10.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:10.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:10.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:10.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:10.399Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:10.399Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:10.398(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:10.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:10.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:10.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:10.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:10.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:10.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:10.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:10.414(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:10.428Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:25.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"889db82b60b78b54","span":"825ede94aad86063","parent":"59685b4167ddba45","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765097\",\"timestamp\":1753756765097,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=889db82b60b78b54, spanId=889db82b60b78b54, nativeHeaders={spanTraceId=[889db82b60b78b54], spanId=[889db82b60b78b54], spanSampled=[0]}, mqtt_duplicate=false, id=f1344d09-f14a-7a6c-4186-e2b86159ce89, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:25.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765097\",\"timestamp\":1753756765097,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:25.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:25.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:25.377Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:25.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:25.378Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:25.377(Timestamp), 2025-07-29 10:39:25.377(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:25.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:25.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:25.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:25.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"973e93ca333cd22b","span":"f2656246f7d87c10","parent":"9ff80fcf74169848","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765105\",\"timestamp\":1753756765105,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=973e93ca333cd22b, spanId=973e93ca333cd22b, nativeHeaders={spanTraceId=[973e93ca333cd22b], spanId=[973e93ca333cd22b], spanSampled=[0]}, mqtt_duplicate=false, id=0a3c06db-9209-39c5-8e58-9d71032899dd, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:25.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:25.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:25.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756765105\",\"timestamp\":1753756765105,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:25.406Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:25.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:25.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:25.406(Timestamp), 2025-07-29 10:39:25.406(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:25.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:25.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:25.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:25.415(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:25.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:25.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:25.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:25.438Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:25.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:25.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:25.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:25.44(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:25.459Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"e0946ee3bc231f75","span":"f2eecf2cd0b44e02","parent":"954a9a45ec94ef45","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780094\",\"timestamp\":1753756780094,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=e0946ee3bc231f75, spanId=e0946ee3bc231f75, nativeHeaders={spanTraceId=[e0946ee3bc231f75], spanId=[e0946ee3bc231f75], spanSampled=[0]}, mqtt_duplicate=false, id=ddedbc4d-6517-3fb6-f6f5-e57686eb7c27, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780094\",\"timestamp\":1753756780094,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:40.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:40.377Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:40.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:40.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:40.377(Timestamp), 2025-07-29 10:39:40.377(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:40.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"8f37492d7feb0d35","span":"7280fe9d4c76efe9","parent":"df9ece5cec8927de","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780103\",\"timestamp\":1753756780103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=8f37492d7feb0d35, spanId=8f37492d7feb0d35, nativeHeaders={spanTraceId=[8f37492d7feb0d35], spanId=[8f37492d7feb0d35], spanSampled=[0]}, mqtt_duplicate=false, id=187d02e8-1476-62bc-6de9-244b3aecb06a, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:40.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:40.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756780103\",\"timestamp\":1753756780103,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:40.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:40.386Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:40.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:40.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:40.386(Timestamp), 2025-07-29 10:39:40.386(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:40.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:40.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:40.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:40.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:40.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:40.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:40.401(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:40.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:40.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:40.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:40.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:40.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:40.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:40.413(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:40.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:40.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:54.810Z","severity":"INFO","service":"eeip-standalone-service","trace":"70b24ba175f27566","span":"7b72f414adc70ade","parent":"6f3f23c44d583034","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"devType\":\"175\",\"jxxx_id\":\"134\",\"device_id\":\"*********\",\"getPkgMode\":\"2\",\"csbh\":\"003\",\"file_downdload_status_list\":[{\"type\":\"jkjx\",\"version\":\"1\",\"status\":\"1\"}],\"task_id\":\"2507291039541399702973768007680\"},\"sign\":\"\",\"messageId\":\"downfile_1753756794008_*********\",\"timestamp\":1753756794008,\"token\":\"9140d3d9e3cb44678a50c0a2ab57a255\"}, headers={mqtt_receivedRetained=false, spanTraceId=70b24ba175f27566, spanId=70b24ba175f27566, nativeHeaders={spanTraceId=[70b24ba175f27566], spanId=[70b24ba175f27566], spanSampled=[0]}, mqtt_duplicate=false, id=a6217ecd-88e3-9056-2965-ffb2b028830b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:54.810Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:54.810Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:54.810Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:54.827Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理开始"}
{"@timestamp":"2025-07-29T02:39:54.836Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:54.836Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"==> Parameters: 2025-07-29 10:39:54.836(Timestamp), 1(Integer), 10(Integer), 134(String), 003(String)"}
{"@timestamp":"2025-07-29T02:39:54.868Z","severity":"INFO","service":"eeip-standalone-service","trace":"8fbfe40841956e31","span":"858d378e65d135cc","parent":"d7384f838357ac35","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-6","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"devType\":\"175\",\"jxxx_id\":\"134\",\"device_id\":\"*********\",\"getPkgMode\":\"2\",\"csbh\":\"2011201\",\"file_downdload_status_list\":[{\"type\":\"jkjx\",\"version\":\"1\",\"status\":\"1\"}],\"task_id\":\"2507291039541399702973768007680\"},\"sign\":\"\",\"messageId\":\"downfile_1753756794537_*********\",\"timestamp\":1753756794537,\"token\":\"fc58ac462df643ccb9ae391381aa10f2\"}, headers={mqtt_receivedRetained=false, spanTraceId=8fbfe40841956e31, spanId=8fbfe40841956e31, nativeHeaders={spanTraceId=[8fbfe40841956e31], spanId=[8fbfe40841956e31], spanSampled=[0]}, mqtt_duplicate=false, id=b5e87891-e969-3708-9d92-cf86b96572d7, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:54.868Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:54.868Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:54.868Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:54.869Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理开始"}
{"@timestamp":"2025-07-29T02:39:54.870Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.updateByExampleSelective","rest":"==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:54.870Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.updateByExampleSelective","rest":"==> Parameters: 2025-07-29 10:39:54.869(Timestamp), 1(Integer), 10(Integer), 134(String), 2011201(String)"}
{"@timestamp":"2025-07-29T02:39:54.918Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:54.918Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:54.920Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:54.920Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:54.920Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 2507291039541399702973768007680(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:54.920Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 2507291039541399702973768007680(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:54.929Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-29T02:39:54.930Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-29T02:39:54.931Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:54.931Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:54.931Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:54.931Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:54.940Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-29T02:39:54.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理结束"}
{"@timestamp":"2025-07-29T02:39:54.940Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-29T02:39:54.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理结束"}
{"@timestamp":"2025-07-29T02:39:55.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"056b0b84d75fe61b","span":"f9dd1917607abb07","parent":"2b11fc56a4f606e9","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-7","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"sign\":\"\",\"token\":\"a50c28b373e54fb09e04752e0a54a3e8\",\"data\":{\"device_id\":\"*********\",\"jxxx_id\":\"134\",\"file_downdload_status_list\":[{\"status\":\"1\",\"version\":\"1\",\"type\":\"jkjx\"}],\"getPkgMode\":\"2\",\"task_id\":\"2507291039541399702973768007680\",\"devType\":\"175\",\"csbh\":\"********\"},\"timestamp\":1753756794828,\"messageId\":\"downfile_1753756794828_*********\"}, headers={mqtt_receivedRetained=false, spanTraceId=056b0b84d75fe61b, spanId=056b0b84d75fe61b, nativeHeaders={spanTraceId=[056b0b84d75fe61b], spanId=[056b0b84d75fe61b], spanSampled=[0]}, mqtt_duplicate=false, id=b384fd03-a0ef-ad04-80e2-d012663eec87, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:55.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:55.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:55.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:55.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理开始"}
{"@timestamp":"2025-07-29T02:39:55.152Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.updateByExampleSelective","rest":"==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:55.152Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.updateByExampleSelective","rest":"==> Parameters: 2025-07-29 10:39:55.148(Timestamp), 1(Integer), 10(Integer), 134(String), ********(String)"}
{"@timestamp":"2025-07-29T02:39:55.168Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:55.168Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:55.168Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 2507291039541399702973768007680(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:55.177Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-29T02:39:55.177Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:55.177Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:55.186Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:55.186Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理结束"}
{"@timestamp":"2025-07-29T02:39:55.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"28641e75ec447df0","span":"fb8d0d506d9bfc91","parent":"c306f4530d5716a5","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-8","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795096\",\"timestamp\":1753756795096,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=28641e75ec447df0, spanId=28641e75ec447df0, nativeHeaders={spanTraceId=[28641e75ec447df0], spanId=[28641e75ec447df0], spanSampled=[0]}, mqtt_duplicate=false, id=4b1d8cc6-57c7-a6cc-c65c-001b0834afb0, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:55.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:55.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:55.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795096\",\"timestamp\":1753756795096,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:55.376Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:55.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:55.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:39:55.376(Timestamp), 2025-07-29 10:39:55.376(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:55.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"5349c07179292276","span":"67349798a0992d3f","parent":"a18f6159563670e9","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-9","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795105\",\"timestamp\":1753756795105,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=5349c07179292276, spanId=5349c07179292276, nativeHeaders={spanTraceId=[5349c07179292276], spanId=[5349c07179292276], spanSampled=[0]}, mqtt_duplicate=false, id=6c1f62b5-f63f-a246-a4f0-b0d731bed04a, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:55.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756795105\",\"timestamp\":1753756795105,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:39:55.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:55.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:55.386Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:39:55.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:39:55.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:39:55.386(Timestamp), 2025-07-29 10:39:55.386(Timestamp)"}
{"@timestamp":"2025-07-29T02:39:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:55.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:55.404(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:39:55.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:39:55.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:39:55.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:39:55.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:39:55.413(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:39:55.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:55.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:55.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"0dd7523f0e6dfe08","span":"6efa36876c19a735","parent":"549cd25ef5bae07d","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-10","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"sign\":\"\",\"token\":\"64784c0e435a497eb0bd277de3f7e1eb\",\"data\":{\"device_id\":\"*********\",\"jxxx_id\":\"134\",\"file_downdload_status_list\":[{\"status\":\"1\",\"version\":\"1\",\"type\":\"jkjx\"}],\"getPkgMode\":\"2\",\"task_id\":\"2507291039541399702973768007680\",\"devType\":\"175\",\"csbh\":\"K3307820091100011\"},\"timestamp\":1753756795406,\"messageId\":\"downfile_1753756795406_*********\"}, headers={mqtt_receivedRetained=false, spanTraceId=0dd7523f0e6dfe08, spanId=0dd7523f0e6dfe08, nativeHeaders={spanTraceId=[0dd7523f0e6dfe08], spanId=[0dd7523f0e6dfe08], spanSampled=[0]}, mqtt_duplicate=false, id=420d5672-a734-661b-8fc4-d37fb15a674d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_school_updated, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:39:55.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:55.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:39:55.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_school_updated] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:39:55.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理开始"}
{"@timestamp":"2025-07-29T02:39:55.635Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"==>  Preparing: UPDATE jx_distribute_status SET t_download_time = ?,jxinf_version = ?,jxinf_pkg_status = ? WHERE ( ( jx_task_id = ? and bzhkcbh = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:55.636Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"==> Parameters: 2025-07-29 10:39:55.631(Timestamp), 1(Integer), 10(Integer), 134(String), K3307820091100011(String)"}
{"@timestamp":"2025-07-29T02:39:55.652Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:55.652Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,dis_task_id,bzhkcbh,jsh,notice_result FROM jx_distribute_task_cs WHERE ( ( dis_task_id = ? and notice_result = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:55.652Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 2507291039541399702973768007680(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:55.660Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-29T02:39:55.660Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==>  Preparing: SELECT id,jx_task_id,bzhkcbh,jsh,t_notice_time,t_download_time,jxinf_version,jxinf_pkg_status FROM jx_distribute_status WHERE ( ( jx_task_id = ? and jsh in ( ? , ? , ? , ? ) and jxinf_pkg_status = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:55.660Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"==> Parameters: 134(String), 1022(String), 2011201(String), 2022105(String), 2022105002(String), 1(Integer)"}
{"@timestamp":"2025-07-29T02:39:55.673Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-29T02:39:55.675Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"==>  Preparing: UPDATE jx_distribute_task SET t_progress = ?,complete = ?,complete_time = ? WHERE ( ( id = ? ) ) "}
{"@timestamp":"2025-07-29T02:39:55.675Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"==> Parameters: 100.00(String), 10(Integer), 2025-07-29 10:39:55.673(Timestamp), 2507291039541399702973768007680(String)"}
{"@timestamp":"2025-07-29T02:39:55.690Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.m.J.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:39:55.690Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.service.impl.JxsjxzztEventHandler","rest":"教学数据下载状态上报处理结束"}
{"@timestamp":"2025-07-29T02:40:00.002Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110440401808639620095703040"}
{"@timestamp":"2025-07-29T02:40:00.002Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110111540401808639620095703040"}
{"@timestamp":"2025-07-29T02:40:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-29T02:40:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：2502130940211339531603881230336"}
{"@timestamp":"2025-07-29T02:40:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-29T02:40:00.007Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新"}
{"@timestamp":"2025-07-29T02:40:00.007Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"=======开始查询和更新设备在线状态========="}
{"@timestamp":"2025-07-29T02:40:00.007Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.007Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String)"}
{"@timestamp":"2025-07-29T02:40:00.012Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1, pageIndex=0)]"}
{"@timestamp":"2025-07-29T02:40:00.012Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-29T02:40:00.012Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-29T02:40:00.012Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：8毫秒"}
{"@timestamp":"2025-07-29T02:40:00.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:00.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072910400002269594936967984128(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 8(Integer), 2025-07-29 10:40:00.004(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 16"}
{"@timestamp":"2025-07-29T02:40:00.022Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-"}
{"@timestamp":"2025-07-29T02:40:00.063Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectByExample_COUNT","rest":"==>  Preparing: SELECT count(0) FROM sb_sbxx WHERE ((sczt = ? AND sblx IN (?, ?, ?, ?))) "}
{"@timestamp":"2025-07-29T02:40:00.063Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectByExample_COUNT","rest":"==> Parameters: 0(String), 170(String), 171(String), 172(String), 175(String)"}
{"@timestamp":"2025-07-29T02:40:00.068Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753756799777, result=JetlinksPageRespModel(pageIndex=0, pageSize=1, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807)]))"}
{"@timestamp":"2025-07-29T02:40:00.068Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1000, pageIndex=0)]"}
{"@timestamp":"2025-07-29T02:40:00.072Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectByExample_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:00.083Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/remote/access/device/listByDeviceNames]参数[DeviceStatusReqModel(deviceNames=[])]"}
{"@timestamp":"2025-07-29T02:40:00.100Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753756799809, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])"}
{"@timestamp":"2025-07-29T02:40:00.100Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753756799816, result=JetlinksPageRespModel(pageIndex=0, pageSize=1000, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807), JetlinksDeviceFirewareRespModel(id=00024720d16a, deviceName=36路办公室超脑, productId=HISOME-DX000, version=2025.05.24.2002), JetlinksDeviceFirewareRespModel(id=*********, deviceName=考场电子班牌, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=ATN240303000100YT55YBF, deviceName=ATN240303000100YT55YBF, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.3_test3), JetlinksDeviceFirewareRespModel(id=ddcdb3ac5810283f, deviceName=ddcdb3ac5810283f, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.8), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ027590, deviceName=柴行楼教学楼103_DENCGW829_C04MB10BDJ027590, productId=CENCGW100_C, version=2025.01.27), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ07548e, deviceName=柴行楼柴行楼zq002_DENCGW829_C04MB10BDJ07548e, productId=CENCGW100_C, version=2025.03.18), JetlinksDeviceFirewareRespModel(id=*********, deviceName=DI思源楼1201考务*********, productId=PROD-DZBP, version=1.3.3), JetlinksDeviceFirewareRespModel(id=*********, deviceName=DI思源楼1203考场*********, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=G04754401, deviceName=G04754401, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754402, deviceName=G04754402, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754403, deviceName=G04754403, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754404, deviceName=G04754404, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754405, deviceName=G04754405, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=*********, deviceName=致远楼致远楼2105_*********, productId=PROD-DZBP, version=1.5.6_c), JetlinksDeviceFirewareRespModel(id=G04754407, deviceName=G04754407, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754408, deviceName=G04754408, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754409, deviceName=G04754409, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G16733827, deviceName=志勤楼教学楼207_G16733827, productId=PROD-DZBP, version=1.1.5), JetlinksDeviceFirewareRespModel(id=*********, deviceName=电子班牌测试, productId=PROD-DZBP, version=1.3.3_c), JetlinksDeviceFirewareRespModel(id=KEP20240707001, deviceName=黄河交通学院_身份核验移动终端2, productId=HISOME_ANDROID, version=20241224-1122#2.1.8), JetlinksDeviceFirewareRespModel(id=PID388E2207007544, deviceName=像素设备, productId=PROD-DZBP, version=1.1.6)]))"}
{"@timestamp":"2025-07-29T02:40:00.105Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":""}
{"@timestamp":"2025-07-29T02:40:00.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.05.20.1807(String), 2025-07-29 10:40:00.1(Timestamp), (String), 0(Integer), 0(String), 00024700113e(String)"}
{"@timestamp":"2025-07-29T02:40:00.108Z","severity":"INFO","service":"eeip-standalone-service","trace":"fe096f00724fe4c7","span":"77955fa48de7b755","parent":"c5ca07d0b4f78ff2","exportable":"true","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"device_id\":\"*********\",\"run_mode\":\"teach\",\"TIMESTAMP\":1753756799805},\"sign\":\"\",\"messageId\":\"putDeviceRunMode_1753756799805_*********\",\"timestamp\":1753756799805,\"token\":\"fc58ac462df643ccb9ae391381aa10f2\"}, headers={mqtt_receivedRetained=false, spanTraceId=fe096f00724fe4c7, spanId=fe096f00724fe4c7, nativeHeaders={spanTraceId=[fe096f00724fe4c7], spanId=[fe096f00724fe4c7], spanSampled=[1]}, mqtt_duplicate=false, id=3a4e479e-d39c-795f-3d86-f435989dd804, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_device_run_mode, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:00.108Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:00.108Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"接收到班牌设备当前运行模式变更: {\"data\":{\"device_id\":\"*********\",\"run_mode\":\"teach\",\"TIMESTAMP\":1753756799805},\"sign\":\"\",\"messageId\":\"putDeviceRunMode_1753756799805_*********\",\"timestamp\":1753756799805,\"token\":\"fc58ac462df643ccb9ae391381aa10f2\"}"}
{"@timestamp":"2025-07-29T02:40:00.108Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_device_run_mode] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:00.108Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_device_run_mode]无法处理"}
{"@timestamp":"2025-07-29T02:40:00.110Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list"}
{"@timestamp":"2025-07-29T02:40:00.114Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"班牌设备当前运行模式变更信息: {\"device_id\":\"*********\",\"run_mode\":\"EDU\",\"tIMESTAMP\":\"2025-07-29 10:40:00\"}"}
{"@timestamp":"2025-07-29T02:40:00.121Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.121Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.121Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.05.24.2002(String), 2025-07-29 10:40:00.121(Timestamp), (String), 0(Integer), 0(String), 00024720d16a(String)"}
{"@timestamp":"2025-07-29T02:40:00.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.136Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.136Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6(String), 2025-07-29 10:40:00.135(Timestamp), (String), 0(Integer), 0(String), *********(String)"}
{"@timestamp":"2025-07-29T02:40:00.141Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=Wrapper(code=200, message=success, result={deviceList=[{deviceName=00024700113e, status=0}, {deviceName=00024720d16a, status=0}, {deviceName=1231241157, status=0}, {deviceName=*********, status=1}, {deviceName=ATN240303000100YT55YBF, status=0}, {deviceName=CENCGW100_S01MA00BBA000D1000E, status=0}, {deviceName=CENCGW100_SIVb770d1a6a1ec5b44, status=1}, {deviceName=cillum, status=0}, {deviceName=ddcdb3ac5810283f, status=0}, {deviceName=DENCGW829_C04MB10BDJ027590, status=1}, {deviceName=DENCGW829_C04MB10BDJ07548e, status=0}, {deviceName=*********, status=1}, {deviceName=*********, status=1}, {deviceName=*********, status=1}, {deviceName=G16733827, status=0}, {deviceName=*********, status=1}, {deviceName=ipsumadk, status=0}, {deviceName=KEP20240707001, status=1}, {deviceName=PID388E2207007544, status=0}, {deviceName=S30SZA2023140181, status=0}, {deviceName=test0012, status=0}, {deviceName=test01-xlh, status=0}, {deviceName=test091903, status=0}, {deviceName=testYdzd004, status=0}, {deviceName=testYdzdXlh001, status=0}, {deviceName=testYdzdXlh002, status=0}], totalRows=26})"}
{"@timestamp":"2025-07-29T02:40:00.141Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.m.JcxxDzbpkzMapper.updateYxmsByXlh","rest":"==>  Preparing: update jcxx_dzbpkz SET yxms = ?, xgsj = ? where xlh = ? "}
{"@timestamp":"2025-07-29T02:40:00.141Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.m.JcxxDzbpkzMapper.updateYxmsByXlh","rest":"==> Parameters: EDU(String), 2025-07-29 10:40:00(String), *********(String)"}
{"@timestamp":"2025-07-29T02:40:00.146Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/*********/properties/read, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/*********/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/*********/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/*********/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))"}
{"@timestamp":"2025-07-29T02:40:00.146Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：144毫秒"}
{"@timestamp":"2025-07-29T02:40:00.151Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.151Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:00.151Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072910400002269594936951206913(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 144(Integer), 2025-07-29 10:40:00.002(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:00.159Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.m.JcxxDzbpkzMapper.updateYxmsByXlh","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.166Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,zhzxsj = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? ) ) ) "}
{"@timestamp":"2025-07-29T02:40:00.166Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1(String), 2025-07-29 10:40:00.146(Timestamp), 0(Integer), *********(String), CENCGW100_SIVb770d1a6a1ec5b44(String), DENCGW829_C04MB10BDJ027590(String), *********(String), *********(String), *********(String), *********(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-29T02:40:00.187Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.187Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.3_test3(String), 2025-07-29 10:40:00.151(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ATN240303000100YT55YBF(String)"}
{"@timestamp":"2025-07-29T02:40:00.190Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 40"}
{"@timestamp":"2025-07-29T02:40:00.190Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) ) ) "}
{"@timestamp":"2025-07-29T02:40:00.190Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 0(String), 0(Integer), 00024700113e(String), 00024720d16a(String), 1231241157(String), ATN240303000100YT55YBF(String), CENCGW100_S01MA00BBA000D1000E(String), cillum(String), ddcdb3ac5810283f(String), DENCGW829_C04MB10BDJ07548e(String), G16733827(String), ipsumadk(String), PID388E2207007544(String), S30SZA2023140181(String), test0012(String), test01-xlh(String), test091903(String), testYdzd004(String), testYdzdXlh001(String), testYdzdXlh002(String)"}
{"@timestamp":"2025-07-29T02:40:00.203Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.203Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.203Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.8(String), 2025-07-29 10:40:00.203(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ddcdb3ac5810283f(String)"}
{"@timestamp":"2025-07-29T02:40:00.207Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 31"}
{"@timestamp":"2025-07-29T02:40:00.207Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"getCssbZxzt - 开始获取场所设备在线状态."}
{"@timestamp":"2025-07-29T02:40:00.216Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"==>  Preparing: SELECT count(0) FROM sb_sbxx sb LEFT JOIN sb_sbcsgx gx ON sb.sbxxbh = gx.sbbh WHERE sb.sczt = '0' AND sb.sblx = '171' AND gx.csbh IS NOT NULL AND sb.xlh IS NOT NULL "}
{"@timestamp":"2025-07-29T02:40:00.216Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"==> Parameters: "}
{"@timestamp":"2025-07-29T02:40:00.218Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.218Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.218Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:00.224Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.01.27(String), 2025-07-29 10:40:00.218(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ027590(String)"}
{"@timestamp":"2025-07-29T02:40:00.224Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"==>  Preparing: SELECT bh,ksjhbh,ccm,kmm,ccmc,kmmc,kssj,jssj,yxcdsj,kssbqksj,ksrcsj,zc,kspch,cjsj,xgsj,xn,xq,scztw FROM ks_kscc WHERE ( ( scztw = ? and kssj <= ? and jssj >= ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.224Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"==> Parameters: 0(String), 2025-07-29 10:40:00.224(Timestamp), 2025-07-29 10:40:00.224(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:00.231Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-29T02:40:00.231Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"getCssbZxzt - 获取场所设备在线状态. [OK]"}
{"@timestamp":"2025-07-29T02:40:00.231Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"========设备在线状态更新完成========="}
{"@timestamp":"2025-07-29T02:40:00.231Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110440401808639620095703040  总共耗时：229毫秒"}
{"@timestamp":"2025-07-29T02:40:00.231Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:00.231Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072910400002269594936951206912(String), 23110110440401808639620095703040(String), jobSbzxztGx(String), sbzxztCxAndGxTaskService(String), 0(Integer), 229(Integer), 2025-07-29 10:40:00.002(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:00.236Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.239Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.239Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.03.18(String), 2025-07-29 10:40:00.236(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ07548e(String)"}
{"@timestamp":"2025-07-29T02:40:00.256Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.256Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.256Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.3.3(String), 2025-07-29 10:40:00.256(Timestamp), (String), 0(Integer), 0(String), *********(String)"}
{"@timestamp":"2025-07-29T02:40:00.270Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.275Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.275Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6(String), 2025-07-29 10:40:00.27(Timestamp), (String), 0(Integer), 0(String), *********(String)"}
{"@timestamp":"2025-07-29T02:40:00.291Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.291Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.291Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.291(Timestamp), (String), 0(Integer), 0(String), G04754401(String)"}
{"@timestamp":"2025-07-29T02:40:00.305Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.305Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.306Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.305(Timestamp), (String), 0(Integer), 0(String), G04754402(String)"}
{"@timestamp":"2025-07-29T02:40:00.316Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.316Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.316Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.316(Timestamp), (String), 0(Integer), 0(String), G04754403(String)"}
{"@timestamp":"2025-07-29T02:40:00.332Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.332Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.332Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.332(Timestamp), (String), 0(Integer), 0(String), G04754404(String)"}
{"@timestamp":"2025-07-29T02:40:00.346Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.346Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.346Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.346(Timestamp), (String), 0(Integer), 0(String), G04754405(String)"}
{"@timestamp":"2025-07-29T02:40:00.361Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.361Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.361Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6_c(String), 2025-07-29 10:40:00.361(Timestamp), (String), 0(Integer), 0(String), *********(String)"}
{"@timestamp":"2025-07-29T02:40:00.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.377(Timestamp), (String), 0(Integer), 0(String), G04754407(String)"}
{"@timestamp":"2025-07-29T02:40:00.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.392(Timestamp), (String), 0(Integer), 0(String), G04754408(String)"}
{"@timestamp":"2025-07-29T02:40:00.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-29 10:40:00.407(Timestamp), (String), 0(Integer), 0(String), G04754409(String)"}
{"@timestamp":"2025-07-29T02:40:00.423Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.423Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.423Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1.5(String), 2025-07-29 10:40:00.423(Timestamp), (String), 0(Integer), 0(String), G16733827(String)"}
{"@timestamp":"2025-07-29T02:40:00.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.3.3_c(String), 2025-07-29 10:40:00.436(Timestamp), (String), 0(Integer), 0(String), *********(String)"}
{"@timestamp":"2025-07-29T02:40:00.455Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.8(String), 2025-07-29 10:40:00.455(Timestamp), 20241224-1122(String), 0(Integer), 0(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-29T02:40:00.471Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:00.471Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-29T02:40:00.471Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1.6(String), 2025-07-29 10:40:00.471(Timestamp), (String), 0(Integer), 0(String), PID388E2207007544(String)"}
{"@timestamp":"2025-07-29T02:40:00.486Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-29T02:40:00.486Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新10条"}
{"@timestamp":"2025-07-29T02:40:00.486Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新 OK, 共计更新10条"}
{"@timestamp":"2025-07-29T02:40:00.486Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：2502130940211339531603881230336  总共耗时：482毫秒"}
{"@timestamp":"2025-07-29T02:40:00.486Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:00.486Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072910400002269594936967984129(String), 2502130940211339531603881230336(String), 设备固件版本更新(String), deviceVersionUpdateTaskService(String), (String), 0(Integer), 482(Integer), 2025-07-29 10:40:00.004(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:10.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"a1cd301d5f03dcd0","span":"b4479d83269d9e6f","parent":"0dad865c5a0cc2b5","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810092\",\"timestamp\":1753756810092,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=a1cd301d5f03dcd0, spanId=a1cd301d5f03dcd0, nativeHeaders={spanTraceId=[a1cd301d5f03dcd0], spanId=[a1cd301d5f03dcd0], spanSampled=[0]}, mqtt_duplicate=false, id=00ff218e-221c-a4d2-a2ba-e96c3941cb19, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:10.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:10.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810092\",\"timestamp\":1753756810092,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:10.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:10.376Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:10.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:10.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:10.376(Timestamp), 2025-07-29 10:40:10.376(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"INFO","service":"eeip-standalone-service","trace":"8224c0f8b89cbb6f","span":"567ca4e95171f2d5","parent":"8ab08776eb9082e9","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810099\",\"timestamp\":1753756810099,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=8224c0f8b89cbb6f, spanId=8224c0f8b89cbb6f, nativeHeaders={spanTraceId=[8224c0f8b89cbb6f], spanId=[8224c0f8b89cbb6f], spanSampled=[0]}, mqtt_duplicate=false, id=83c17688-7e92-713e-f20d-39192436bd78, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756810099\",\"timestamp\":1753756810099,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:10.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:10.393(Timestamp), 2025-07-29 10:40:10.393(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:10.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:10.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:10.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:10.4(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:10.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:10.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:10.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:10.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:10.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:10.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:10.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:10.418(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:10.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:25.366Z","severity":"INFO","service":"eeip-standalone-service","trace":"4f5829c740d61d89","span":"6ed5eb3b8fd28f3e","parent":"f725445a9a5ba561","exportable":"true","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825093\",\"timestamp\":1753756825093,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=4f5829c740d61d89, spanId=4f5829c740d61d89, nativeHeaders={spanTraceId=[4f5829c740d61d89], spanId=[4f5829c740d61d89], spanSampled=[1]}, mqtt_duplicate=false, id=ef8ca6d8-0fac-b9bc-a5a3-2f442ab33d8b, spanSampled=1, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:25.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:25.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825093\",\"timestamp\":1753756825093,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:25.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:25.368Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:25.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:25.369Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:25.368(Timestamp), 2025-07-29 10:40:25.368(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:25.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:25.384Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:25.385Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:25.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"3186a570687dcdb3","span":"05c88503fbbd895f","parent":"3578bb4fecbbe0e5","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825098\",\"timestamp\":1753756825098,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=3186a570687dcdb3, spanId=3186a570687dcdb3, nativeHeaders={spanTraceId=[3186a570687dcdb3], spanId=[3186a570687dcdb3], spanSampled=[0]}, mqtt_duplicate=false, id=b27da244-3e71-2bef-6d66-aed55d45cb73, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:25.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:25.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:25.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756825098\",\"timestamp\":1753756825098,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:25.386Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:25.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:25.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:25.386(Timestamp), 2025-07-29 10:40:25.386(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:25.391Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:25.391Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:25.391Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:25.391(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:25.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:25.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:25.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:25.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:25.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:25.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:25.410Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:25.409(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:25.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"0489bce5d369ab2b","span":"7d188bd74b72cff5","parent":"d0b882d7fa4f6ed2","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-6","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840090\",\"timestamp\":1753756840090,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=0489bce5d369ab2b, spanId=0489bce5d369ab2b, nativeHeaders={spanTraceId=[0489bce5d369ab2b], spanId=[0489bce5d369ab2b], spanSampled=[0]}, mqtt_duplicate=false, id=77a53fb2-b5d6-dc70-4158-aaa90ce0553d, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840090\",\"timestamp\":1753756840090,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:40.376Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:40.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:40.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:40.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:40.376(Timestamp), 2025-07-29 10:40:40.376(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"ac3ccf82a6ceada8","span":"aeab42654568a5db","parent":"664c42f368721b6b","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-7","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840099\",\"timestamp\":1753756840099,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=ac3ccf82a6ceada8, spanId=ac3ccf82a6ceada8, nativeHeaders={spanTraceId=[ac3ccf82a6ceada8], spanId=[ac3ccf82a6ceada8], spanSampled=[0]}, mqtt_duplicate=false, id=2cdb1e09-5d4d-de0a-54e2-6046caec642f, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756840099\",\"timestamp\":1753756840099,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:40.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:40.388Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:40.388Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:40.388Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:40.388(Timestamp), 2025-07-29 10:40:40.388(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:40.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:40.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:40.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:40.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:40.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:40.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:40.398(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:40.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:40.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:40.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:40.411Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:40.411Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:40.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:40.411(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:40.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:40.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:55.392Z","severity":"INFO","service":"eeip-standalone-service","trace":"81384fcc1c62d1b4","span":"b701e948876a182f","parent":"726887f73317a325","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-8","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855105\",\"timestamp\":1753756855105,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=81384fcc1c62d1b4, spanId=81384fcc1c62d1b4, nativeHeaders={spanTraceId=[81384fcc1c62d1b4], spanId=[81384fcc1c62d1b4], spanSampled=[0]}, mqtt_duplicate=false, id=fe8a289e-5e52-d274-1d25-40c8675be1b7, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:55.392Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:55.392Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755352808\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855105\",\"timestamp\":1753756855105,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:55.392Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:55.392Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-5","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:55.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:55.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:15:52.808(Timestamp), 2(String), 2025-07-29 10:40:55.392(Timestamp), 2025-07-29 10:40:55.392(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:55.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:55.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:55.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:55.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"a9a22b2cb7be7769","span":"d17a305906dcaf4e","parent":"c2823e4ff713946b","exportable":"false","pid":"24360","thread":"ThreadPoolTaskExecutor-9","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855116\",\"timestamp\":1753756855116,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}, headers={mqtt_receivedRetained=false, spanTraceId=a9a22b2cb7be7769, spanId=a9a22b2cb7be7769, nativeHeaders={spanTraceId=[a9a22b2cb7be7769], spanId=[a9a22b2cb7be7769], spanSampled=[0]}, mqtt_duplicate=false, id=5b869cb5-22ff-88aa-5048-83d8f4b1839b, spanSampled=0, mqtt_receivedTopic=/PROD-DZBP/*********/event/on_teacher_attendance, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-29T02:40:55.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"接收到教师考勤信息: {\"data\":{\"pkbbh\":\"2507281604421399422327325720695\",\"device_id\":\"*********\",\"curriculum_id\":\"wmzd(kz)\",\"time\":\"1753755448074\",\"job_number\":\"JSGH202410105\",\"place_id\":\"2011203\",\"pkkssj\":\"2025-07-29 10:30:00\",\"pkjssj\":\"2025-07-29 12:10:00\"},\"sign\":\"\",\"messageId\":\"upKqqdjg_2_wmzd(kz)_JSGH202410105_1753756855116\",\"timestamp\":1753756855116,\"token\":\"46fae610bd7246e48604603f1cce21bf\"}"}
{"@timestamp":"2025-07-29T02:40:55.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:*********"}
{"@timestamp":"2025-07-29T02:40:55.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-3","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/PROD-DZBP/*********/event/on_teacher_attendance] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-29T02:40:55.413Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-2","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"设备上报事件,topic[/PROD-DZBP/*********/event/on_teacher_attendance]无法处理"}
{"@timestamp":"2025-07-29T02:40:55.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==>  Preparing: INSERT INTO kq_dkjl ( bh,rylb,xgh,pkbbh,pkkssj,pkjssj,bqbj,dksj,dkfs,cjsj,gxsj ) VALUES( ?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-29T02:40:55.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"==> Parameters: null, 0(Integer), JSGH202410105(String), 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), null, 2025-07-29 10:17:28.074(Timestamp), 2(String), 2025-07-29 10:40:55.413(Timestamp), 2025-07-29 10:40:55.413(Timestamp)"}
{"@timestamp":"2025-07-29T02:40:55.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:55.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:55.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:15:52.808(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:55.417(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:55.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqDkjlMapper.insert","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:55.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==>  Preparing: SELECT bh,pkbbh,pkkssj,pkjssj,gh,kqjg,jgms,kqsj,cjsj,gxsj FROM kq_jskq WHERE pkbbh = ? AND gh = ? "}
{"@timestamp":"2025-07-29T02:40:55.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"==> Parameters: 2507281604421399422327325720695(String), JSGH202410105(String)"}
{"@timestamp":"2025-07-29T02:40:55.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-1","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-29T02:40:55.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.mapper.KqJskqMapper.selectOne","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-29T02:40:55.438Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==>  Preparing: UPDATE kq_jskq SET pkbbh = ?,pkkssj = ?,pkjssj = ?,gh = ?,kqjg = ?,jgms = ?,kqsj = ?,cjsj = ?,gxsj = ? WHERE bh = ? "}
{"@timestamp":"2025-07-29T02:40:55.438Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"==> Parameters: 2507281604421399422327325720695(String), 2025-07-29 10:30:00.0(Timestamp), 2025-07-29 12:10:00.0(Timestamp), JSGH202410105(String), 0(Integer), null, 2025-07-29 10:17:28.074(Timestamp), 2025-07-29 10:15:25.0(Timestamp), 2025-07-29 10:40:55.436(Timestamp), 228(Integer)"}
{"@timestamp":"2025-07-29T02:40:55.450Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24360","thread":"ThreadPoolTaskExecutor-4","class":"c.x.a.m.KqJskqMapper.updateByPrimaryKey","rest":"<==    Updates: 1"}
