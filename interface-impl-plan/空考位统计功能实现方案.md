# 空考位统计功能实现方案

> 基于接口实现设计模板，为菜单-身份核验-查询统计-空考位统计功能提供完整的技术实现方案。

---

## 1. 需求分析

### 1.1 功能概述

- **模块/业务名称**：身份核验-空考位统计
- **核心目标**：统计考场空考位信息，提供考场管理和监控功能

### 1.2 功能详细需求

- **场景 1**：空考位统计查询（合并列表和统计头部信息）
  - 输入条件：分页参数、考试计划编号（ksjhbh）、考试场次（下拉框筛选）、终端上报情况（是否下拉框）
  - 期望输出：分页列表数据（考场号、场所名称、终端上报情况、终端上报时间、操作详情按钮）+ 统计摘要信息（总考场数、已上报考场数、未上报考场数、上报比例）

- **场景 2**：详情页面查询
  - 输入条件：分页参数、考试计划编号（ksjhbh）、考试场次（下拉框）、异常描述（下拉框：全部/1-误识别/2-坐错他人位置/3-实际未参加考试/4-他人坐错位置/5-人工核验/6-缺考（空位）/7-无编排）、准考证号（文本框）、考场号（文本框）
  - 期望输出：考场号、场所名称、准考证号、姓名、座位号、异常描述、终端上报时间、上报上级平台情况、上报上级平台上报时间

### 1.3 技术约束

- **所属系统/模块**：eeip-identity-verify身份核验模块
- **数据库**：复用表 ks_kcxx（考场信息）、ks_kkw_msg（空考位消息）、cs_jsjbxx（教室基本信息）
- **依赖服务**：无外部依赖，使用现有的MyBatis查询
- **其他限制**：需考虑无考场对应的情况，以逻辑考场号为主信息进行查询

---

## 2. 架构设计

### 2.1 设计原则

- **复用现有架构**：遵循EEIP三层架构模式（Controller → Service → Mapper）
- **分层设计**：Controller → Service → Mapper
- **统一响应**：`WrapMapper.ok()`

### 2.2 模块依赖关系

```text
eeip-identity-verify/
├── provider/eeip-provider-identity-verify/
│   ├── facade/manager/
│   │   └── CxtjController.java
│   ├── service/
│   │   ├── KsKkwMsgService.java
│   │   └── impl/KsKkwMsgServiceImpl.java
│   ├── mapper/
│   │   └── KsKkwMsgServiceMapper.java
│   └── resources/mapper/
│       └── KsKkwMsgServiceMapper.xml
└── api/eeip-api-identity-verify/
    └── model/
        ├── req/KkwTjReq.java
        ├── resp/KkwTjResp.java
        └── dto/KkwTjDTO.java
```

---

## 3. 接口设计

### 3.1 REST 接口

| 编号    | 接口名称        | URL                             | Method | 请求模型       | 响应模型         |
| ----- | ----------- |---------------------------------| ------ |------------|--------------|
| 3.1.1 | 空考位统计查询     | `/manager/identity/cxtj/kkwtj`  | POST   | `kkwTjReq` | `KkwTjResp`  |
| 3.1.2 | 空考位详情页面查询   | `/manager/identity/cxtj/kkwxq`  | POST   | `KkwXqReq` | `KkwXqResp`  |
| 3.1.3 | 空考位详情导出     | `/manager/identity/cxtj/kkwxqdc` | POST   | `KkwXqReq` | `KkwXqdcResp` |

> **示例代码**（接口 3.1.1）：

```java
@PostMapping("/manager/identity/cxtj/kkwtj")
public Wrapper<KkwTjResp> getEmptySeatsStatistics(@RequestBody kkwTjReq reqModel) {
    KkwTjResp result = ksKkwMsgService.getKkwTj(reqModel);
    return WrapMapper.ok(result);
}
```

### 3.2 数据传输对象（DTO / VO / Model）

#### 3.2.1 请求模型（Req）

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwTjReq extends RemoteReqBaseModel {
    private static final long serialVersionUID = -1L;

    /** 考试场次 */
    private String ccm;
    
    /** 终端上报情况 0-否 1-是 */
    private String sbzt;
    
    /** 考试计划编号 */
    private String ksjhbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwXqReq extends RemoteReqBaseModel {
    private static final long serialVersionUID = -1L;

    /** 考试计划编号 */
    private String ksjhbh;
    
    /** 考试场次 */
    private String ccm;
    
    /** 异常类型 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工核验 6-缺考（空位） 7-无编排 */
    private String yclx;
    
    /** 准考证号 */
    private String zkzh;
    
    /** 考场号 */
    private String kch;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

#### 3.2.2 响应模型（Resp）

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwTjResp extends RemoteRespBaseModel {
    private static final long serialVersionUID = -1L;

    /** 分页数据 */
    private PageInfo<KkwTjItem> pageInfo;
    
    /** 统计摘要 */
    private KkwTjInfoVO summary;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwTjXqResp extends RemoteRespBaseModel {
    private static final long serialVersionUID = -1L;

    /** 分页数据 */
    private PageInfo<KkwTjXqVO> pageInfo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class KkwXqdcResp extends RemoteRespBaseModel {
    private static final long serialVersionUID = -1L;

    /** 导出文件路径 */
    private String dclj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

#### 3.2.3 内部 DTO / VO 示例

```java
@Data
@EqualsAndHashCode(callSuper = false)
public class KkwTjItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 考场号 */
    private String kch;
    
    /** 场所名称 */
    private String csmc;
    
    /** 终端上报情况 0-否 1-是 */
    private String sfsb;
    
    /** 终端上报时间 */
    private String sbsj;
    
    /** 考场ID（用于详情查询） */
    private String bzhkcid;
    
    /** 逻辑考场编号 */
    private String ljkcbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

```java
@Data
@EqualsAndHashCode(callSuper = false)
public class KkwTjInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 总考场数 */
    private Integer total;
    
    /** 已上报考场数 */
    private Integer ysb;
    
    /** 未上报考场数 */
    private Integer wsb;
    
    /** 上报比例 */
    private String reportRate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

```java
@Data
@EqualsAndHashCode(callSuper = false)
public class KkwTjXqVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 考场号 */
    private String kch;
    
    /** 场所名称 */
    private String csmc;
    
    /** 准考证号 */
    private String zkzh;
    
    /** 姓名 */
    private String xm;
    
    /** 座位号 */
    private String zwh;
    
    /** 异常描述 */
    private String yclx;
    
    /** 终端上报时间 */
    private String zdsbsj;
    
    /** 上报上级平台情况 */
    private String sfsb;
    
    /** 上报上级平台上报时间 */
    private String sbsjsj;
    
    /** 逻辑考场号 */
    private String ljkcbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

#### 3.2.4 Excel导出模板类

```java
@Data
@ContentRowHeight(17)//内容部分行高
@HeadRowHeight(32)//表头行高
@HeadFontStyle(fontHeightInPoints = 12)//表头字体
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,//居中
        borderBottom = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,//边框
        borderRight = BorderStyleEnum.THIN,borderLeft = BorderStyleEnum.THIN)
public class KkwXqTemplate {
    
    /** 考场号 */
    @ColumnWidth(15)
    @ExcelProperty({"考场号"})
    private String kch;
    
    /** 场所名称 */
    @ColumnWidth(20)
    @ExcelProperty({"场所名称"})
    private String csmc;
    
    /** 准考证号 */
    @ColumnWidth(18)
    @ExcelProperty({"准考证号"})
    private String zkzh;
    
    /** 姓名 */
    @ColumnWidth(12)
    @ExcelProperty({"姓名"})
    private String xm;
    
    /** 座位号 */
    @ColumnWidth(10)
    @ExcelProperty({"座位号"})
    private String zwh;
    
    /** 异常描述 */
    @ColumnWidth(18)
    @ExcelProperty({"异常描述"})
    private String yclx;
    
    /** 终端上报时间 */
    @ColumnWidth(20)
    @ExcelProperty({"终端上报时间"})
    private String zdsbsj;
    
    /** 逻辑考场号 */
    @ColumnWidth(15)
    @ExcelProperty({"逻辑考场号"})
    private String ljkcbh;
}
```

---

## 4. 数据库设计

### 4.1 表结构

- **主表**：`ks_kcxx`（考场信息表 - 包含考场编号、考场名称、逻辑考场编号等）
- **关联表1**：`ks_kkw_msg`（空考位消息表 - 通过ljkch字段关联考场表，包含上报时间、考生信息等）
- **关联表2**：`cs_jsjbxx`（教室基本信息表 - 通过bzhkcid字段关联，提供教室名称等场所信息）

### 4.2 SQL / MyBatis Mapper 片段

```xml
<!-- 空考位统计列表查询 -->
<select id="selectKkwTjLb" resultType="com.xcwlkj.identityverify.model.vo.KkwTjItem">
    SELECT DISTINCT
        kc.kcbh as kch,
        kc.bzhkcmc as csmc,
        CASE
        WHEN count(msg.id) > 0 THEN '1'
        ELSE '0'
        END as sfsb,
        DATE_FORMAT(max(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj,
        kc.bzhkcid as bzhkcid,
        kc.ljkcbh as ljkcbh
    FROM ks_kcxx kc
    LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH 
        AND msg.SCZT = '0'
        <if test="ccm != null and ccm != ''">
        AND msg.CCM = #{ccm}
        </if>
    LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0'
    WHERE kc.sczt = '0'
        <if test="ksjhbh != null and ksjhbh != ''">
        AND kc.ksjhbh = #{ksjhbh}
        </if>
        <if test="sbzt != null and sbzt != ''">
            <if test="sbzt == '1'.toString()">
            AND msg.id IS NOT NULL
            </if>
            <if test="sbzt == '0'.toString()">
            AND msg.id IS NULL
            </if>
        </if>
    ORDER BY kc.kcbh
</select>

<!-- 空考位统计摘要信息 -->
<select id="selectKkwTjInfo" resultType="com.xcwlkj.identityverify.model.vo.KkwtjInfoVO">
    SELECT
        COUNT(DISTINCT kc.ljkcbh) as totalExamRooms,
        COUNT(DISTINCT msg.kch) as reportedExamRooms,
        COUNT(DISTINCT CASE WHEN msg.ljkch IS NULL THEN kc.ljkcbh END) as unreportedExamRooms,
        CONCAT(
            ROUND(
                COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.id END) * 100.0 /
                COUNT(DISTINCT kc.id),
                2
            ),
            '%'
        ) as reportRate
    FROM ks_kcxx kc
    LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM 
        AND msg.SCZT = '0'
        <if test="ccm != null and ccm != ''">
        AND msg.CCM = #{ccm}
        </if>
    WHERE kc.sczt = '0'
        <if test="ksjhbh != null and ksjhbh != ''">
        AND kc.ksjhbh = #{ksjhbh}
        </if>
    GROUP BY kc.bzhkdid
</select>

<!-- 空考位详情列表查询 -->
<select id="getKkwTjXq" resultType="com.xcwlkj.identityverify.model.vo.KkwTjXqVO">
    SELECT 
        kc.ljkcbh as kch,
        kc.bzhkcmc as csmc,
        msg.KS_ZKZH as zkzh,
        ksxx.ksxm as xm,
        msg.KS_BPZWH as zwh,
        msg.RCBZ yclx,
        DATE_FORMAT(msg.timestamp, '%Y-%m-%d %H:%i:%s') as zdsbsj,
        sbzs.report_flag as sfsb,  
        sbzs.report_time as sbsjsj 
    FROM ks_kcxx kc
    INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm
    LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0'
    LEFT JOIN (
    select sum(sbkszs) as sbkszs,ksjhbh,ccm,ljkcbh,sblx,max(report_flag) as report_flag,max(report_time) as report_time from ks_ydsb_sbkszs GROUP BY ksjhbh,ccm,ljkcbh,sblx
    ) sbzs ON
    msg.ksjhbh = sbzs.ksjhbh AND msg.ccm = sbzs.ccm AND msg.ljkch = sbzs.ljkcbh
    LEFT JOIN ks_ksrcxx ksxx on
    msg.ksjhbh = ksxx.ksjhbh AND
    msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh

    WHERE kc.sczt = '0'
        <if test="ksjhbh != null and ksjhbh != ''">
        AND kc.ksjhbh = #{ksjhbh}
        </if>
        <if test="ccm != null and ccm != ''">
        AND msg.CCM = #{ccm}
        </if>
        <if test="yclx != null and yclx != ''">
        AND msg.RCBZ = #{yclx}
        </if>
        <if test="zkzh != null and zkzh != ''">
        AND msg.KS_ZKZH LIKE CONCAT('%', #{zkzh}, '%')
        </if>
        <if test="kch != null and kch != ''">
        AND kc.ljkcbh LIKE CONCAT('%', #{kch}, '%')
        </if>
    ORDER BY kc.ljkcbh, msg.KS_BPZWH
</select>
```

---

## 5. 业务逻辑实现

### 5.1 Service 接口

```java
public interface KsKkwMsgService {
    /**
     * 查询空考位统计信息（包含列表和摘要）
     */
    KkwTjResp getKkwTj(kkwTjReq req);
    
    /**
     * 查询空考位详情列表
     */
    KkwTjXqResp  getKkwTjXq(KkwTjXqReq req);
    
    /**
     * 导出空考位详情Excel
     */
    String exportKkwXqExcel(KkwXqDTO dto);
}
```

### 5.2 Service 实现要点

1. **参数校验**：验证必要的查询参数
2. **分页查询**：使用PageHelper进行分页处理（导出时不分页）
3. **VO/DTO 转换**：统一数据格式转换
4. **Excel导出**：使用EasyExcel框架生成xlsx文件，包含异常类型转换
5. **异常与日志**：记录关键操作日志，处理查询异常

### 5.3 Controller 要点

- **BeanUtils 复制**：请求参数到DTO的转换
- **统一异常处理**：使用框架统一异常处理机制
- **响应包装**：使用WrapMapper统一包装响应结果

---

## 6. 单元测试设计

| 用例编号  | 场景         | 关键步骤            | 断言                           |
| ----- | ---------- |-----------------| ---------------------------- |
| 6.1.1 | 正常统计查询     | 调用 `getKkwTj`   | 返回分页数据 size > 0，统计摘要信息正确      |
| 6.1.2 | 终端上报状态筛选   | 传入 sfsb=1       | 返回数据均为已上报状态                  |
| 6.1.3 | 考试场次筛选     | 传入 ccm          | 返回数据均为指定场次                   |
| 6.1.4 | 统计摘要准确性    | 验证统计摘要中的各项数据    | 总数、已上报数、未上报数、比例计算正确          |
| 6.1.5 | 详情列表查询     | 调用 `getKkwTjXq` | 返回符合筛选条件的详细空考位信息             |
| 6.1.6 | 异常描述筛选     | 传入 yclx=1       | 返回数据均为指定异常类型                 |
| 6.1.7 | 准考证号筛选     | 传入 zhzk         | 返回包含指定准考证号的数据                |
| 6.1.8 | 考场号筛选      | 传入 kch          | 返回包含指定考场号的数据                 |
| 6.1.9 | Excel导出测试   | 调用 `exportKkwXqExcel` | 返回有效文件路径，文件包含正确数据格式 |
| 6.1.10 | 参数缺失       | 传空 req          | 抛 `IllegalArgumentException` |

> **技术栈**：JUnit5；使用 MockMvc 测试 Controller，基于 `com.xcwlkj.junit.MinimalDatabaseTest`编写新的测试类

---

## 7. 实施计划

| 阶段      | 任务                 | 预计工时  |
| ------- | ------------------ | ----- |
| Phase 1 | DTO / VO / Req / Resp 创建 | 0.5 d |
| Phase 2 | Mapper & SQL 编写      | 1 d   |
| Phase 3 | Service 层实现         | 1 d   |
| Phase 4 | Controller 层实现      | 0.5 d |
| Phase 5 | Excel导出模板和功能实现    | 0.5 d |
| Phase 6 | 单元测试编写             | 0.5 d |
| Phase 7 | 集成测试 & 性能调优        | 0.5 d |

---

## 8. 风险评估与应对

| 风险         | 影响               | 应对措施                           |
| ---------- | ---------------- | ------------------------------ |
| 表关联复杂性     | 查询性能下降，数据不一致     | 建立合适的索引，优化SQL查询逻辑             |
| 无考场对应情况    | 数据显示异常           | 使用LEFT JOIN，以逻辑考场号为主进行查询      |
| 大数据量查询性能   | 页面响应慢            | 添加分页查询，建立索引，考虑缓存机制             |
| 大数据量Excel导出 | 导出超时，内存溢出        | 分批查询导出，使用流式写入，设置合理的超时时间      |
| 上级平台上报信息缺失 | 详情页面信息不完整        | 预留字段，后续补充相关表关联               |
| 考生姓名信息缺失   | 详情页面显示不完整        | 需要关联考生信息表（ks_ksxx等），获取考生姓名信息 |

---

## 9. 总结

- **架构一致**：遵循EEIP三层架构模式，保持代码风格统一
- **代码复用**：复用现有的BaseController、BaseService等基础组件
- **数据完整**：考虑无考场对应情况，以逻辑考场号为主信息进行统计
- **导出功能**：基于EasyExcel实现Excel导出，支持xlsx格式，包含完整的空考位详情信息
- **扩展性强**：预留上级平台上报、考生信息等扩展字段
- **测试完备**：覆盖主要业务场景的单元测试和集成测试

> **备注**：
> 1. 考生姓名需要关联考生信息表（如ks_ksrcxx）通过准考证号获取
> 2. 上级平台上报情况需要关联相关上报记录表
> 3. Excel导出功能已实现，导出列头包括：考场号、场所名称、准考证号、姓名、座位号、异常描述、终端上报时间、逻辑考场号
> 4. 建议在ks_kkw_msg表的LJKCH、CCM字段上建立复合索引提升查询性能
> 5. 考虑添加缓存机制优化统计查询性能
> 6. 大数据量导出时建议使用异步处理避免请求超时