# 接口实现设计模板

> 本模板以“考务消息查询功能实现方案”为范本，保留了相同的章节与层次结构，所有占位符请根据实际项目替换。

---

## 1. 需求分析

### 1.1 功能概述

- **模块/业务名称**：{{模块名称}}
- **核心目标**：{{核心目标简述}}

### 1.2 功能详细需求

- **场景 1**：{{场景 / 用户故事}}
  - 输入条件：{{条件}}
  - 期望输出：{{输出}}
- **场景 2**：{{…}}

### 1.3 技术约束

- **所属系统/模块**：{{所在系统}}
- **数据库**：{{复用表 / 新增表}}
- **依赖服务**：{{外部依赖}}
- **其他限制**：{{…}}

---

## 2. 架构设计

### 2.1 设计原则

- **复用现有架构**：{{说明}}
- **分层设计**：Controller → Service → Mapper
- **统一响应**：`WrapMapper.ok()`

### 2.2 模块依赖关系

```text
{{示意目录结构}}
```

---

## 3. 接口设计

### 3.1 REST 接口

| 编号    | 接口名称     | URL             | Method | 请求模型           | 响应模型            |
| ----- | -------- | --------------- | ------ | -------------- | --------------- |
| 3.1.1 | {{接口 1}} | `/api/{{path}}` | POST   | `{{ReqModel}}` | `{{RespModel}}` |
| 3.1.2 | {{接口 2}} | `/api/{{path}}` | POST   | `{{ReqModel}}` | `{{RespModel}}` |

> **示例代码**（接口 3.1.1）：

```java
@PostMapping("/api/{{path}}")
public Wrapper<{{RespModel}}> {{methodName}}(@RequestBody {{ReqModel}} reqModel) {
    // TODO 业务实现
}
```

### 3.2 数据传输对象（DTO / VO / Model）

#### 3.2.1 请求模型（Req）

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class {{ReqModel}} extends RemoteReqBaseModel {
    private static final long serialVersionUID = -1L;

    // TODO: 字段定义

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

#### 3.2.2 响应模型（Resp）

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class {{RespModel}} extends RemoteRespBaseModel {
    private static final long serialVersionUID = -1L;

    // TODO: 字段定义

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

#### 3.2.3 内部 DTO / VO 示例

```java
@Data
@EqualsAndHashCode(callSuper = false)
public class {{DtoName}} implements Serializable {
    private static final long serialVersionUID = 1L;

    // TODO: 字段定义

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

> ⚠️ **字段设计规范**：出现嵌套对象时，Req 使用独立 `DTO`，Resp 使用独立 `VO`，数组一律使用 `List`，时间一律以字符串传递

---

## 4. 数据库设计

### 4.1 表结构

- **主表**：`{{table_name}}`（说明关键字段）
- **关联表**：`{{table_name}}`（用途说明）

### 4.2 SQL / MyBatis Mapper 片段

```xml
<select id="select{{Entity}}List" resultType="{{VO}}">
    SELECT ...
    FROM {{table}}
    WHERE ...
</select>
```

---

## 5. 业务逻辑实现

### 5.1 Service 接口

```java
public interface {{ServiceName}} {
    PageInfo<{{VO}}> list({{DTO}} dto);
}
```

### 5.2 Service 实现要点

1. 参数校验
2. 分页查询（PageHelper）
3. VO/DTO 转换
4. 异常与日志

### 5.3 Controller 要点

- BeanUtils 复制
- 统一异常处理

---

## 6. 单元测试设计

| 用例编号  | 场景   | 关键步骤      | 断言                           |
| ----- | ---- | --------- | ---------------------------- |
| 6.1.1 | 正常查询 | 调用 `list` | 返回集合 size > 0                |
| 6.1.2 | 参数缺失 | 传空 DTO    | 抛 `IllegalArgumentException` |

> **技术栈**：JUnit5；必要时使用 MockMvc 测试 Controller。

---

## 7. 实施计划

| 阶段      | 任务           | 预计工时  |
| ------- | ------------ | ----- |
| Phase 1 | DTO / VO 创建  | 0.5 d |
| Phase 2 | Mapper & SQL | 0.5 d |
| Phase 3 | Service 层    | 1 d   |
| Phase 4 | Controller   | 0.5 d |
| Phase 5 | 单元测试         | 0.5 d |
| Phase 6 | 集成测试 & 调优    | 0.5 d |

---

## 8. 风险评估与应对

| 风险    | 影响     | 应对措施        |
| ----- | ------ | ----------- |
| 性能瓶颈  | 高并发查询慢 | 建索引、缓存、分页   |
| 需求变更  | 迭代延期   | 保持接口通用、可扩展  |
| 数据一致性 | 统计错误   | 事务控制、单元测试覆盖 |

---

## 9. 总结

- **架构一致**：遵循公司三层架构
- **代码复用**：优先复用现有模块
- **测试完备**：单元 + 集成测试

> **备注**：模板内容仅供参考，具体实现可根据项目实际情况进行调整。

