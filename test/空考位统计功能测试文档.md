# 空考位统计功能测试文档

## 执行摘要

**测试执行日期：** [待填写：2025-07-29~2025-07-30]

**测试环境：** [本地开发环境]

**测试用例总数：** [28]

**通过：** √[待填写] | **失败：** ×[待填写] | **跳过：** ⚠[待填写]

**成功率：** [待填写：XX%]

### 关键发现

- [待填写：发现的关键问题简要总结]
- [待填写：API整体健康状况评估]
- [待填写：性能亮点或关注点]

---

## 测试环境详情

| 参数 | 值                          |
| --- |----------------------------|
| 基础URL | 127.0.0.1:8888             |
| 环境 | 本地开发环境                     |
| 测试框架 | Apifox                     |
| 数据库 | MySQL 8.0                  |
| 测试人员 | kongkn                     |
| 模块 | eeip-identity-verify身份核验模块 |

---

## 测试覆盖率

### 已测试端点

| 端点 | 方法 | 分类 | 状态 |
| --- | --- | --- | --- |
| `/manager/identity/cxtj/kkwtj` | POST | 空考位统计查询 | [待填写] |
| `/manager/identity/cxtj/kkwxq` | POST | 空考位详情查询 | [待填写] |
| `/manager/identity/cxtj/kkwxqdc` | POST | 空考位详情导出 | [待填写] |

**图例：** ✅ 通过 | ❌ 失败 | ⚠️ 警告 | ⏭️ 跳过

---

## 详细测试结果

### 🟢 通过的测试

### TC001 - 空考位统计查询 - 正常查询

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证正常参数下的空考位统计查询
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "ccm": "001",
  "sbzt": ""
}
```
- **预期结果：**
  - 响应状态码：200
  - 返回分页数据，包含考场列表
  - 返回统计摘要信息（总考场数、已上报数、未上报数、上报比例）
  - 响应时间 < 2000ms
- **测试断言：**
  - `response.data.pageInfo` 不为空
  - `response.data.summary` 包含所有统计字段
  - 分页信息正确（total、pageNum、pageSize）
- **实际结果：** ✅

### TC002 - 空考位统计查询 - 已上报筛选

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证终端上报状态筛选功能
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "ccm": "001",
  "sbzt": "1"
}
```
- **预期结果：**
  - 返回的考场数据中 `sfsb` 字段均为 "1"
  - 统计摘要中已上报数量 = 返回的总记录数
- **测试断言：**
  - 所有返回记录的 `sfsb` = "1"
  - `summary.ysb` >= 返回的记录数
- **实际结果：** ✅

### TC003 - 空考位统计查询 - 未上报筛选

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证未上报考场筛选功能
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "ccm": "001",
  "sbzt": "0"
}
```
- **预期结果：**
  - 返回的考场数据中 `sfsb` 字段均为 "0"
  - 统计摘要中未上报数量正确
- **测试断言：**
  - 所有返回记录的 `sfsb` = "0"
  - `summary.wsb` >= 返回的记录数
- **实际结果：** ✅

### TC004 - 空考位统计查询 - 考试场次筛选

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证考试场次筛选功能
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "ccm": "002",
  "sbzt": ""
}
```
- **预期结果：**
  - 返回指定场次的考场数据
  - 统计摘要基于该场次计算
- **测试断言：**
  - 查询条件正确传递到数据库
  - 返回数据符合场次筛选条件
- **实际结果：** ✅

### TC005 - 空考位统计查询 - 分页功能

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证分页查询功能
- **请求参数：**
```json
{
  "pageNum": 2,
  "pageSize": 5,
  "ksjhbh": "TEST2025001"
}
```
- **预期结果：**
  - 正确返回第2页数据
  - 每页最多5条记录
  - 分页信息准确
- **测试断言：**
  - `pageInfo.pageNum` = 2
  - `pageInfo.pageSize` = 5
  - `pageInfo.list.size()` <= 5
- **实际结果：** ✅

### TC006 - 空考位统计查询 - 统计摘要准确性

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证统计摘要数据的准确性
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 100,
  "ksjhbh": "TEST2025001"
}
```
- **预期结果：**
  - total = ysb + wsb
  - reportRate = (ysb / total) * 100%
  - 统计数据与实际数据一致
- **测试断言：**
  - 验证总数计算准确性
  - 验证上报比例计算准确性
  - 各统计值非负数
- **实际结果：** ✅

### TC007 - 空考位详情查询 - 正常查询

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwxq`
- **测试目标：** 验证空考位详情正常查询
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "ccm": "001"
}
```
- **预期结果：**
  - 返回详细的空考位信息列表
  - 包含考场号、准考证号、姓名、座位号等信息
- **测试断言：**
  - `pageInfo.list` 不为空
  - 每条记录包含必要字段
  - 数据格式正确
- **实际结果：** ✅ 
- **备注：** 测试数据中多编制了两条空考位消息，不存在对应的考生，查询的考生姓名为空

### TC008 - 空考位详情查询 - 异常类型筛选

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwxq`
- **测试目标：** 验证异常类型筛选功能
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "ccm": "001",
  "yclx": "6"
}
```
- **预期结果：**
  - 返回异常类型为"6-缺考（空位）"的记录
  - 筛选结果准确
- **测试断言：**
  - 所有记录的 `yclx` = "6"
  - 异常描述正确显示
- **实际结果：** ✅

### TC009 - 空考位详情查询 - 准考证号模糊查询

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwxq`
- **测试目标：** 验证准考证号模糊查询功能
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "zkzh": "2025"
}
```
- **预期结果：**
  - 返回准考证号包含"2025"的记录
  - 模糊匹配正确
- **测试断言：**
  - 所有记录的 `zkzh` 包含查询关键字
  - 忽略大小写匹配
- **实际结果：** ✅

### TC010 - 空考位详情查询 - 考场号模糊查询

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwxq`
- **测试目标：** 验证考场号模糊查询功能
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "kch": "101"
}
```
- **预期结果：**
  - 返回考场号包含"001"的记录
  - 模糊匹配正确
- **测试断言：**
  - 所有记录的 `kch` 包含查询关键字
  - 匹配逻辑考场编号
- **实际结果：** ✅

### TC011 - 空考位详情查询 - 多条件组合查询

- **状态：** [✅]
- **接口：** POST `/manager/identity/cxtj/kkwxq`
- **测试目标：** 验证多个筛选条件组合查询
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ksjhbh": "TEST2025001",
  "ccm": "001",
  "yclx": "6",
  "zkzh": "2025"
}
```
- **预期结果：**
  - 返回同时满足所有条件的记录
  - AND逻辑正确
- **测试断言：**
  - 所有记录满足全部筛选条件
  - 组合查询结果准确
- **实际结果：** ✅

### TC012 - 空考位详情导出 - 正常导出

- **状态：** [待填写：✅/❌/⚠️]
- **接口：** POST `/manager/identity/cxtj/kkwxqdc`
- **测试目标：** 验证Excel导出功能
- **请求参数：**
```json
{
  "ksjhbh": "TEST2025001",
  "ccm": "001"
}
```
- **预期结果：**
  - 返回有效的文件路径
  - 生成的Excel文件包含正确数据
  - 文件格式为xlsx
- **测试断言：**
  - `response.data.dclj` 不为空
  - 文件路径有效且可访问
  - Excel文件内容正确
- **实际结果：** [待填写]

### 🔴 失败的测试

### TC013 - 空考位统计查询 - 必填参数缺失

- **状态：** [待填写：✅/❌/⚠️]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证必填参数校验
- **请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10
}
```
- **预期结果：** 400 Bad Request
- **错误消息：** "考试计划编号不能为空"
- **测试断言：**
  - 状态码 = 400
  - 错误信息明确
- **实际结果：** [待填写]
- **失败原因：** [待填写]

### TC014 - 空考位统计查询 - 无效分页参数

- **状态：** [待填写：✅/❌/⚠️]
- **接口：** POST `/manager/identity/cxtj/kkwtj`
- **测试目标：** 验证分页参数校验
- **请求参数：**
```json
{
  "pageNum": 0,
  "pageSize": -1,
  "ksjhbh": "2025001"
}
```
- **预期结果：** 400 Bad Request
- **错误消息：** "分页参数无效"
- **测试断言：**
  - 状态码 = 400
  - 参数校验生效
- **实际结果：** [待填写]
- **失败原因：** [待填写]

---

## 边界测试用例

### TC018 - 空数据查询

- **测试目标：** 验证无数据时的响应
- **请求参数：** 不存在的考试计划编号
- **预期结果：** 返回空列表，统计信息为0
- **状态：** [待填写]

---

## 兼容性测试用例

### TC028 - 历史数据兼容性

- **测试目标：** 验证历史数据查询兼容性
- **测试数据：** 老版本格式的数据
- **预期结果：** 兼容处理或明确提示
- **状态：** [待填写]

---

## 问题和缺陷

### 严重问题 🔴

### [待填写]问题#1：[问题标题]

- **严重性：** 严重
- **端点：** [相关接口]
- **描述：** [问题详细描述]
- **影响：** [对业务的影响]
- **建议：** [解决建议]

### 高优先级问题 🟠

### [待填写]问题#2：[问题标题]

- **严重性：** 高
- **端点：** [相关接口]
- **描述：** [问题详细描述]
- **影响：** [对业务的影响]
- **建议：** [解决建议]

### 中等优先级问题 🟡

### [待填写]问题#3：[问题标题]

- **严重性：** 中等
- **端点：** [相关接口]
- **描述：** [问题详细描述]
- **影响：** [对业务的影响]
- **建议：** [解决建议]

---

## 数据验证结果

### 输入验证测试

- [待填写：✅/❌] 必填字段验证
- [待填写：✅/❌] 分页参数验证
- [待填写：✅/❌] 考试计划编号格式验证
- [待填写：✅/❌] SQL注入防护
- [待填写：✅/❌] XSS防护

### 输出验证测试

- [待填写：✅/❌] 响应格式验证
- [待填写：✅/❌] 数据类型一致性
- [待填写：✅/❌] 分页信息正确性
- [待填写：✅/❌] 统计摘要准确性
- [待填写：✅/❌] Excel导出格式正确性

### 业务逻辑验证

- [待填写：✅/❌] 考场统计逻辑正确
- [待填写：✅/❌] 上报状态计算准确
- [待填写：✅/❌] 异常类型筛选有效
- [待填写：✅/❌] 关联表数据一致
- [待填写：✅/❌] 逻辑考场号处理正确

---

## 建议

### 需要立即采取的行动

1. [待填写：基于测试结果的紧急问题]
2. [待填写：安全漏洞修复]
3. [待填写：数据一致性问题]

### 短期改进

1. [待填写：性能优化建议]
2. [待填写：用户体验改进]
3. [待填写：错误处理优化]

### 长期增强

1. [待填写：架构优化建议]
2. [待填写：监控和日志完善]
3. [待填写：自动化测试扩展]

---

## 测试数据和工件

### 测试集合

- [空考位统计功能测试集合](./collections/kkw-api-tests.json)
- [测试数据集](./data/kkw-test-data.sql)
- [环境配置](./environments/local-dev.json)

### 数据库测试数据

```sql
-- 测试用考场数据
INSERT INTO ks_kcxx (id, kcbh, kcmc, ksjhbh, ccm, kmmc, bzhkcmc, bzhkcid, ljkcbh, kdbh, bzhkdid, sczt, create_time, update_time) VALUES
('TEST_KCXX001', '101', '101考场', 'TEST2025001', '001', '数学', '第一教学楼101教室', 'TEST_KC001', 'LJ101', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX002', '102', '102考场', 'TEST2025001', '001', '数学', '第一教学楼102教室', 'TEST_KC002', 'LJ102', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX003', '103', '103考场', 'TEST2025001', '001', '数学', '第一教学楼103教室', 'TEST_KC003', 'LJ103', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX004', '201', '201考场', 'TEST2025001', '001', '数学', '第一教学楼201教室', 'TEST_KC004', 'LJ201', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX005', '202', '202考场', 'TEST2025001', '001', '数学', '第一教学楼202教室', 'TEST_KC005', 'LJ202', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX006', '301', '301考场', 'TEST2025001', '002', '英语', '第二教学楼101教室', 'TEST_KC006', 'LJ301', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX007', '302', '302考场', 'TEST2025001', '002', '英语', '第二教学楼102教室', 'TEST_KC007', 'LJ302', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX008', '303', '303考场', 'TEST2025001', '002', '英语', '第二教学楼103教室', 'TEST_KC008', 'LJ303', 'KD001', 'BZHKD001', '0', NOW(), NOW());


-- 测试用空考位消息数据
INSERT INTO ks_kkw_msg (id, ksjhbh, ccm, bzhkdid, bzhkcid, ljkch, kch, ks_zkzh, ks_bpzwh, ks_sjzwh, rcbz, timestamp, czsj, sczt, create_time, update_time) VALUES
-- 101考场的空考位消息（已上报）- 对应考生入场信息中的王五（未入场，缺考）
('TEST_KKW001', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC001', 'LJ101', '101', '202501003', '03', '03', '6', '2025-07-29 09:00:00', '2025-07-29 09:00:00', '0', NOW(), NOW()),
-- 101考场额外的空考位消息 - 使用已有考生张三的准考证号作为示例
('TEST_KKW002', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC001', 'LJ101', '101', '202501001', '04', '04', '7', '2025-07-29 09:01:00', '2025-07-29 09:01:00', '0', NOW(), NOW()),

-- 102考场的空考位消息（已上报）- 对应考生入场信息中的钱七（未入场，缺考）
('TEST_KKW003', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC002', 'LJ102', '102', '202501005', '02', '02', '6', '2025-07-29 09:02:00', '2025-07-29 09:02:00', '0', NOW(), NOW()),
-- 102考场额外的空考位消息 - 使用已有考生赵六的准考证号作为示例
('TEST_KKW004', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC002', 'LJ102', '102', '202501004', '03', '03', '7', '2025-07-29 09:03:00', '2025-07-29 09:03:00', '0', NOW(), NOW()),

-- 301考场的空考位消息（已上报）- 对应考生入场信息中的吴十（未入场，缺考）
('TEST_KKW005', 'TEST2025001', '002', 'BZHKD001', 'TEST_KC006', 'LJ301', '301', '202501008', '02', '02', '6', '2025-07-29 11:00:00', '2025-07-29 11:00:00', '0', NOW(), NOW()),
-- 301考场额外的空考位消息 - 使用已有考生周九的准考证号作为示例
('TEST_KKW006', 'TEST2025001', '002', 'BZHKD001', 'TEST_KC006', 'LJ301', '301', '202501007', '03', '03', '7', '2025-07-29 11:01:00', '2025-07-29 11:01:00', '0', NOW(), NOW()),

-- 103考场的异常类型测试数据 - 使用已有考生孙八的准考证号
('TEST_KKW007', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC003', 'LJ103', '103', '202501006', '02', '03', '2', '2025-07-29 09:05:00', '2025-07-29 09:05:00', '0', NOW(), NOW()),
-- 103考场另一个异常类型测试数据 - 使用已有考生李四的准考证号
('TEST_KKW008', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC003', 'LJ103', '103', '202501002', '03', '02', '4', '2025-07-29 09:06:00', '2025-07-29 09:06:00', '0', NOW(), NOW()),

-- 201考场的异常类型测试数据 - 使用已有考生张三和李四的准考证号
('TEST_KKW009', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC004', 'LJ201', '201', '202501001', '01', '01', '1', '2025-07-29 09:07:00', '2025-07-29 09:07:00', '0', NOW(), NOW()),
('TEST_KKW010', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC004', 'LJ201', '201', '202501002', '02', '02', '5', '2025-07-29 09:08:00', '2025-07-29 09:08:00', '0', NOW(), NOW());

-- 测试用教室基本信息
INSERT INTO cs_jsjbxx (jsh, jsmc, bzhkcid, xqh, jxlh, szlc, zws, sczt, create_time, update_time) VALUES
('TEST_JS001', '第一教学楼101教室', 'TEST_KC001', 'XQ1', 'JXL001', '1', 30, '0', NOW(), NOW()),
('TEST_JS002', '第一教学楼102教室', 'TEST_KC002', 'XQ1', 'JXL001', '1', 30, '0', NOW(), NOW()),
('TEST_JS003', '第一教学楼103教室', 'TEST_KC003', 'XQ1', 'JXL001', '1', 30, '0', NOW(), NOW()),
('TEST_JS004', '第一教学楼201教室', 'TEST_KC004', 'XQ1', 'JXL001', '2', 30, '0', NOW(), NOW()),
('TEST_JS005', '第一教学楼202教室', 'TEST_KC005', 'XQ1', 'JXL001', '2', 30, '0', NOW(), NOW()),
('TEST_JS006', '第二教学楼101教室', 'TEST_KC006', 'XQ1', 'JXL002', '1', 25, '0', NOW(), NOW()),
('TEST_JS007', '第二教学楼102教室', 'TEST_KC007', 'XQ1', 'JXL002', '1', 25, '0', NOW(), NOW()),
('TEST_JS008', '第二教学楼103教室', 'TEST_KC008', 'XQ1', 'JXL002', '1', 25, '0', NOW(), NOW());

-- 测试用考生入场信息
INSERT INTO ks_ksrcxx (ksrcbh, ksjhbh, ccm, zkzh, ksxm, sfzh, ksh, kcbh, ljkcbh, zwh, sfrc, rcsj, bzhkcid, create_time, update_time) VALUES
('TEST_RC001', 'TEST2025001', '001', '202501001', '张三', '110101199001011234', 'KS001', '101', 'LJ101', '01', '1', '20250729 083000', 'TEST_KC001',  NOW(), NOW()),
('TEST_RC002', 'TEST2025001', '001', '202501002', '李四', '110101199001021234', 'KS002', '101', 'LJ101', '02', '1', '20250729 083100', 'TEST_KC001', NOW(), NOW()),
('TEST_RC003', 'TEST2025001', '001', '202501003', '王五', '110101199001031234', 'KS003', '101', 'LJ101', '03', '0', NULL, 'TEST_KC001',  NOW(), NOW()),
('TEST_RC004', 'TEST2025001', '001', '202501004', '赵六', '110101199001041234', 'KS004', '102', 'LJ102', '01', '1', '20250729 083200', 'TEST_KC002',  NOW(), NOW()),
('TEST_RC005', 'TEST2025001', '001', '202501005', '钱七', '110101199001051234', 'KS005', '102', 'LJ102', '02', '0', NULL, 'TEST_KC002',  NOW(), NOW()),
('TEST_RC006', 'TEST2025001', '001', '202501006', '孙八', '110101199001061234', 'KS006', '103', 'LJ103', '01', '1', '20250729 083300', 'TEST_KC003',  NOW(), NOW()),
('TEST_RC007', 'TEST2025001', '002', '202501007', '周九', '110101199001071234', 'KS007', '301', 'LJ301', '01', '1', '20250729 103000', 'TEST_KC006',  NOW(), NOW()),
('TEST_RC008', 'TEST2025001', '002', '202501008', '吴十', '110101199001081234', 'KS008', '301', 'LJ301', '02', '0', NULL, 'TEST_KC006',NOW(), NOW());


```

### 截图和证据

- [接口测试截图](./screenshots/)
- [性能监控图表](./performance/)
- [Excel导出样例](./exports/)

---

## 附录

### 测试配置

```yaml
# 数据库配置
datasource:
  url: ***********************************************************************************
  username: root
  password: BoojuxMysql
  driver-class-name: com.mysql.jdbc.Driver
```

### 异常类型映射

| 代码 | 描述 | 测试重点 |
| --- | --- | --- |
| 1 | 误识别 | 筛选准确性 |
| 2 | 坐错他人位置 | 数据关联正确性 |
| 3 | 实际未参加考试 | 状态判断逻辑 |
| 4 | 他人坐错位置 | 座位冲突处理 |
| 5 | 人工核验 | 手动处理流程 |
| 6 | 缺考（空位） | 核心业务逻辑 |
| 7 | 无编排 | 边界情况处理 |

---

**报告生成时间：** [待填写：执行测试后自动更新]

**生成工具：** JUnit5 + MockMvc + EasyExcel

**报告版本：** 1.0
