openapi: 3.0.3
info:
  title: 空考位统计查询接口文档
  description: 提供空考位统计查询和详情查询的API接口
  version: 1.0.0
  contact:
    name: xcwlkj.com
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 开发环境

paths:
  /manager/identity/cxtj/kkwtj:
    post:
      tags:
        - 查询统计
      summary: 空考位统计查询
      description: 查询空考位统计信息，支持分页查询
      operationId: getEmptySeatsStatistics
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KkwTjReqModel'
            example:
              ksjhbh: "2025001"
              ccm: "001"
              sbzt: "1"
              pageNum: 1
              pageSize: 10
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KkwTjWrapper'
              example:
                code: "200"
                message: "操作成功"
                result:
                  pageInfo:
                    pageNum: 1
                    pageSize: 10
                    total: 50
                    pages: 5
                    list:
                      - kch: "101"
                        csmc: "第一教学楼101"
                        sfsb: "1"
                        sbsj: "2025-07-29 10:30:00"
                        bzhkcid: "KC001"
                        ljkcbh: "LJ001"
                  summary:
                    total: 100
                    ysb: 80
                    wsb: 20
                    reportRate: "80%"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorWrapper'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorWrapper'

  /manager/identity/cxtj/kkwxq:
    post:
      tags:
        - 查询统计
      summary: 空考位详情页面查询
      description: 查询空考位详细信息，支持多条件筛选和分页
      operationId: getEmptySeatsDetails
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KkwXqReqModel'
            example:
              ksjhbh: "2025001"
              ccm: "001"
              yclx: "6"
              zkzh: "202501001"
              kch: "101"
              pageNum: 1
              pageSize: 10
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KkwXqWrapper'
              example:
                code: "200"
                message: "操作成功"
                result:
                  pageInfo:
                    pageNum: 1
                    pageSize: 10
                    total: 25
                    pages: 3
                    list:
                      - kch: "101"
                        csmc: "第一教学楼101"
                        zkzh: "202501001"
                        xm: "张三"
                        zwh: "01"
                        yclx: "缺考（空位）"
                        zdsbsj: "2025-07-29 10:30:00"
                        sfsb: "1"
                        sbsjsj: "2025-07-29 10:35:00"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorWrapper'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorWrapper'

components:
  schemas:
    KkwTjReqModel:
      type: object
      required:
        - ksjhbh
        - pageNum
        - pageSize
      properties:
        ksjhbh:
          type: string
          description: 考试计划编号
          example: "2025001"
        ccm:
          type: string
          description: 考试场次
          example: "001"
        sbzt:
          type: string
          description: 终端上报情况 0-否 1-是
          enum: ["0", "1"]
          example: "1"
        pageNum:
          type: integer
          description: 页码
          minimum: 1
          example: 1
        pageSize:
          type: integer
          description: 每页大小
          minimum: 1
          maximum: 100
          example: 10

    KkwXqReqModel:
      type: object
      required:
        - ksjhbh
        - pageNum
        - pageSize
      properties:
        ksjhbh:
          type: string
          description: 考试计划编号
          example: "2025001"
        ccm:
          type: string
          description: 考试场次
          example: "001"
        yclx:
          type: string
          description: 异常类型 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工核验 6-缺考（空位） 7-无编排
          enum: ["1", "2", "3", "4", "5", "6", "7"]
          example: "6"
        zkzh:
          type: string
          description: 准考证号
          example: "202501001"
        kch:
          type: string
          description: 考场号
          example: "101"
        pageNum:
          type: integer
          description: 页码
          minimum: 1
          example: 1
        pageSize:
          type: integer
          description: 每页大小
          minimum: 1
          maximum: 100
          example: 10

    KkwTjItem:
      type: object
      properties:
        kch:
          type: string
          description: 考场号
          example: "101"
        csmc:
          type: string
          description: 场所名称
          example: "第一教学楼101"
        sfsb:
          type: string
          description: 终端上报情况 0-否 1-是
          enum: ["0", "1"]
          example: "1"
        sbsj:
          type: string
          description: 终端上报时间
          format: date-time
          example: "2025-07-29 10:30:00"
        bzhkcid:
          type: string
          description: 考场ID（用于详情查询）
          example: "KC001"
        ljkcbh:
          type: string
          description: 逻辑考场编号
          example: "LJ001"

    KkwTjInfoVO:
      type: object
      properties:
        total:
          type: integer
          description: 总考场数
          example: 100
        ysb:
          type: integer
          description: 已上报考场数
          example: 80
        wsb:
          type: integer
          description: 未上报考场数
          example: 20
        reportRate:
          type: string
          description: 上报比例
          example: "80%"

    KkwTjXqVO:
      type: object
      properties:
        kch:
          type: string
          description: 考场号
          example: "101"
        csmc:
          type: string
          description: 场所名称
          example: "第一教学楼101"
        zkzh:
          type: string
          description: 准考证号
          example: "202501001"
        xm:
          type: string
          description: 姓名
          example: "张三"
        zwh:
          type: string
          description: 座位号
          example: "01"
        yclx:
          type: string
          description: 异常描述
          example: "缺考（空位）"
        zdsbsj:
          type: string
          description: 终端上报时间
          format: date-time
          example: "2025-07-29 10:30:00"
        sfsb:
          type: string
          description: 上报上级平台情况
          enum: ["0", "1"]
          example: "1"
        sbsjsj:
          type: string
          description: 上报上级平台上报时间
          format: date-time
          example: "2025-07-29 10:35:00"

    PageInfo:
      type: object
      properties:
        pageNum:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页大小
          example: 10
        total:
          type: integer
          description: 总记录数
          example: 50
        pages:
          type: integer
          description: 总页数
          example: 5
        list:
          type: array
          description: 数据列表
          items:
            oneOf:
              - $ref: '#/components/schemas/KkwTjItem'
              - $ref: '#/components/schemas/KkwTjXqVO'

    KkwTjRespModel:
      type: object
      properties:
        pageInfo:
          allOf:
            - $ref: '#/components/schemas/PageInfo'
            - type: object
              properties:
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/KkwTjItem'
        summary:
          $ref: '#/components/schemas/KkwTjInfoVO'

    KkwXqRespModel:
      type: object
      properties:
        pageInfo:
          allOf:
            - $ref: '#/components/schemas/PageInfo'
            - type: object
              properties:
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/KkwTjXqVO'

    Wrapper:
      type: object
      properties:
        code:
          type: string
          description: 响应码
          example: "200"
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        result:
          type: object
          description: 响应数据

    KkwTjWrapper:
      allOf:
        - $ref: '#/components/schemas/Wrapper'
        - type: object
          properties:
            result:
              $ref: '#/components/schemas/KkwTjRespModel'

    KkwXqWrapper:
      allOf:
        - $ref: '#/components/schemas/Wrapper'
        - type: object
          properties:
            result:
              $ref: '#/components/schemas/KkwXqRespModel'

    ErrorWrapper:
      allOf:
        - $ref: '#/components/schemas/Wrapper'
        - type: object
          properties:
            code:
              type: string
              example: "400"
            message:
              type: string
              example: "请求参数错误"
            result:
              type: object
              nullable: true
              example: null

tags:
  - name: 查询统计
    description: 空考位相关查询统计接口
