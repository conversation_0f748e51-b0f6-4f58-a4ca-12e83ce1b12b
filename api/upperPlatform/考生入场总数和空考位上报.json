{"openapi": "3.0.1", "info": {"title": "new_xiaoji_check_platform", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/ksyw/revIdvData/sbkkw": {"post": {"summary": "空考位上报", "deprecated": false, "description": "sm4加密 使用固定秘钥hssfhy@2025~$#@!\nencrptJson解密后字符串如下：\n{\n  \"dataArray\": [\n    {\n      \"examPlanCode\": \"考试编号\",\n      \"orgCode\": \"机构编号\",\n      \"sn\": \"设备序列号\",\n      \"devType\": \"172\",\n      \"ccm\": \"场次码\",\n      \"bzhkcid\": \"标准化考场id\",\n      \"zcqswzm\": \"座次起始位置码\",\n      \"zwbjfsm\": \"座位布局方式码\",\n      \"zwplfsm\": \"座位排列方式码\",\n      \"kwlb\": [\n        {\n          \"ljkch\": \"逻辑考场号考点唯一\",\n          \"kch\": \"考场号\",\n          \"kzwhxq\": [\n            {\n              \"zkzh\": \"准考证号\",\n              \"bpzwh\": \"编排数据中考生座位号\",\n              \"sjzwh\": \"实际座位号\",\n              \"rcbz\": \"入场备注1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工核验 6-缺考（空位）7-无编排\",\n              \"sjyxj\": \"数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场\",\n              \"ysbhyxx\": {\n                \"yzsj\": \"验证时间yyyy-MM-dd HH:mm:ss\",\n                \"hyjg\": \"核验结果 1-通过 0-不通过\",\n                \"kddm\": \"考点代码\",\n                \"szjg\": \"刷证结果 1-通过 0-不通过\",\n                \"rlsbjg\": \"人脸识别结果 1-通过 0-不通过\",\n                \"zwrzjg\": \"指纹认证结果 1-通过 0-不通过\"\n              },\n              \"ysbrcxx\": {\n                \"sbsj\": \"上报时间yyyy-MM-dd HH:mm:ss\",\n                \"kddm\": \"考点代码\",\n                \"rczt\": \" 入场状态 0-人工审核不通过，1-人工审核通过，2-缺考\"\n              }\n            }\n          ]\n        }\n      ]\n    }\n  ],\n  \"czsj\": \"操作时间yyyy-MM-dd HH:mm:ss\",\n  \"timestamp\": 1643179973\n}", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"encrptJson": {"type": "string", "description": "字符串需要sm4加密"}, "sbxlh": {"type": "string", "description": "设备序列号"}}, "required": ["encrptJson", "sbxlh"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/ksyw/revIdvData/ksrczs": {"post": {"summary": "考生入场总数", "deprecated": false, "description": "sm4加密 使用固定秘钥hssfhy@2025~$#@!\nencrptJson解密后字符串如下\n{\n\"rczs\": [\n        {\n        \"sbsj\": \"上报时间yyyy-MM-dd HH:mm:ss\",\n        \"kslb\": [\n            {\n            \"zkzh\": \"准考证号\"\n            }\n        ],\n        \"kddm\": \"考点代码\",\n        \"kch\": \"考场号\",\n        \"rczs\": \"入场总数\",\n        \"ljkcbh\": \"逻辑考场编号\"\n    }\n],\n\"exam\": {\n        \"examPlanCode\": \"考试编号\",\n        \"examSeqCode\": \"场次编号\",\n        \"msgId\": \"消息uuid\"\n    }\n}", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"encrptJson": {"type": "string", "description": "字符串需要sm4加密"}, "sbxlh": {"type": "string"}}, "required": ["encrptJson", "sbxlh"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [], "security": []}