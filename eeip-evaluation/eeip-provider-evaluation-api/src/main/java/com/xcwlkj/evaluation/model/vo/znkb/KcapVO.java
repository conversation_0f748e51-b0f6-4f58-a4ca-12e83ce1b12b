/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.vo.znkb;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 课程安排vo
 * <AUTHOR>
 * @version $Id: KcapVO.java, v 0.1 2023年02月23日 14时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KcapVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 课程列表 */
    private List<KcItemVO> kcList;
    /** 总条数 */
    private Integer totalRows;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
