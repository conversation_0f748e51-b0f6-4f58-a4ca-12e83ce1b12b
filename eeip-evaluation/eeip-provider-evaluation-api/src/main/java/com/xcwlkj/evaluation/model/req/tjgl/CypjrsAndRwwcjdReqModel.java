/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.req.tjgl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 参与评价人数及任务完成进度请求
 * <AUTHOR>
 * @version $Id: CypjrsAndRwwcjdReqModel.java, v 0.1 2023年02月23日 13时51分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CypjrsAndRwwcjdReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 学期编号 */
    @NotBlank(message = "学期编号不能为空")
    private String xqbh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}