/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.evaluation.service.hystrix;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.evaluation.service.AssociateFeignApi;

import com.xcwlkj.evaluation.model.dto.associate.AddDTO;
import com.xcwlkj.evaluation.model.dto.associate.DeleteDTO;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 * @version $Id: AssociateFeignHystrix.java, v 0.1 2025年06月04日 13时09分 xcwlkj.com Exp $
 */
@Component
public class AssociateFeignHystrix implements AssociateFeignApi{

    /** 
     * @see com.xcwlkj.evaluation.service.AssociateFeignApi#add(com.xcwlkj.evaluation.model.dto.associate.AddDTO)
     */
    @Override
    public Wrapper<Void> add(AddDTO addDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.evaluation.service.AssociateFeignApi#delete(com.xcwlkj.evaluation.model.dto.associate.DeleteDTO)
     */
    @Override
    public Wrapper<Void> delete(DeleteDTO deleteDto) {
        return WrapMapper.error();
    }
}
