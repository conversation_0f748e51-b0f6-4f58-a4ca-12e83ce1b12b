/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.vo.tjgl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 教师评价结果列表查询vo
 * <AUTHOR>
 * @version $Id: JspjjglbCxVO.java, v 0.1 2023年02月23日 17时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JspjjglbCxVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 教师评价结果列表查询返回列表 */
    private List<JspjjglbCxRespItemVO> jspjjglbCxRespList;
    /** 总条数 */
    private Integer totalRows;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
