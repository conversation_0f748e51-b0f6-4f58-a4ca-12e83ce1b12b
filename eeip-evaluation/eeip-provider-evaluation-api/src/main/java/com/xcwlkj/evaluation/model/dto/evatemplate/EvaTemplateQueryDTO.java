/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.dto.evatemplate;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;


/**
 * 查询评价模板dto
 * <AUTHOR>
 * @version $Id: EvaTemplateQueryDTO.java, v 0.1 2022年11月01日 09时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class EvaTemplateQueryDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 模板名称 */
    private String mbmc;
    /** 业务编号 */
    private String ywbh;
    /** 模板编号 */
    private String mbbh;
    /** 第几页 */
    @NotNull(message = "第几页不能为空")
    private Integer pageNum;
    /** 每页显示数 */
    @NotNull(message = "每页显示数不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
