/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.baseinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.evaluation.model.vo.baseinfo.CameraItemVO;
import java.util.List;


/**
 * 查询教室镜头信息响应
 * <AUTHOR>
 * @version $Id: CameraListRespModel.java, v 0.1 2022年10月27日 18时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CameraListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 镜头列表 */
    private List<CameraItemVO> cameraList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}