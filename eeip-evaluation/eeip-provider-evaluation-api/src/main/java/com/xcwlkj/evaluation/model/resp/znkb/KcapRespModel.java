/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.znkb;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.evaluation.model.vo.znkb.KcItemVO;
import java.util.List;


/**
 * 课程安排响应
 * <AUTHOR>
 * @version $Id: KcapRespModel.java, v 0.1 2023年02月23日 14时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KcapRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 课程列表 */
    private List<KcItemVO> kcList;
    /** 总条数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}