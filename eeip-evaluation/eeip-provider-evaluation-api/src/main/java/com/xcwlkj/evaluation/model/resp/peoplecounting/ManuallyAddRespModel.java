/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.peoplecounting;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 手工录入人数检测响应
 * <AUTHOR>
 * @version $Id: ManuallyAddRespModel.java, v 0.1 2023年09月13日 15时48分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ManuallyAddRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}