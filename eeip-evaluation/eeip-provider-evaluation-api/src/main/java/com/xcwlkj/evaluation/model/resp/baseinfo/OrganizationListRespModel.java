/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.baseinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.evaluation.model.vo.baseinfo.OrganizationItemVO;
import java.util.List;


/**
 * 组织机构列表响应
 * <AUTHOR>
 * @version $Id: OrganizationListRespModel.java, v 0.1 2022年10月25日 09时39分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrganizationListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 组织列表 */
    private List<OrganizationItemVO> organizationList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}