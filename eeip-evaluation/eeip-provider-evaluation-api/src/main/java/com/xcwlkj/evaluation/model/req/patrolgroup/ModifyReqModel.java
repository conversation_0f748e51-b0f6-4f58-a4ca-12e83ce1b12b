/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.req.patrolgroup;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.xcwlkj.evaluation.model.dto.patrolgroup.XkzzzXgItemDTO;
import java.util.List;
import com.xcwlkj.evaluation.model.dto.patrolgroup.XkzcyXgItemDTO;
import com.xcwlkj.evaluation.model.dto.patrolgroup.JsfwXgItemDTO;
import com.xcwlkj.evaluation.model.dto.patrolgroup.RoomfwXgItemDTO;
import com.xcwlkj.evaluation.model.dto.patrolgroup.GlpjmbXgItemDTO;


/**
 * 巡课组修改请求
 * <AUTHOR>
 * @version $Id: ModifyReqModel.java, v 0.1 2022年11月09日 15时23分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModifyReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 巡课组编号 */
    @NotBlank(message = "巡课组编号不能为空")
    private String xkzbh;
    /** 巡课组名称 */
    @NotBlank(message = "巡课组名称不能为空")
    private String xkzmc;
    /** 巡课说明 */
    private String xksm;
    /** 巡课组组长修改列表 */
    @NotNull(message = "巡课组组长修改列表不能为空")
    private List<XkzzzXgItemDTO> xkzzzXgList;
    /** 巡课组成员修改列表 */
    @NotNull(message = "巡课组成员修改列表不能为空")
    private List<XkzcyXgItemDTO> xkzcyXgList;
    /** 教师范围修改列表 */
    @NotNull(message = "教师范围修改列表不能为空")
    private List<JsfwXgItemDTO> jsfwXgList;
    /** 教室范围修改列表 */
    @NotNull(message = "教室范围修改列表不能为空")
    private List<RoomfwXgItemDTO> roomfwXgList;
    /** 关联评价模板修改列表 */
    @NotNull(message = "关联评价模板修改列表不能为空")
    private List<GlpjmbXgItemDTO> glpjmbXgList;
    /** 是否报告 */
    @NotBlank(message = "是否报告不能为空")
    private String sfbg;
    /** 开始时间 */
    private String kssj;
    /** 结束时间 */
    private String jssj;
    /** 报告对象类型 */
    private String bgdxlx;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}