/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.req.evatemplate;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import com.xcwlkj.evaluation.model.dto.evatemplate.PjxModifyReqItemDTO;
import java.util.List;


/**
 * 修改评价模板请求
 * <AUTHOR>
 * @version $Id: EvaTemplateModifyReqModel.java, v 0.1 2022年11月02日 22时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EvaTemplateModifyReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 模板编号 */
    @NotBlank(message = "模板编号不能为空")
    private String mbbh;
    /** 模板名称 */
    private String mbmc;
    /** 模板描述 */
    private String mbms;
    /** 评价项请求列表  ；若修改评价项则必传 */
    private List<PjxModifyReqItemDTO> pjxModifyReqList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}