/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.baseinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.evaluation.model.vo.baseinfo.StaffItemVO;
import java.util.List;


/**
 * 查询教职工信息响应
 * <AUTHOR>
 * @version $Id: StaffListRespModel.java, v 0.1 2023年02月14日 16时00分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 教职工列表 */
    private List<StaffItemVO> staffList;
    /** 总条数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}