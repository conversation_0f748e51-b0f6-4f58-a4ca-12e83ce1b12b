/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.dto.patrolgroup;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 巡课组新增dto
 * <AUTHOR>
 * @version $Id: AddDTO.java, v 0.1 2022年11月07日 17时08分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class AddDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 巡课组名称 */
    @NotBlank(message = "巡课组名称不能为空")
    private String xkzmc;
    /** 巡课说明 */
    private String xksm;
    /** 巡课组组长新增列表 */
    @NotNull(message = "巡课组组长新增列表不能为空")
    private List<XkzzzXzItemDTO> xkzzzXzList;
    /** 巡课组成员新增列表 */
    @NotNull(message = "巡课组成员新增列表不能为空")
    private List<XkzcyXzItemDTO> xkzcyXzList;
    /** 教师范围新增列表 */
    @NotNull(message = "教师范围新增列表不能为空")
    private List<JsfwXzItemDTO> jsfwXzList;
    /** 教室范围新增列表 */
    @NotNull(message = "教室范围新增列表不能为空")
    private List<RoomfwXzItemDTO> roomfwXzList;
    /** 关联评价模板新增列表 */
    @NotNull(message = "关联评价模板新增列表不能为空")
    private List<GlpjmbXzItemDTO> glpjmbXzList;
    /** 是否报告 */
    @NotBlank(message = "是否报告不能为空")
    private String sfbg;
    /** 开始时间 */
    private String kssj;
    /** 结束时间 */
    private String jssj;
    /** 报告对象类型 */
    private String bgdxlx;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
