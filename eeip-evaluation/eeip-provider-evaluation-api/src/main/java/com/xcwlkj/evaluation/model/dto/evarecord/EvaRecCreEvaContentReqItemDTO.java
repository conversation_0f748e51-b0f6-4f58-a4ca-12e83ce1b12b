/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.dto.evarecord;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 生成评价记录dto
 * <AUTHOR>
 * @version $Id: EvaRecCreEvaContentReqItemDTO.java, v 0.1 2022年11月09日 16时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class EvaRecCreEvaContentReqItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 评价项编号 */
    @NotBlank(message = "评价项编号不能为空")
    private String pjxbh;
    /** 评价项类型  1-单选 / 2-多选 / 3-打分 */
    @NotBlank(message = "评价项类型  1-单选 / 2-多选 / 3-打分不能为空")
    private String pjxlx;
    /** 评价项选项编号 ； 如果 评价项类型为 1-单选 / 2-多选 时必传 */
    private String xxbh;
    /** 评价分值 评价项类型为 3-打分 时必传，如果 评价项类型为 1-单选 / 2-多选 时其中选项对应的有分值则也必传 */
    private Integer pjfz;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
