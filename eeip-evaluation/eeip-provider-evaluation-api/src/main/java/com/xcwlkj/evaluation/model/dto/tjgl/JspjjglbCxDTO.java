/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.dto.tjgl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 教师评价结果列表查询dto
 * <AUTHOR>
 * @version $Id: JspjjglbCxDTO.java, v 0.1 2023年02月23日 17时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JspjjglbCxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 学期编号 */
    @NotBlank(message = "学期编号不能为空")
    private String xqbh;
    /** 第几页 */
    @NotNull(message = "第几页不能为空")
    private Integer pageNum;
    /** 每页显示数量 */
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
