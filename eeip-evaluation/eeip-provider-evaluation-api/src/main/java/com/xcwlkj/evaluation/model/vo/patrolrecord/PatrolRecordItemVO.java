/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.vo.patrolrecord;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 查询巡课记录vo
 * <AUTHOR>
 * @version $Id: PatrolRecordItemVO.java, v 0.1 2023年02月09日 10时28分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class PatrolRecordItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 巡课记录编号 */
    private String xkjlbh;
    /** 巡课人员编号 */
    private String xkrybh;
    /** 巡课人员姓名 */
    private String xkryxm;
    /** 巡课人员类型 */
    private String xkrylx;
    /** 巡课组编号 */
    private String xkzbh;
    /** 学期编号 */
    private String xqbh;
    /** 课程编号 */
    private String kcbh;
    /** 课程名称 */
    private String kcmc;
    /** 排课编号 */
    private String pkbh;
    /** 排课名称 */
    private String pkmc;
    /** 巡课时间 */
    private String xksj;
    /** 被巡教师列表 */
    private List<BxjsItemVO> bxjsList;
    /** 是否评价 */
    private Boolean sfpj;
    /** 评价记录编号 */
    private String pjjlbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
