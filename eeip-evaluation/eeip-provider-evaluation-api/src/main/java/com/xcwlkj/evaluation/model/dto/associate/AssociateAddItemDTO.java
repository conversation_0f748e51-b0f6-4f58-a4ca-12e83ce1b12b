/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.dto.associate;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 业务关联-新增dto
 * <AUTHOR>
 * @version $Id: AssociateAddItemDTO.java, v 0.1 2025年06月04日 13时15分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class AssociateAddItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 评价模板编号 */
    @NotBlank(message = "评价模板编号不能为空")
    private String pjmbbh;
    /** 评价模板名称 */
    @NotBlank(message = "评价模板名称不能为空")
    private String pjmbmc;
    /**  */
    @NotBlank(message = "不能为空")
    private String ywbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
