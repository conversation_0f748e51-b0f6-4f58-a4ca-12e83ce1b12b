/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.req.evarecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.xcwlkj.evaluation.model.dto.evarecord.EvaRecCreBeEvaObjReqItemDTO;

import java.util.Date;
import java.util.List;
import com.xcwlkj.evaluation.model.dto.evarecord.EvaRecCreEvaContentReqItemDTO;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 生成评价记录请求
 * <AUTHOR>
 * @version $Id: EvaRecordCreateReqModel.java, v 0.1 2022年11月09日 16时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EvaRecordCreateReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 评价模板编号 */
    @NotBlank(message = "评价模板编号不能为空")
    private String pjmbbh;
    /** 业务编号 */
    @NotBlank(message = "业务编号不能为空")
    private String ywbh;
    /** 业务类型 1 督导评价；2 同行评价；3 学生评价；4 巡课； */
    @NotBlank(message = "业务类型 1 督导评价；2 同行评价；3 学生评价；4 巡课；不能为空")
    private String ywlx;
    /** 评价对象类型 */
    @NotBlank(message = "评价对象类型不能为空")
    private String pjdxlx;
    /** 评价对象编号 */
    @NotBlank(message = "评价对象编号不能为空")
    private String pjdxbh;
    /** 评价对象名称 */
    @NotBlank(message = "评价对象名称不能为空")
    private String pjdxmc;
    /** 总评分 */
    @NotNull(message = "总评分不能为空")
    private Integer zpf;
    /** 评价时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pjsj;
    /** 评语 */
    private String py;
    /** 被评对象是否可见：0 可见 ；1 不可见 */
    @NotBlank(message = "被评对象是否可见：0 可见 ；1 不可见不能为空")
    private String bpdxsfkj;
    /** 被评价对象请求列表 */
    @NotNull(message = "被评价对象请求列表不能为空")
    private List<EvaRecCreBeEvaObjReqItemDTO> evaRecCreBeEvaObjReqList;
    /** 评价内容请求列表 */
    @NotNull(message = "评价内容请求列表不能为空")
    private List<EvaRecCreEvaContentReqItemDTO> evaRecCreEvaContentReqList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}