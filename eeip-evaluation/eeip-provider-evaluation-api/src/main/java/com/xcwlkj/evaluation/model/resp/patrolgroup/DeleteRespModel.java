/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.patrolgroup;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 巡课组删除响应
 * <AUTHOR>
 * @version $Id: DeleteRespModel.java, v 0.1 2022年11月07日 17时10分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeleteRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}