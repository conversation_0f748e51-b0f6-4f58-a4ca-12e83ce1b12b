/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.dto.baseinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 查询课程表dto
 * <AUTHOR>
 * @version $Id: LessionListDTO.java, v 0.1 2023年03月01日 14时47分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class LessionListDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 业务类型 */
    @NotBlank(message = "业务类型不能为空")
    private String ywlx;
    /** 业务编号 */
    @NotBlank(message = "业务编号不能为空")
    private String ywbh;
    /** 开课学年度 */
    @NotBlank(message = "开课学年度不能为空")
    private String kkxnd;
    /** 开课学期码 */
    @NotBlank(message = "开课学期码不能为空")
    private String kkxqm;
    /** 学期开始日期 */
    @NotBlank(message = "学期开始日期不能为空")
    private String xqksrq;
    /** 学期结束日期 */
    @NotBlank(message = "学期结束日期不能为空")
    private String xqjsrq;
    /** 查询类型 */
    @NotBlank(message = "查询类型不能为空")
    private String cxlx;
    /** 查询内容 */
    private String cxnr;
    /** 周次 */
    private Integer zc;
    /** 星期 */
    private Integer xq;
    /** 节次 */
    private Integer jc;
    /** 上课状态 */
    private String skzt;
    /** 开课院系代码 */
    private String kkyxdm;
    /** 教学楼编号 */
    private String jxlbh;
    /** 是否绑定摄像头 0-否 1-是 */
    private Integer sfbdsxt;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
