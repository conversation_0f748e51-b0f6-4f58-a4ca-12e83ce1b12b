/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.dto.peoplecounting;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 查询教室人员数量dto
 * <AUTHOR>
 * @version $Id: RoomPeopleNumberDTO.java, v 0.1 2023年02月14日 13时52分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class RoomPeopleNumberDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 查询教室列表 */
    @NotNull(message = "查询教室列表不能为空")
    private List<CxjsItemDTO> cxjsList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
