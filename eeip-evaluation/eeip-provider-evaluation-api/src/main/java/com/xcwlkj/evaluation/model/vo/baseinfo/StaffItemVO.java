/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.vo.baseinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 查询教职工信息vo
 * <AUTHOR>
 * @version $Id: StaffItemVO.java, v 0.1 2023年02月14日 16时00分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class StaffItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 教师编号 */
    private String jsbh;
    /** 教师姓名 */
    private String jsxm;
    /** 职工号 */
    private String zzh;
    /** 所属组织编号 */
    private String sszzbh;
    /** 组织名称 */
    private String zzmc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
