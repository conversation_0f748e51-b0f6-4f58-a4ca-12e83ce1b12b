/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.baseinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.evaluation.model.vo.baseinfo.LessionItemVO;
import java.util.List;


/**
 * 查询课程表响应
 * <AUTHOR>
 * @version $Id: LessionListRespModel.java, v 0.1 2022年10月27日 16时05分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LessionListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 课程表列表 */
    private List<LessionItemVO> lessionList;
    /** 总条数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}