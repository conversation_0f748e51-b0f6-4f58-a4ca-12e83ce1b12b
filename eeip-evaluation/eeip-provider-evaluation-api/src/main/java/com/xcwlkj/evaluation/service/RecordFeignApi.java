/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.evaluation.service;

import com.xcwlkj.core.annotation.mock.YapiMock;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


import com.xcwlkj.evaluation.service.hystrix.RecordFeignHystrix;
import com.xcwlkj.evaluation.model.dto.record.RecordQueryDTO;
import com.xcwlkj.evaluation.model.vo.record.RecordQueryVO;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * 
 * <AUTHOR>
 * @version $Id: RecordFeignApi.java, v 0.1 2025年05月22日 15时24分 xcwlkj.com Exp $
 */
@FeignClient(value = "evaluation-service", fallback = RecordFeignHystrix.class)
public interface RecordFeignApi {

   
	/**
	 * 评价记录查询
	 * @param recordQueryDto
	 * @return
	 */
	@YapiMock(projectId="2270", returnClass = RecordQueryVO.class)
    @PostMapping(value = "/sys/evaluation/record/recordQuery")
    Wrapper<RecordQueryVO> recordQuery(@RequestBody RecordQueryDTO recordQueryDto);
}




