<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.evaluation.mapper.PktjBpdxpmMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.evaluation.model.domain.PktjBpdxpm">
        <id column="xqbh" jdbcType="VARCHAR" property="xqbh" />
        <id column="bpjdxbh" jdbcType="VARCHAR" property="bpjdxbh" />
        <result column="bpjdxmc" jdbcType="VARCHAR" property="bpjdxmc" />
        <result column="bpjdxlx" jdbcType="VARCHAR" property="bpjdxlx" />
        <result column="ddpkdf" jdbcType="DECIMAL" property="ddpkdf" />
        <result column="thpkdf" jdbcType="DECIMAL" property="thpkdf" />
        <result column="xspkdf" jdbcType="DECIMAL" property="xspkdf" />
        <result column="zhdf" jdbcType="DECIMAL" property="zhdf" />
        <result column="pmlb" jdbcType="VARCHAR" property="pmlb" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        xqbh,
        bpjdxbh,
        bpjdxmc,
        bpjdxlx,
        ddpkdf,
        thpkdf,
        xspkdf,
        zhdf,
        pmlb,
        create_time,
        update_time

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="xqbh != null and xqbh != ''">
            AND xqbh = #{xqbh,jdbcType=VARCHAR}
        </if>
        <if test="bpjdxbh != null and bpjdxbh != ''">
            AND bpjdxbh = #{bpjdxbh,jdbcType=VARCHAR}
        </if>
        <if test="bpjdxmc != null and bpjdxmc != ''">
            AND bpjdxmc = #{bpjdxmc,jdbcType=VARCHAR}
        </if>
        <if test="bpjdxlx != null and bpjdxlx != ''">
            AND bpjdxlx = #{bpjdxlx,jdbcType=VARCHAR}
        </if>
        <if test="ddpkdf != null and ddpkdf != ''">
            AND ddpkdf = #{ddpkdf,jdbcType=DECIMAL}
        </if>
        <if test="thpkdf != null and thpkdf != ''">
            AND thpkdf = #{thpkdf,jdbcType=DECIMAL}
        </if>
        <if test="xspkdf != null and xspkdf != ''">
            AND xspkdf = #{xspkdf,jdbcType=DECIMAL}
        </if>
        <if test="zhdf != null and zhdf != ''">
            AND zhdf = #{zhdf,jdbcType=DECIMAL}
        </if>
        <if test="pmlb != null and pmlb != ''">
            AND pmlb = #{pmlb,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="xqbh != null ">
            xqbh = #{xqbh,jdbcType=VARCHAR},
        </if>
        <if test="bpjdxbh != null ">
            bpjdxbh = #{bpjdxbh,jdbcType=VARCHAR},
        </if>
        <if test="bpjdxmc != null ">
            bpjdxmc = #{bpjdxmc,jdbcType=VARCHAR},
        </if>
        <if test="bpjdxlx != null ">
            bpjdxlx = #{bpjdxlx,jdbcType=VARCHAR},
        </if>
        <if test="ddpkdf != null ">
            ddpkdf = #{ddpkdf,jdbcType=DECIMAL},
        </if>
        <if test="thpkdf != null ">
            thpkdf = #{thpkdf,jdbcType=DECIMAL},
        </if>
        <if test="xspkdf != null ">
            xspkdf = #{xspkdf,jdbcType=DECIMAL},
        </if>
        <if test="zhdf != null ">
            zhdf = #{zhdf,jdbcType=DECIMAL},
        </if>
        <if test="pmlb != null ">
            pmlb = #{pmlb,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

    <insert id="batchInsert">
        insert into pktj_bpdxpm(<include refid="Base_Column_List" />)
        values
        <foreach collection="insertList" item="item" separator=",">
            (
              #{item.xqbh,jdbcType=VARCHAR},
              #{item.bpjdxbh,jdbcType=VARCHAR},
              #{item.bpjdxmc,jdbcType=VARCHAR},
              #{item.bpjdxlx,jdbcType=VARCHAR},
              #{item.ddpkdf,jdbcType=DECIMAL},
              #{item.thpkdf,jdbcType=DECIMAL},
              #{item.xspkdf,jdbcType=DECIMAL},
              #{item.zhdf,jdbcType=DECIMAL},
              #{item.pmlb,jdbcType=VARCHAR},
              #{item.createTime,jdbcType=TIMESTAMP},
              #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <update id="batchUpdateScores">
        <foreach collection="updateList" item="item" separator=";">
            <if test="item.xqbh != null and item.xqbh != '' and item.bpjdxbh != null and item.bpjdxbh != ''">
                update pktj_bpdxpm
                <set>
                    ddpkdf = #{item.ddpkdf,jdbcType=DECIMAL},
                    thpkdf = #{item.thpkdf,jdbcType=DECIMAL},
                    xspkdf = #{item.xspkdf,jdbcType=DECIMAL},
                    zhdf = #{item.zhdf,jdbcType=DECIMAL},
                    pmlb = #{item.pmlb,jdbcType=VARCHAR},
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP}
                </set>
                where xqbh = #{item.xqbh}
                and bpjdxbh = #{item.bpjdxbh}
            </if>
        </foreach>
    </update>

</mapper>
