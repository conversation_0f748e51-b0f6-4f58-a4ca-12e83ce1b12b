package com.xcwlkj.evaluation.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/** 
 * 排名类别枚举类
 */
public enum PmlbEnum {

    YX("1","优秀"),
    LH("2","良好"),
    ZD("3","中等"),
    JC("4","较差");

    private String code;
    private String desc;

    private PmlbEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return this.code;
    }
    
    public String getDesc() {
        return this.desc;
    }
    
    public static PmlbEnum get(String code) {
        for (PmlbEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }

}
