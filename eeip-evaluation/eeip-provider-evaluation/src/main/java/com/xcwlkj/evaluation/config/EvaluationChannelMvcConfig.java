/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.evaluation.config;

import com.xcwlkj.core.config.PcObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @version $Id: ThirdChannelMvcConfig.java, v 0.1 2018年8月28日 下午3:47:05 danfeng.zhou Exp $
 */
@SuppressWarnings("deprecation")
@Configuration
@EnableWebMvc
@ConditionalOnProperty(name = "spring.application.name", havingValue = "evaluation-service")
public class EvaluationChannelMvcConfig extends WebMvcConfigurerAdapter {

    /**
     * @see org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter#addResourceHandlers(org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry)
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/META-INF/resources/",
            "classpath:/resources/", "classpath:/static/");
    }

    /**
     * @see org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter#configureMessageConverters(java.util.List)
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        PcObjectMapper.buildMvcMessageConverter(converters);
    }
}
