/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.evaluation.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xcwlkj.util.StringUtil;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.MapPropertySource;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Map;

/**
 * 配置文件 - Redis Cluster + Spring Cache
 * 必须继承CachingConfigurerSupport，不然此类中生成的Bean不会生效（没有替换掉默认生成的，只是一个普通的bean）
 * springboot默认生成的缓存管理器和redisTemplate支持的类型很有限，根本不满足我们的需求，会抛出如下异常：
 *      org.springframework.cache.interceptor.SimpleKey cannot be cast to java.lang.String
 * <AUTHOR>
 * @version $Id: RedisConfig.java, v 0.1 2019年8月13日 下午7:45:17 danfeng.zhou Exp $
 */
@Configuration
@EnableCaching
@ConditionalOnProperty(name = "spring.application.name", havingValue = "evaluation-service")
public class RedisConfig extends CachingConfigurerSupport {
    /** 集群地址配置，和单机配置只要配置一个即可，集群配置优先  */
    @Value("${spring.redis.cluster.nodes}")
    private String clusterNodes;
    /** 单机配置:服务器地址  */
    @Value("${spring.redis.cluster.host}")
    private String redisHost;
    /** 单机配置:端口  */
    @Value("${spring.redis.cluster.port}")
    private int    redisPort;
    /** redis的密码 */
    @Value("${spring.redis.cluster.passwd}")
    private String redisPasswd;
    /** redis超时时间 */
    @Value("${spring.redis.cluster.timeOut}")
    private int    timeOut   = 5000;
    /** 跨集群执行命令时要遵循的最大重定向数量 */
    @Value("${spring.redis.cluster.max-redirects}")
    private int    redirects = 12;
    /** 缓存注解(@Cacheable、@CacheEvict等)缓存的有效时间,默认180秒 */
    @Value("${spring.redis.cluster.expire-time:180}")
    private int    expireTime;

    /**
     * Redis Cluster参数配置
     * @return
     */
    @Bean
    public RedisClusterConfiguration redisClusterConfiguration() {
        Map<String, Object> source = Maps.newHashMap();
        source.put("spring.redis.cluster.nodes", clusterNodes);
        source.put("spring.redis.cluster.timeout", timeOut);
        //跨集群执行命令时要遵循的最大重定向数量
        source.put("spring.redis.cluster.max-redirects", redirects);
        return new RedisClusterConfiguration(new MapPropertySource("RedisClusterConfiguration", source));
    }

    /**
     * 连接池设置
     * @return
     */
    @Bean(name = "redisConnectionFactory")
    public RedisConnectionFactory redisConnectionFactory(RedisClusterConfiguration redisClusterConfiguration) {
        JedisConnectionFactory cf = null;
        //集群
        if (StringUtil.isNotBlank(clusterNodes)) {
            if (StringUtil.isNotBlank(redisPasswd)) {
                redisClusterConfiguration.setPassword(redisPasswd);
            }
            cf = new JedisConnectionFactory(redisClusterConfiguration);
            //单机
        } else {
            RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration(redisHost, redisPort);
            standaloneConfig.setDatabase(0);
            if (StringUtil.isNotBlank(redisPasswd)) {
                standaloneConfig.setPassword(redisPasswd);
            }
            cf = new JedisConnectionFactory(standaloneConfig);
        }
        cf.afterPropertiesSet();
        return cf;
    }

    @Bean
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public RedisCacheConfiguration redisCacheConfiguration() {
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);

        RedisSerializationContext.SerializationPair<Object> pair = RedisSerializationContext.SerializationPair
            .fromSerializer(jackson2JsonRedisSerializer);

        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofSeconds(expireTime)).serializeValuesWith(pair);

        return redisCacheConfiguration;
    }

    /**
     * 管理缓存
     * @param redisConnectionFactory
     * @return
     */
    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        //初始化一个RedisCacheWriter
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        //初始化RedisCacheManager
        RedisCacheManager cacheManager = new RedisCacheManager(redisCacheWriter, redisCacheConfiguration());
        return cacheManager;
    }

    /**
     * 生产key的策略
     * 配置key生成器，作用于缓存管理器管理的所有缓存
     * 如果缓存注解（@Cacheable、@CacheEvict等）中指定key属性，那么会覆盖此key生成器
     * @see org.springframework.cache.annotation.CachingConfigurerSupport#keyGenerator()
     * @return
     */
    @Bean
    public KeyGenerator keyGenerator() {
        return new KeyGenerator() {
            @Override
            public Object generate(Object target, Method method, Object... params) {
                //规定  本类名+方法名+参数名 为key
                StringBuilder sb = new StringBuilder();
                sb.append(target.getClass().getName() + ":");
                sb.append(method.getName() + ":");
                for (Object obj : params) {
                    sb.append(obj.toString() + ",");
                }
                sb.deleteCharAt(sb.length() - 1);
                return sb.toString();
            }
        };
    }

}
