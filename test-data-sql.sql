-- 空考位统计查询接口测试数据
-- 执行前请确保相关表已创建

-- 1. 清理测试数据（可选）
-- DELETE FROM ks_kkw_msg WHERE ksjhbh = 'TEST2025001';
-- DELETE FROM ks_kcxx WHERE ksjhbh = 'TEST2025001';
-- DELETE FROM cs_jsjbxx WHERE bzhkcid LIKE 'TEST%';
-- DELETE FROM ks_ksrcxx WHERE ksjhbh = 'TEST2025001';

-- 2. 插入教室基本信息测试数据
INSERT INTO cs_jsjbxx (jsh, jsmc, bzhkcid, xqh, jxlh, szlc, zws, sczt, create_time, update_time) VALUES
('TEST_JS001', '第一教学楼101教室', 'TEST_KC001', 'XQ001', 'JXL001', '1', 30, '0', NOW(), NOW()),
('TEST_JS002', '第一教学楼102教室', 'TEST_KC002', 'XQ001', 'JXL001', '1', 30, '0', NOW(), NOW()),
('TEST_JS003', '第一教学楼103教室', 'TEST_KC003', 'XQ001', 'JXL001', '1', 30, '0', NOW(), NOW()),
('TEST_JS004', '第一教学楼201教室', 'TEST_KC004', 'XQ001', 'JXL001', '2', 30, '0', NOW(), NOW()),
('TEST_JS005', '第一教学楼202教室', 'TEST_KC005', 'XQ001', 'JXL001', '2', 30, '0', NOW(), NOW()),
('TEST_JS006', '第二教学楼101教室', 'TEST_KC006', 'XQ001', 'JXL002', '1', 25, '0', NOW(), NOW()),
('TEST_JS007', '第二教学楼102教室', 'TEST_KC007', 'XQ001', 'JXL002', '1', 25, '0', NOW(), NOW()),
('TEST_JS008', '第二教学楼103教室', 'TEST_KC008', 'XQ001', 'JXL002', '1', 25, '0', NOW(), NOW());

-- 3. 插入考试考场信息测试数据
INSERT INTO ks_kcxx (id, kcbh, kcmc, ksjhbh, ccm, kmmc, bzhkcmc, bzhkcid, ljkcbh, kdbh, bzhkdid, sczt, create_time, update_time) VALUES
('TEST_KCXX001', '101', '101考场', 'TEST2025001', '001', '数学', '第一教学楼101教室', 'TEST_KC001', 'LJ101', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX002', '102', '102考场', 'TEST2025001', '001', '数学', '第一教学楼102教室', 'TEST_KC002', 'LJ102', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX003', '103', '103考场', 'TEST2025001', '001', '数学', '第一教学楼103教室', 'TEST_KC003', 'LJ103', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX004', '201', '201考场', 'TEST2025001', '001', '数学', '第一教学楼201教室', 'TEST_KC004', 'LJ201', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX005', '202', '202考场', 'TEST2025001', '001', '数学', '第一教学楼202教室', 'TEST_KC005', 'LJ202', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX006', '301', '301考场', 'TEST2025001', '002', '英语', '第二教学楼101教室', 'TEST_KC006', 'LJ301', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX007', '302', '302考场', 'TEST2025001', '002', '英语', '第二教学楼102教室', 'TEST_KC007', 'LJ302', 'KD001', 'BZHKD001', '0', NOW(), NOW()),
('TEST_KCXX008', '303', '303考场', 'TEST2025001', '002', '英语', '第二教学楼103教室', 'TEST_KC008', 'LJ303', 'KD001', 'BZHKD001', '0', NOW(), NOW());

-- 4. 插入考生入场信息测试数据
INSERT INTO ks_ksrcxx (ksrcbh, ksjhbh, ccm, zkzh, ksxm, sfzh, ksh, kcbh, ljkcbh, zwh, sfrc, rcsj, bzhkcid, sczt, create_time, update_time) VALUES
('TEST_RC001', 'TEST2025001', '001', '202501001', '张三', '110101199001011234', 'KS001', '101', 'LJ101', '01', '1', '2025-07-29 08:30:00', 'TEST_KC001', '0', NOW(), NOW()),
('TEST_RC002', 'TEST2025001', '001', '202501002', '李四', '110101199001021234', 'KS002', '101', 'LJ101', '02', '1', '2025-07-29 08:31:00', 'TEST_KC001', '0', NOW(), NOW()),
('TEST_RC003', 'TEST2025001', '001', '202501003', '王五', '110101199001031234', 'KS003', '101', 'LJ101', '03', '0', NULL, 'TEST_KC001', '0', NOW(), NOW()),
('TEST_RC004', 'TEST2025001', '001', '202501004', '赵六', '110101199001041234', 'KS004', '102', 'LJ102', '01', '1', '2025-07-29 08:32:00', 'TEST_KC002', '0', NOW(), NOW()),
('TEST_RC005', 'TEST2025001', '001', '202501005', '钱七', '110101199001051234', 'KS005', '102', 'LJ102', '02', '0', NULL, 'TEST_KC002', '0', NOW(), NOW()),
('TEST_RC006', 'TEST2025001', '001', '202501006', '孙八', '110101199001061234', 'KS006', '103', 'LJ103', '01', '1', '2025-07-29 08:33:00', 'TEST_KC003', '0', NOW(), NOW()),
('TEST_RC007', 'TEST2025001', '002', '202501007', '周九', '110101199001071234', 'KS007', '301', 'LJ301', '01', '1', '2025-07-29 10:30:00', 'TEST_KC006', '0', NOW(), NOW()),
('TEST_RC008', 'TEST2025001', '002', '202501008', '吴十', '110101199001081234', 'KS008', '301', 'LJ301', '02', '0', NULL, 'TEST_KC006', '0', NOW(), NOW());

-- 5. 插入空考位消息测试数据
INSERT INTO ks_kkw_msg (id, ksjhbh, ccm, bzhkdid, bzhkcid, ljkch, kch, ks_zkzh, ks_bpzwh, ks_sjzwh, rcbz, timestamp, czsj, sczt, create_time, update_time) VALUES
-- 101考场的空考位消息（已上报）
('TEST_KKW001', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC001', 'LJ101', '101', '202501003', '03', '03', '6', '2025-07-29 09:00:00', '2025-07-29 09:00:00', '0', NOW(), NOW()),
('TEST_KKW002', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC001', 'LJ101', '101', '202501009', '04', '04', '7', '2025-07-29 09:01:00', '2025-07-29 09:01:00', '0', NOW(), NOW()),

-- 102考场的空考位消息（已上报）
('TEST_KKW003', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC002', 'LJ102', '102', '202501005', '02', '02', '6', '2025-07-29 09:02:00', '2025-07-29 09:02:00', '0', NOW(), NOW()),
('TEST_KKW004', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC002', 'LJ102', '102', '202501010', '03', '03', '7', '2025-07-29 09:03:00', '2025-07-29 09:03:00', '0', NOW(), NOW()),

-- 301考场的空考位消息（已上报）
('TEST_KKW005', 'TEST2025001', '002', 'BZHKD001', 'TEST_KC006', 'LJ301', '301', '202501008', '02', '02', '6', '2025-07-29 11:00:00', '2025-07-29 11:00:00', '0', NOW(), NOW()),
('TEST_KKW006', 'TEST2025001', '002', 'BZHKD001', 'TEST_KC006', 'LJ301', '301', '202501011', '03', '03', '7', '2025-07-29 11:01:00', '2025-07-29 11:01:00', '0', NOW(), NOW()),

-- 一些异常类型的测试数据
('TEST_KKW007', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC003', 'LJ103', '103', '202501012', '02', '03', '2', '2025-07-29 09:05:00', '2025-07-29 09:05:00', '0', NOW(), NOW()),
('TEST_KKW008', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC003', 'LJ103', '103', '202501013', '03', '02', '4', '2025-07-29 09:06:00', '2025-07-29 09:06:00', '0', NOW(), NOW()),
('TEST_KKW009', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC004', 'LJ201', '201', '202501014', '01', '01', '1', '2025-07-29 09:07:00', '2025-07-29 09:07:00', '0', NOW(), NOW()),
('TEST_KKW010', 'TEST2025001', '001', 'BZHKD001', 'TEST_KC004', 'LJ201', '201', '202501015', '02', '02', '5', '2025-07-29 09:08:00', '2025-07-29 09:08:00', '0', NOW(), NOW());

-- 6. 验证数据插入结果的查询语句
-- 查看考场统计情况
SELECT 
    '考场统计' as 类型,
    COUNT(*) as 总考场数,
    COUNT(CASE WHEN EXISTS(SELECT 1 FROM ks_kkw_msg msg WHERE msg.ljkch = kc.ljkcbh AND msg.sczt = '0') THEN 1 END) as 已上报考场数,
    COUNT(CASE WHEN NOT EXISTS(SELECT 1 FROM ks_kkw_msg msg WHERE msg.ljkch = kc.ljkcbh AND msg.sczt = '0') THEN 1 END) as 未上报考场数
FROM ks_kcxx kc 
WHERE kc.ksjhbh = 'TEST2025001' AND kc.sczt = '0';

-- 查看空考位消息统计
SELECT 
    '空考位消息统计' as 类型,
    ccm as 场次,
    COUNT(*) as 消息数量,
    COUNT(CASE WHEN rcbz = '6' THEN 1 END) as 缺考数量,
    COUNT(CASE WHEN rcbz = '7' THEN 1 END) as 无编排数量,
    COUNT(CASE WHEN rcbz IN ('1','2','3','4','5') THEN 1 END) as 其他异常数量
FROM ks_kkw_msg 
WHERE ksjhbh = 'TEST2025001' AND sczt = '0'
GROUP BY ccm;

-- 查看考生入场情况
SELECT 
    '考生入场统计' as 类型,
    ccm as 场次,
    COUNT(*) as 总人数,
    COUNT(CASE WHEN sfrc = '1' THEN 1 END) as 已入场人数,
    COUNT(CASE WHEN sfrc = '0' THEN 1 END) as 未入场人数
FROM ks_ksrcxx 
WHERE ksjhbh = 'TEST2025001' AND sczt = '0'
GROUP BY ccm;
