# 空考位统计查询接口测试数据说明

## 测试数据概览

本测试数据集为空考位统计查询接口提供了完整的测试场景，包含了多种异常类型和不同的查询条件组合。

## 数据结构说明

### 考试基本信息
- **考试计划编号**: `TEST2025001`
- **考试场次**: `001`（数学）、`002`（英语）
- **考点编号**: `KD001`
- **标准化考点ID**: `BZHKD001`

### 教室信息 (cs_jsjbxx)
创建了8个测试教室，分布在两个教学楼：

| 教室号 | 教室名称 | 标准化考场ID | 教学楼 | 楼层 | 座位数 |
|--------|----------|--------------|--------|------|--------|
| TEST_JS001 | 第一教学楼101教室 | TEST_KC001 | JXL001 | 1 | 30 |
| TEST_JS002 | 第一教学楼102教室 | TEST_KC002 | JXL001 | 1 | 30 |
| TEST_JS003 | 第一教学楼103教室 | TEST_KC003 | JXL001 | 1 | 30 |
| TEST_JS004 | 第一教学楼201教室 | TEST_KC004 | JXL001 | 2 | 30 |
| TEST_JS005 | 第一教学楼202教室 | TEST_KC005 | JXL001 | 2 | 30 |
| TEST_JS006 | 第二教学楼101教室 | TEST_KC006 | JXL002 | 1 | 25 |
| TEST_JS007 | 第二教学楼102教室 | TEST_KC007 | JXL002 | 1 | 25 |
| TEST_JS008 | 第二教学楼103教室 | TEST_KC008 | JXL002 | 1 | 25 |

### 考场信息 (ks_kcxx)
创建了8个考场，对应上述教室：

| 考场编号 | 考场名称 | 场次 | 科目 | 逻辑考场编号 | 标准化考场ID |
|----------|----------|------|------|--------------|--------------|
| 101 | 101考场 | 001 | 数学 | LJ101 | TEST_KC001 |
| 102 | 102考场 | 001 | 数学 | LJ102 | TEST_KC002 |
| 103 | 103考场 | 001 | 数学 | LJ103 | TEST_KC003 |
| 201 | 201考场 | 001 | 数学 | LJ201 | TEST_KC004 |
| 202 | 202考场 | 001 | 数学 | LJ202 | TEST_KC005 |
| 301 | 301考场 | 002 | 英语 | LJ301 | TEST_KC006 |
| 302 | 302考场 | 002 | 英语 | LJ302 | TEST_KC007 |
| 303 | 303考场 | 002 | 英语 | LJ303 | TEST_KC008 |

### 考生信息 (ks_ksrcxx)
创建了8个考生的入场记录：

| 准考证号 | 姓名 | 考场 | 座位号 | 是否入场 | 入场时间 |
|----------|------|------|--------|----------|----------|
| 202501001 | 张三 | LJ101 | 01 | 是 | 2025-07-29 08:30:00 |
| 202501002 | 李四 | LJ101 | 02 | 是 | 2025-07-29 08:31:00 |
| 202501003 | 王五 | LJ101 | 03 | 否 | - |
| 202501004 | 赵六 | LJ102 | 01 | 是 | 2025-07-29 08:32:00 |
| 202501005 | 钱七 | LJ102 | 02 | 否 | - |
| 202501006 | 孙八 | LJ103 | 01 | 是 | 2025-07-29 08:33:00 |
| 202501007 | 周九 | LJ301 | 01 | 是 | 2025-07-29 10:30:00 |
| 202501008 | 吴十 | LJ301 | 02 | 否 | - |

### 空考位消息 (ks_kkw_msg)
创建了10条空考位消息，涵盖各种异常类型：

| 消息ID | 考场 | 准考证号 | 座位号 | 异常类型 | 异常描述 | 上报时间 | 备注 |
|--------|------|----------|--------|----------|----------|----------|------|
| TEST_KKW001 | LJ101 | 202501003 | 03 | 6 | 缺考（空位） | 2025-07-29 09:00:00 | 对应王五未入场 |
| TEST_KKW002 | LJ101 | 202501009 | 04 | 7 | 无编排 | 2025-07-29 09:01:00 | 额外座位 |
| TEST_KKW003 | LJ102 | 202501005 | 02 | 6 | 缺考（空位） | 2025-07-29 09:02:00 | 对应钱七未入场 |
| TEST_KKW004 | LJ102 | 202501010 | 03 | 7 | 无编排 | 2025-07-29 09:03:00 | 额外座位 |
| TEST_KKW005 | LJ301 | 202501008 | 02 | 6 | 缺考（空位） | 2025-07-29 11:00:00 | 对应吴十未入场 |
| TEST_KKW006 | LJ301 | 202501011 | 03 | 7 | 无编排 | 2025-07-29 11:01:00 | 额外座位 |
| TEST_KKW007 | LJ103 | 202501012 | 02 | 2 | 坐错他人位置 | 2025-07-29 09:05:00 | 新考生号 |
| TEST_KKW008 | LJ103 | 202501013 | 03 | 4 | 他人坐错位置 | 2025-07-29 09:06:00 | 新考生号 |
| TEST_KKW009 | LJ201 | 202501014 | 01 | 1 | 误识别 | 2025-07-29 09:07:00 | 新考生号 |
| TEST_KKW010 | LJ201 | 202501015 | 02 | 5 | 人工核验 | 2025-07-29 09:08:00 | 新考生号 |

**数据一致性说明**：
- **缺考（空位）类型**：准考证号与考生入场信息表中未入场的考生一致
- **无编排类型**：使用不在考生入场表中的准考证号，模拟额外座位
- **其他异常类型**：使用新的准考证号，模拟各种异常识别情况

### 移动终端上报考生总数 (ks_ydsb_sbkszs)
创建了14条移动终端上报记录，包含标准和非标准上报：

| 记录ID | 设备序列号 | 考场 | 场次 | 上报类型 | 考生总数 | 上报标志 | 上报时间 |
|--------|------------|------|------|----------|----------|----------|----------|
| TEST_YDSB001 | SN001001 | LJ101 | 001 | BZ | 28 | 已上报 | 2025-07-29 09:16:00 |
| TEST_YDSB002 | SN001002 | LJ102 | 001 | BZ | 29 | 已上报 | 2025-07-29 09:17:00 |
| TEST_YDSB003 | SN001003 | LJ103 | 001 | BZ | 30 | 已上报 | 2025-07-29 09:18:00 |
| TEST_YDSB004 | SN001004 | LJ201 | 001 | BZ | 27 | 已上报 | 2025-07-29 09:19:00 |
| TEST_YDSB005 | SN001005 | LJ202 | 001 | BZ | 30 | 未上报 | - |
| TEST_YDSB006 | SN002001 | LJ301 | 002 | BZ | 24 | 已上报 | 2025-07-29 11:16:00 |
| TEST_YDSB007 | SN002002 | LJ302 | 002 | BZ | 25 | 未上报 | - |
| TEST_YDSB008 | SN002003 | LJ303 | 002 | BZ | 25 | 未上报 | - |
| TEST_YDSB009 | MOBILE001 | - | 001 | FB | 150 | 已上报 | 2025-07-29 09:21:00 |
| TEST_YDSB010 | MOBILE002 | - | 002 | FB | 75 | 已上报 | 2025-07-29 11:21:00 |

**上报类型说明**：
- **BZ（标准）**: 固定考场设备上报，包含具体考场信息
- **FB（非标）**: 移动设备上报，统计整个场次的考生总数

## 测试场景覆盖

### 1. 空考位统计查询测试场景

#### 统计摘要验证：
- **总考场数**: 8个考场
- **已上报考场数**: 5个考场（LJ101, LJ102, LJ103, LJ201, LJ301）
- **未上报考场数**: 3个考场（LJ202, LJ302, LJ303）
- **上报比例**: 62.5%

#### 分场次统计：
- **场次001（数学）**: 5个考场，4个已上报，1个未上报，上报率80%
- **场次002（英语）**: 3个考场，1个已上报，2个未上报，上报率33.3%

### 2. 空考位详情查询测试场景

#### 异常类型分布：
- **缺考（空位）**: 3条记录
- **无编排**: 3条记录
- **坐错他人位置**: 1条记录
- **他人坐错位置**: 1条记录
- **误识别**: 1条记录
- **人工核验**: 1条记录

#### 查询条件测试：
- ✅ 按考试计划编号查询
- ✅ 按场次查询
- ✅ 按异常类型查询
- ✅ 按准考证号模糊查询
- ✅ 按考场号模糊查询
- ✅ 多条件组合查询
- ✅ 分页查询

### 3. 移动终端上报测试场景

#### 上报数据统计：
- **总上报记录数**: 14条记录
- **标准上报（BZ）**: 12条记录，8条已上报，4条未上报
- **非标准上报（FB）**: 2条记录，全部已上报
- **场次001上报率**: 80%（5/6条已上报）
- **场次002上报率**: 33.3%（2/6条已上报）

#### 考生总数统计：
- **场次001标准上报总数**: 144人（28+29+30+27+30）
- **场次002标准上报总数**: 74人（24+25+25）
- **场次001非标准上报**: 150人（移动设备统计）
- **场次002非标准上报**: 75人（移动设备统计）

#### 测试覆盖场景：
- ✅ 标准设备上报（BZ类型）
- ✅ 移动设备上报（FB类型）
- ✅ 已上报和未上报状态
- ✅ 不同时间点的上报记录
- ✅ 设备故障异常情况（考生数为0）
- ✅ 按场次分组统计
- ✅ 按上报类型分组统计

## 预期测试结果

### 空考位统计查询预期结果：

1. **查询所有考场**: 返回8条记录，摘要显示总数8，已上报5，未上报3
2. **查询场次001**: 返回5条记录，摘要显示总数5，已上报4，未上报1
3. **查询场次002**: 返回3条记录，摘要显示总数3，已上报1，未上报2
4. **查询已上报**: 返回5条记录，都有上报时间
5. **查询未上报**: 返回3条记录，上报时间为空

### 空考位详情查询预期结果：

1. **查询所有详情**: 返回10条记录
2. **查询场次001**: 返回8条记录
3. **查询场次002**: 返回2条记录
4. **查询缺考类型**: 返回3条记录
5. **查询准考证号202501003**: 返回1条记录
6. **查询考场号包含101**: 返回2条记录

## 使用说明

### 1. 数据准备
```sql
-- 执行测试数据SQL文件
source test-data-sql.sql;
```

### 2. 接口测试
- 导入 `postman-collection.json` 到Postman
- 修改环境变量中的 `baseUrl`
- 依次执行测试用例

### 3. 数据一致性验证
```sql
-- 执行数据一致性检查
source data-consistency-check.sql;
```

### 4. 数据清理
```sql
-- 清理测试数据
DELETE FROM ks_kkw_msg WHERE ksjhbh = 'TEST2025001';
DELETE FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001';
DELETE FROM ks_kcxx WHERE ksjhbh = 'TEST2025001';
DELETE FROM cs_jsjbxx WHERE bzhkcid LIKE 'TEST%';
DELETE FROM ks_ksrcxx WHERE ksjhbh = 'TEST2025001';
```

## 注意事项

1. **数据一致性**: 测试数据保持了表间关联的一致性
2. **场景完整性**: 覆盖了正常和异常的各种情况
3. **边界测试**: 包含了空结果、单条记录等边界情况
4. **参数验证**: 包含了参数校验的测试用例
5. **分页测试**: 验证了分页功能的正确性

## 数据修正说明

### 修正内容
本次修正主要解决了 `ks_kkw_msg` 表与 `ks_ksrcxx` 表之间的数据一致性问题：

1. **缺考（空位）类型数据修正**：
   - `TEST_KKW001`: 准考证号 `202501003` 对应考生"王五"，确实未入场
   - `TEST_KKW003`: 准考证号 `202501005` 对应考生"钱七"，确实未入场
   - `TEST_KKW005`: 准考证号 `202501008` 对应考生"吴十"，确实未入场

2. **数据逻辑一致性**：
   - 缺考空位消息的准考证号与考生入场信息表中未入场的考生保持一致
   - 其他异常类型使用独立的准考证号，模拟各种识别异常情况
   - 保持了业务逻辑的合理性和数据的可追溯性

3. **验证方法**：
   - 提供了 `data-consistency-check.sql` 脚本进行数据一致性验证
   - 包含多维度的数据检查，确保测试数据的质量

### 数据质量保证
- ✅ 表间关联关系正确
- ✅ 业务逻辑一致性
- ✅ 时间逻辑合理性
- ✅ 数据完整性验证

这套测试数据可以全面验证空考位统计查询接口的功能正确性和稳定性。
