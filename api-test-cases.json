{"空考位统计查询接口测试用例": {"接口地址": "/manager/identity/cxtj/kkwtj", "请求方法": "POST", "测试用例": [{"用例名称": "基础查询-查询所有考场", "描述": "查询指定考试计划的所有考场空考位统计", "请求体": {"ksjhbh": "TEST2025001", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 8, "pageNum": 1, "pageSize": 10}, "summary": {"total": 8, "ysb": 5, "wsb": 3, "reportRate": "62.5%"}}}}, {"用例名称": "按场次查询", "描述": "查询指定场次的考场空考位统计", "请求体": {"ksjhbh": "TEST2025001", "ccm": "001", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 5, "pageNum": 1, "pageSize": 10}, "summary": {"total": 5, "ysb": 4, "wsb": 1, "reportRate": "80%"}}}}, {"用例名称": "查询已上报考场", "描述": "只查询已上报空考位消息的考场", "请求体": {"ksjhbh": "TEST2025001", "ccm": "001", "sbzt": "1", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 4, "pageNum": 1, "pageSize": 10, "list": [{"kch": "101", "csmc": "第一教学楼101教室", "sfsb": "1", "sbsj": "2025-07-29 09:01:00", "bzhkcid": "TEST_KC001", "ljkcbh": "LJ101"}]}}}}, {"用例名称": "查询未上报考场", "描述": "只查询未上报空考位消息的考场", "请求体": {"ksjhbh": "TEST2025001", "ccm": "001", "sbzt": "0", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 1, "pageNum": 1, "pageSize": 10, "list": [{"kch": "202", "csmc": "第一教学楼202教室", "sfsb": "0", "sbsj": null, "bzhkcid": "TEST_KC005", "ljkcbh": "LJ202"}]}}}}, {"用例名称": "参数验证-缺少必填参数", "描述": "测试缺少必填参数时的错误处理", "请求体": {"pageNum": 1, "pageSize": 10}, "预期结果": {"code": "400", "message": "考试计划编号不能为空", "result": null}}, {"用例名称": "参数验证-页码参数错误", "描述": "测试页码参数为0时的错误处理", "请求体": {"ksjhbh": "TEST2025001", "pageNum": 0, "pageSize": 10}, "预期结果": {"code": "400", "message": "页码不能为空", "result": null}}]}, "空考位详情查询接口测试用例": {"接口地址": "/manager/identity/cxtj/kkwxq", "请求方法": "POST", "测试用例": [{"用例名称": "基础查询-查询所有详情", "描述": "查询指定考试计划的所有空考位详情", "请求体": {"ksjhbh": "TEST2025001", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 10, "pageNum": 1, "pageSize": 10, "list": [{"kch": "LJ101", "csmc": "第一教学楼101教室", "zkzh": "202501003", "xm": "王五", "zwh": "03", "yclx": "缺考（空位）", "zdsbsj": "2025-07-29 09:00:00", "sfsb": "0", "sbsjsj": null}]}}}}, {"用例名称": "按场次查询详情", "描述": "查询指定场次的空考位详情", "请求体": {"ksjhbh": "TEST2025001", "ccm": "001", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 8, "pageNum": 1, "pageSize": 10}}}}, {"用例名称": "按异常类型查询", "描述": "查询缺考（空位）类型的详情", "请求体": {"ksjhbh": "TEST2025001", "ccm": "001", "yclx": "6", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 2, "pageNum": 1, "pageSize": 10, "list": [{"kch": "LJ101", "csmc": "第一教学楼101教室", "zkzh": "202501003", "xm": "王五", "zwh": "03", "yclx": "缺考（空位）", "zdsbsj": "2025-07-29 09:00:00", "sfsb": "0", "sbsjsj": null}]}}}}, {"用例名称": "按准考证号查询", "描述": "根据准考证号模糊查询详情", "请求体": {"ksjhbh": "TEST2025001", "zkzh": "202501003", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 1, "pageNum": 1, "pageSize": 10, "list": [{"kch": "LJ101", "csmc": "第一教学楼101教室", "zkzh": "202501003", "xm": "王五", "zwh": "03", "yclx": "缺考（空位）", "zdsbsj": "2025-07-29 09:00:00", "sfsb": "0", "sbsjsj": null}]}}}}, {"用例名称": "按考场号查询", "描述": "根据考场号模糊查询详情", "请求体": {"ksjhbh": "TEST2025001", "kch": "101", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 2, "pageNum": 1, "pageSize": 10}}}}, {"用例名称": "组合条件查询", "描述": "使用多个条件组合查询", "请求体": {"ksjhbh": "TEST2025001", "ccm": "001", "yclx": "2", "kch": "103", "pageNum": 1, "pageSize": 10}, "预期结果": {"code": "200", "message": "操作成功", "result": {"pageInfo": {"total": 1, "pageNum": 1, "pageSize": 10, "list": [{"kch": "LJ103", "csmc": "第一教学楼103教室", "zkzh": "202501012", "xm": null, "zwh": "02", "yclx": "坐错他人位置", "zdsbsj": "2025-07-29 09:05:00", "sfsb": "0", "sbsjsj": null}]}}}}]}, "异常类型说明": {"1": "误识别", "2": "坐错他人位置", "3": "实际未参加考试", "4": "他人坐错位置", "5": "人工核验", "6": "缺考（空位）", "7": "无编排"}, "测试环境配置": {"数据库": "执行 test-data-sql.sql 文件中的测试数据", "接口地址": "http://localhost:8080", "请求头": {"Content-Type": "application/json", "Authorization": "Bearer your-token-here"}}, "测试步骤": ["1. 执行测试数据SQL脚本，插入测试数据", "2. 启动应用服务", "3. 使用Postman或其他工具发送请求", "4. 验证响应结果是否符合预期", "5. 测试完成后可选择清理测试数据"]}