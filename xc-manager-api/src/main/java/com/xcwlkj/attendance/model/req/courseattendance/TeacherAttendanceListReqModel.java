/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.req.courseattendance;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;


/**
 * 教师考勤列表请求
 * <AUTHOR>
 * @version $Id: TeacherAttendanceListReqModel.java, v 0.1 2023年07月17日 09时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TeacherAttendanceListReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 考勤结果 0：正常1：请假2：迟到3：早退4：旷课 */
    private Integer kqjg;
    /** 教师姓名/工号 */
    private String jsxm;
    /** 课程名称 */
    private String kcmc;
    /** 开始时间 */
    private String kssj;
    /** 结束时间 */
    private String jssj;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageNum;
    /** 页大小 */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}