/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.dto.classduty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 班级值日新增dto
 * <AUTHOR>
 * @version $Id: AddDTO.java, v 0.1 2025年06月19日 13时16分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class AddDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 开课学年度 */
    @NotBlank(message = "开课学年度不能为空")
    private String kkxnd;
    /** 开课学期码 */
    @NotBlank(message = "开课学期码不能为空")
    private String kkxqm;
    /** 星期 */
    @NotNull(message = "星期不能为空")
    private Integer xq;
    /** 开始周次 */
    @NotNull(message = "开始周次不能为空")
    private Integer kszc;
    /** 结束周次 */
    @NotNull(message = "结束周次不能为空")
    private Integer jszc;
    /** 是否单双周  0：每周\r\n            1：单周\r\n            2：双周 */
    @NotBlank(message = "是否单双周  0：每周\r\n            1：单周\r\n            2：双周不能为空")
    private String sfdsz;
    /** 值日班班号 */
    @NotBlank(message = "值日班班号不能为空")
    private String zrbbh;
    /** 值日班名称 */
    private String zrbmc;
    /** 值日项属性码 */
    @NotBlank(message = "值日项属性码不能为空")
    private String zrxsxm;
    /** 值日项名称 */
    @NotBlank(message = "值日项名称不能为空")
    private String zrxmc;
    /** 值日生学号，多个用逗号隔开 */
    @NotBlank(message = "值日生学号，多个用逗号隔开不能为空")
    private String zrsxh;
    /** 值日生姓名，多个用逗号隔开 */
    @NotBlank(message = "值日生姓名，多个用逗号隔开不能为空")
    private String zrsxm;
    /** 教室号 */
    private String jsh;
    /** 值日地点 */
    private String zrdd;
    /** 教室所在校区号 */
    private String jsszxqh;
    /** 值日所在周数 */
    private Integer zrszzs;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
