/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.resp.classhonor;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.campus.model.vo.classhonor.HonorItemVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;


/**
 * 获取班级荣誉列表响应
 * <AUTHOR>
 * @version $Id: HonorListRespModel.java, v 0.1 2025年06月06日 13时52分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HonorListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 荣誉列表 */
    private List<HonorItemVO> honorList;
    /** 总数 */
    private Long total;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}