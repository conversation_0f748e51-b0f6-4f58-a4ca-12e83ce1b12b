/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.req.campusapplication;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 校园应用开关请求
 * <AUTHOR>
 * @version $Id: CampusApplicationSwitchingApplicationReqModel.java, v 0.1 2023年07月18日 13时30分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CampusApplicationSwitchingApplicationReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 应用编号 */
    @NotNull(message = "应用编号不能为空")
    private List<String> yybh;
    /** 开关状态 */
    @NotNull(message = "开关状态不能为空")
    private String kgzt;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}