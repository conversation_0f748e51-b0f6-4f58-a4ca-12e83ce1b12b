/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.req.datadistribute;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;


/**
 * 下发配置修改请求
 * <AUTHOR>
 * @version $Id: ModifyTaskConfigReqModel.java, v 0.1 2023年07月21日 14时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModifyTaskConfigReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 编号 */
    @NotNull(message = "编号不能为空")
    private Integer bh;
    /** 任务大类：EDU教学模式、EXAM考试模式 */
    @NotBlank(message = "任务大类：EDU教学模式、EXAM考试模式不能为空")
    private String classify;
    /** 任务类型,多个以逗号隔开: 编排表bp,学生xs,教师js,人脸rl */
    @NotBlank(message = "任务类型,多个以逗号隔开: 编排表bp,学生xs,教师js,人脸rl不能为空")
    private String rwlx;
    /** 提前天数 */
    @NotNull(message = "提前天数不能为空")
    private Integer day;
    /** 小时 */
    private Integer hour;
    /** 分钟 */
    private Integer minute;
    /** 状态 0：未启用 1：启用  不填默认为1 */
    private Integer zt;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}