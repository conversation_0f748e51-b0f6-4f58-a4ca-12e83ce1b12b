/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.campus.model.req.campusapplication;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 校园应用设备当前应用版本列表请求
 * <AUTHOR>
 * @version $Id: AppCurrVersionListReqModel.java, v 0.1 2024年11月29日 11时09分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppCurrVersionListReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 设备序列号列表 */
    @NotNull(message = "设备序列号列表不能为空")
    private List<String> xlhList;
    /** 软件包名 */
    private String packageName;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageNum;
    /** 页大小 */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}