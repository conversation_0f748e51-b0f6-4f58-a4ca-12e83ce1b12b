/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.resp.news;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.campus.model.vo.news.XwItemVO;
import java.util.List;


/**
 * 查询校园新闻列表响应
 * <AUTHOR>
 * @version $Id: SelectZhxyXwListRespModel.java, v 0.1 2023年07月19日 14时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SelectZhxyXwListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /**  */
    private List<XwItemVO> xwList;
    /**  */
    private Integer total;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}