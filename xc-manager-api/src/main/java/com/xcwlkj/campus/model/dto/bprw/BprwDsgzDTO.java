/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.dto.bprw;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 班牌任务添加dto
 * <AUTHOR>
 * @version $Id: BprwAddDTO.java, v 0.1 2023年07月19日 16时17分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class BprwDsgzDTO implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;

    /** 场景类型 */
    @NotBlank(message = "场景类型不能为空")
    private String cjlx;
    /** 执行时间  HH:mm:ss精确到秒 */
    @NotNull(message = "执行时间  HH:mm:ss精确到秒不能为空")
    private String zxsj;
    /** 时长,单位秒,-1表示时间不限制 */
    @NotNull(message = "时长,单位秒,-1表示时间不限制不能为空")
    private Integer sc;
    /** 重复周期   复选星期一~星期日  以逗号分隔 */
    @NotNull(message = "重复周期   复选星期一~星期日  以逗号分隔不能为空")
    private String cfzq;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
