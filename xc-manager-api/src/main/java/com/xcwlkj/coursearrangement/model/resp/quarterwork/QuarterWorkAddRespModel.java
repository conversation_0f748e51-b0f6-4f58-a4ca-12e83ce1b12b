/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.resp.quarterwork;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 学期作息关联接口（添加）响应
 * <AUTHOR>
 * @version $Id: QuarterWorkAddRespModel.java, v 0.1 2023年08月04日 10时53分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuarterWorkAddRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}