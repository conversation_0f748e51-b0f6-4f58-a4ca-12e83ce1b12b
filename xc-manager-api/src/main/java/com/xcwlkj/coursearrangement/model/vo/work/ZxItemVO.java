/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.vo.work;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 列表-作息vo
 *
 * <AUTHOR>
 * @version $Id: ZxItemVO.java, v 0.1 2022年10月08日 09时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ZxItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private String bh;
    /**
     * 作息名称
     */
    private String zxmc;
    /**
     * 作息状态
     */
    private String zxzt;
    /**
     * 节次数量
     */
    private Integer jcsl;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
