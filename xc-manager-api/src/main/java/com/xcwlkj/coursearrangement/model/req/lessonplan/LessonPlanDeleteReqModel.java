/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.req.lessonplan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 学期开课计划删除请求
 *
 * <AUTHOR>
 * @version $Id: LessonPlanDeleteReqModel.java, v 0.1 2022年10月08日 09时35分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LessonPlanDeleteReqModel extends RemoteReqBaseModel {
    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     * 实践学时
     */
    private List<String> idList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}