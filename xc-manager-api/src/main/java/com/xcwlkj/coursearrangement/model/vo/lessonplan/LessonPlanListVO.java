/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.vo.lessonplan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 学期开课计划列表vo
 *
 * <AUTHOR>
 * @version $Id: LessonPlanListVO.java, v 0.1 2022年10月08日 09时35分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LessonPlanListVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private List<LessonPlanItemVO> lessonPlanList;
    /**
     *
     */
    private Integer totalRows;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
