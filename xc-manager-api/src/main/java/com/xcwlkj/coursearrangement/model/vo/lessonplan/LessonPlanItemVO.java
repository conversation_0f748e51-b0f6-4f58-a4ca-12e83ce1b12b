/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.vo.lessonplan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 学期开课计划列表vo
 *
 * <AUTHOR>
 * @version $Id: LessonPlanItemVO.java, v 0.1 2022年10月08日 09时35分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LessonPlanItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 开课学年度
     */
    private String kkxnd;
    /**
     * 开课学期码
     */
    private String kkxqm;
    /**
     * 课程号
     */
    private String kch;
    /**
     * 课程名称
     */
    private String kcmc;
    /**
     * 学分
     */
    private String xf;
    /**
     * 课程简介
     */
    private String kcjj;
    /**
     * 课程开设单位号
     */
    private String kcksdwh;
    /**
     * 单位名称
     */
    private String kcksdwmc;
    /**
     *
     */
    private String bh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
