/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.dto.work;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 修改子项-作息dto
 *
 * <AUTHOR>
 * @version $Id: WorkUpdateItemDTO.java, v 0.1 2022年10月08日 09时37分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkUpdateItemDTO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空")
    private String bh;
    /**
     * 节次
     */
    @NotNull(message = "节次不能为空")
    private Integer jc;
    /**
     * 节次开始时间
     */
    @NotBlank(message = "节次开始时间不能为空")
    private String startTime;
    /**
     * 节次结束时间
     */
    @NotBlank(message = "节次结束时间不能为空")
    private String endTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
