/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.req.work;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;


/**
 * 根据教室号查询教室作息绑定详情请求
 * <AUTHOR>
 * @version $Id: WorkBindingDetailByJshReqModel.java, v 0.1 2024年08月05日 15时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkBindingDetailByJshReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 教室号 */
    @NotBlank(message = "教室号不能为空")
    private String jsh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}