/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.dto.work;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 删除子项-作息dto
 *
 * <AUTHOR>
 * @version $Id: WorkDeleteItemDTO.java, v 0.1 2022年10月08日 09时38分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkDeleteItemDTO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @NotNull(message = "不能为空")
    private List<String> bhList;

    @NotNull(message = "不能为空")
    private String zxbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
