/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.req.work;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * 列表-作息请求
 *
 * <AUTHOR>
 * @version $Id: WorkListReqModel.java, v 0.1 2022年10月08日 09时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkListReqModel extends RemoteReqBaseModel {
    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     * 作息状态
     */
    private String zxzt;
    /**
     * 作息名称
     */
    private String zxmc;
    /**
     *
     */
    @NotNull(message = "不能为空")
    private Integer pageNum;
    /**
     *
     */
    @NotNull(message = "不能为空")
    private Integer pageSize;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}