/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.resp.work;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 添加子项-作息响应
 *
 * <AUTHOR>
 * @version $Id: WorkAddItemRespModel.java, v 0.1 2022年10月08日 09时38分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkAddItemRespModel extends RemoteRespBaseModel {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}