/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.model.dto.standarddata;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 模板下载dto
 * <AUTHOR>
 * @version $Id: TemplateDownloadDTO.java, v 0.1 2022年09月29日 15时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TemplateDownloadDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 模板编号 */
    @NotBlank(message = "模板编号不能为空")
    private String mbbh;
    /** 模板类型 */
    @NotBlank(message = "模板类型不能为空")
    private String mblx;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
