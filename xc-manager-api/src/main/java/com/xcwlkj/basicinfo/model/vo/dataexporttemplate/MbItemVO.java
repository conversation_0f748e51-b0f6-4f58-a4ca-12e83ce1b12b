/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.model.vo.dataexporttemplate;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 模板列表vo
 * <AUTHOR>
 * @version $Id: MbItemVO.java, v 0.1 2022年10月27日 14时49分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class MbItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 配置编号 */
    private String pzbh;
    /** 配置名称 */
    private String pzmc;
    /** 导出sql语句 */
    private String dcsql;
    /** 模板地址 */
    private String mbdz;
    /** 模板类型 */
    private String mblx;
    /** 导出文件名称 */
    private String dcwjmc;
    /** 入参列表 */
    private String rc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
