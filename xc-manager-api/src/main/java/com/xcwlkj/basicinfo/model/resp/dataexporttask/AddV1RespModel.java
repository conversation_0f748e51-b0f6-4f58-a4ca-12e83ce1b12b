/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.model.resp.dataexporttask;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 新建导出任务响应
 * <AUTHOR>
 * @version $Id: AddV1RespModel.java, v 0.1 2022年10月27日 15时22分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddV1RespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 任务编号 */
    private String bh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}