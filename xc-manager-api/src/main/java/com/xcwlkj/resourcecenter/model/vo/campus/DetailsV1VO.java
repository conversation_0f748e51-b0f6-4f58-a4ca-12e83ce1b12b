/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.campus;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 校区-详情vo
 * <AUTHOR>
 * @version $Id: DetailsV1VO.java, v 0.1 2022年09月29日 14时19分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DetailsV1VO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 校区号 */
    private String xqh;
    /** 学校代码 */
    private String xxdm;
    /** 校区名称 */
    private String xqmc;
    /** 校区地址 */
    private String xqdz;
    /** 校区邮政编码 */
    private String xqyzbm;
    /** 校区联系电话 */
    private String xqlxdh;
    /** 校区传真电话 */
    private String xqczdh;
    /** 校区负责人号 */
    private String xqfzrh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
