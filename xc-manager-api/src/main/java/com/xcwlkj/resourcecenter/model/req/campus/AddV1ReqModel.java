/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.campus;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 校区-添加请求
 * <AUTHOR>
 * @version $Id: AddV1ReqModel.java, v 0.1 2022年09月29日 13时54分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddV1ReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 校区号 */
    @NotBlank(message = "校区号不能为空")
    private String xqh;
    /** 学校代码 */
    @NotBlank(message = "学校代码不能为空")
    private String xxdm;
    /** 校区名称 */
    @NotBlank(message = "校区名称不能为空")
    private String xqmc;
    /** 校区地址 */
    private String xqdz;
    /** 校区邮政编码 */
    private String xqyzbm;
    /** 校区联系电话 */
    private String xqlxdh;
    /** 校区传真电话 */
    private String xqczdh;
    /** 校区负责人号 */
    private String xqfzrh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}