/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.room;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import com.xcwlkj.resourcecenter.model.dto.room.JsxxDTO;
import com.xcwlkj.resourcecenter.model.dto.room.KcxxDTO;


/**
 * 场地-添加请求
 * <AUTHOR>
 * @version $Id: AddV1ReqModel.java, v 0.1 2022年10月08日 14时51分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddV1ReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 房间号             学校自编，建议前 6 位为建筑物编号，后 6 位为房间在该建筑物内的编号 */
    @NotBlank(message = "房间号             学校自编，建议前 6 位为建筑物编号，后 6 位为房间在该建筑物内的编号不能为空")
    private String fjh;
    /** 房间名称 */
    @NotBlank(message = "房间名称不能为空")
    private String fjmc;
    /** 建筑物号 */
    @NotBlank(message = "建筑物号不能为空")
    private String jzwh;
    /** 房间用途码 */
    @NotBlank(message = "房间用途码不能为空")
    private String fjytm;
    /** 房间楼层 */
    private String fjlc;
    /** 房间建筑面积 */
    private BigDecimal fjjzmj;
    /** 房间使用面积 */
    private BigDecimal fjsymj;
    /** 是否属于教室 */
    @NotNull(message = "是否属于教室不能为空")
    private Boolean sfjs;
    /** 教室信息，仅当属于教室时填写 */
    @Valid
    private JsxxDTO jsxx;
    /** 是否属于考场 */
    @NotNull(message = "是否属于考场不能为空")
    private Boolean sfkc;
    /** 考场信息，仅当属于考场时填写 */
    @Valid
    private KcxxDTO kcxx;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}