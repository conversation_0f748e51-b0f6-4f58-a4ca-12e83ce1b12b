/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.camera;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 摄像机状态列表vo
 *
 * <AUTHOR>
 * @version $Id: SbItemVO.java, v 0.1 2023年05月17日 15时24分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SbItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 设备名称
     */
    private String sbmc;
    /**
     * 关联教室
     */
    private String classroom;
    /**
     * 网络地址
     */
    private String wldz;
    /**
     * 设备状态
     */
    private String sbzt;
    /**
     * 1-评估，2-考场
     */
    private String yt;
    /**
     * 录像状态
     */
    private String lxzt;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
