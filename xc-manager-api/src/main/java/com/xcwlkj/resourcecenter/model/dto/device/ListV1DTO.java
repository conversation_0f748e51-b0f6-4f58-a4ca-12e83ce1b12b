/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.dto.device;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;


/**
 * 设备-列表dto
 * <AUTHOR>
 * @version $Id: ListV1DTO.java, v 0.1 2022年10月09日 11时52分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ListV1DTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备名称 */
    private String sbmc;
    /** 序列号 */
    private String xlh;
    /** 设备类型 */
    private String sblx;
    /** 设备状态 */
    private String sbzt;
    /** 校区号 */
    private String xqh;
    /** 建筑物号 */
    private String jzwh;
    /** 设备绑定的场所编号（教室号） */
    private String csbh;
    /** 页码 */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;
    /** 页大小 */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
