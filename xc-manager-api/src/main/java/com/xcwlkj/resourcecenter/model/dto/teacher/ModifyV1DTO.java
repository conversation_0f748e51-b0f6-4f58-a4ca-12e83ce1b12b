/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.dto.teacher;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 教职工-编辑dto
 * <AUTHOR>
 * @version $Id: ModifyV1DTO.java, v 0.1 2022年10月08日 13时16分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ModifyV1DTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 工号 */
    @NotBlank(message = "工号不能为空")
    private String gh;
    /** 单位号 */
    @NotBlank(message = "单位号不能为空")
    private String dwh;
    /** 校区号 */
    private String xqh;
    /** 姓名 */
    @NotBlank(message = "姓名不能为空")
    private String xm;
    /** 性别码 */
    @NotBlank(message = "性别码不能为空")
    private String xbm;
    /** 民族码 */
    private String mzm;
    /** 身份证件类型码 */
    @NotBlank(message = "身份证件类型码不能为空")
    private String sfzjlxm;
    /** 身份证件号 */
    @NotBlank(message = "身份证件号不能为空")
    private String sfzjh;
    /** 政治面貌码 */
    private String zzmmm;
    /** 照片 */
    private String zp;
    /** 教职工类别码 */
    @NotBlank(message = "教职工类别码不能为空")
    private String jzglbm;
    /** 职位/职称 */
    private String zwzc;
    /** 职务类别码 */
    private String zwlbm;
    /** 岗位类别码 */
    private String gwlbm;
    /** 任课状况码   0:未任科，1：已任课 */
    private String rkzkm;
    /** 当前状态码   0：未在职，1：在职 */
    @NotBlank(message = "当前状态码   0：未在职，1：在职不能为空")
    private String dqztm;
    /** 英文姓名 */
    private String ywxm;
    /** 姓名拼音 */
    private String xmpy;
    /** 曾用名 */
    private String cym;
    /** 出生日期 */
    private String csrq;
    /** 出生地码 */
    private String csdm;
    /** 籍贯 */
    private String jg;
    /** 国籍/地区码 */
    private String gjdqm;
    /** 婚姻状况码 */
    private String hyzkm;
    /** 港澳台侨外码 */
    private String gatqwm;
    /** 健康状况码 */
    private String jkzkm;
    /** 信仰宗教码 */
    private String xyzjm;
    /** 血型码 */
    private String xxm;
    /** 身份证件有效期 */
    private String sfzjyxq;
    /** 文化程度码 */
    private String whcdm;
    /** 参加工作年月 */
    private String cjgzny;
    /** 来校日期 */
    private String lxrq;
    /** 编制类别码 */
    private String bzlbm;
    /** 学科类别码 */
    private String xklbm;
    /** 一级学科码 */
    private String yjxkm;
    /** 二级学科码 */
    private String ejxkm;
    /** 研究方向 */
    private String yjfx;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
