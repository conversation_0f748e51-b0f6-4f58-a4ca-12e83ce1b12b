/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.schoolresource;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 校本资源-添加请求
 * <AUTHOR>
 * @version $Id: AddV1ReqModel.java, v 0.1 2022年10月09日 14时53分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddV1ReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 资源类别 */
    @NotBlank(message = "资源类别不能为空")
    private String zylb;
    /** 资源名称 */
    @NotBlank(message = "资源名称不能为空")
    private String zymc;
    /** 资源简介 */
    private String zyjj;
    /** 教职工姓名 */
    private String jzgxm;
    /** 原创教职工号 */
    private String ycjzgh;
    /** 所属单位号 */
    private String ssdwh;
    /** 原始文件名称 */
    @NotBlank(message = "原始文件名称不能为空")
    private String yswjmc;
    /** 新文件名称 */
    @NotBlank(message = "新文件名称不能为空")
    private String xwjmc;
    /** 文件存储路径  id */
    @NotBlank(message = "文件存储路径  id不能为空")
    private String wjcclj;
    /** 文件类型 */
    @NotBlank(message = "文件类型不能为空")
    private String wjlx;
    /** 封面图名 */
    private String fmtm;
    /** 封面图路径  id */
    private String fmtlj;
    /** 备注 */
    private String bz;
    /** 学科类别码 */
    private String xklbm;
    /** 一级学科码 */
    private String yjxkm;
    /** 二级学科码 */
    private String ejxkm;
    /** 专业号 */
    @NotBlank(message = "专业号不能为空")
    private String zyh;
    /** 专业名称 */
    @NotBlank(message = "专业名称不能为空")
    private String zymc2;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}