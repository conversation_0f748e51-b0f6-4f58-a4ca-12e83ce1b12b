/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.examroom;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 场地-考场-详情vo
 * <AUTHOR>
 * @version $Id: DetailsV1VO.java, v 0.1 2022年10月09日 09时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DetailsV1VO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 教室号 */
    private String jsh;
    /** 是否标准化考场 */
    private String sfbzhkc;
    /** 标准化考点标志码 */
    private String bzhkdid;
    /** 标准化考场标志码 */
    private String bzhkcid;
    /** 标准化考场名称 */
    private String bzhkcmc;
    /** 考场容量 */
    private Integer kxrl;
    /** 座次起始位置码 */
    private String zcqswzm;
    /** 座次起始位置 */
    private String zcqswz;
    /** 座位布局方式码 */
    private String zwbjfsm;
    /** 座位布局方式 */
    private String zwbjfs;
    /** 座位排列方式码 */
    private String zwplfsm;
    /** 座位排列方式 */
    private String zwplfs;
    /** 考场实际位置 */
    private String kcsjwz;
    /** 建筑名称 */
    private String jzmc;
    /** 所在楼层 */
    private String szlc;
    /** 是否残障考场(备用考场) */
    private String czkc;
    /** 考场所在房间编号 */
    private String kcszfjh;
    /** 考场管理部门 */
    private String kcglbm;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
