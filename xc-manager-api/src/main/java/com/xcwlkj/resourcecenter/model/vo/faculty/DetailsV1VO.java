/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.faculty;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 院系-详情vo
 * <AUTHOR>
 * @version $Id: DetailsV1VO.java, v 0.1 2022年10月08日 10时19分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DetailsV1VO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 单位号 */
    private String dwh;
    /** 单位名称 */
    private String dwmc;
    /** 单位英文名称 */
    private String dwywmc;
    /** 单位简称 */
    private String dwjc;
    /** 单位英文简称 */
    private String dwywjc;
    /** 单位简拼 */
    private String dwjp;
    /** 单位地址 */
    private String dwdz;
    /** 隶属单位号 */
    private String lsdwh;
    /** 校区号 */
    private String xqh;
    /** 单位层级             1：校级             2：院系级 */
    private String dwcj;
    /** 单位类别码 */
    private String dwlbm;
    /** 单位办别码 */
    private String dwbbm;
    /** 单位有效标识             1 是 (有效)              0 否 (失效) */
    private String dwyxbs;
    /** 失效日期 */
    private String sxrq;
    /** 是否实体             1 是              0 否（即虚体） */
    private String sfst;
    /** 建立年月             格式：YYYYMM */
    private String jlny;
    /** 单位负责人号 */
    private String dwfzrh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
