/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.dicttype;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 字典类型列表vo
 *
 * <AUTHOR>
 * @version $Id: DictItemVO.java, v 0.1 2022年10月08日 09时49分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DictTypeItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;
    /**
     * 字典类型名称
     */
    private String name;
    /**
     * 字典类型
     */
    private String type;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
