/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.resp.examroom;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.resourcecenter.model.vo.examroom.ExamroomItemVO;
import java.util.List;


/**
 * 场地-考场-列表响应
 * <AUTHOR>
 * @version $Id: ListV1RespModel.java, v 0.1 2022年10月09日 09时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListV1RespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 考场列表 */
    private List<ExamroomItemVO> examroomList;
    /** 总条数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}