/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.dto.bpgl;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 班牌远程控制-定时开关机dto
 * <AUTHOR>
 * @version $Id: SjgzItemDTO.java, v 0.1 2023年07月26日 10时49分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SjgzItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 场景，off关、on开（运行模式时，场景：EDU教学模式、EXAM考试模式） */
    @NotBlank(message = "场景，off关、on开（运行模式时，场景：EDU教学模式、EXAM考试模式）不能为空")
    private String cj;
    /** 执行时间  HH:mm:mm 精确到秒 */
    @NotBlank(message = "执行时间  HH:mm:mm 精确到秒 不能为空")
    private String zxsj;
    /** 时长，单位秒，-1表示时间不限制 */
    @NotBlank(message = "时长，单位秒，-1表示时间不限制不能为空")
    private Integer sc;
    /** 重复周期，星期一到星期日，以逗号隔开, 例:1,2,3,4,5,6,7 */
    @NotBlank(message = "重复周期，星期一到星期日，以逗号隔开, 例:1,2,3,4,5,6,7不能为空")
    private String cfzq;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
