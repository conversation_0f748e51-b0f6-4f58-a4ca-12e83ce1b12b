/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.resp.classroom;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.resourcecenter.model.vo.classroom.XqItemVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;


/**
 * 查询教室信息vo
 * <AUTHOR>
 * @version $Id: TreeV1VO.java, v 0.1 2022年10月13日 10时26分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TreeV1RespModel extends RemoteRespBaseModel {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 校区列表 */
    private List<XqItemVO> xqList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
