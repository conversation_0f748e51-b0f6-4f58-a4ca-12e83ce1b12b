/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.dictdata;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 更新字典数据请求
 *
 * <AUTHOR>
 * @version $Id: DictDataUpdateReqModel.java, v 0.1 2022年10月08日 09时50分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DictDataUpdateReqModel extends RemoteReqBaseModel {
    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;
    /**
     * 字典标签
     */
    @NotBlank(message = "字典标签不能为空")
    private String dictLabel;
    /**
     * 字典键值
     */
    @NotBlank(message = "字典键值不能为空")
    private String dictValue;
    /**
     * 字典类型id
     */
    @NotBlank(message = "字典类型id不能为空")
    private String dictTypeId;
    /**
     * 字典类型
     */
    @NotBlank(message = "字典类型不能为空")
    private String dictType;
    /**
     * 样式属性
     */
    private String cssClass;
    /**
     * 表格回显样式
     */
    private String listClass;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 是否默认
     */
    private Integer isDefault;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}