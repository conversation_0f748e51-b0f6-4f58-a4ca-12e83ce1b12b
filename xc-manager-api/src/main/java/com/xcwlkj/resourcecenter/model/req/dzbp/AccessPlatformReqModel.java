/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.dzbp;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 接入配置请求
 * <AUTHOR>
 * @version $Id: AccessPlatformReqModel.java, v 0.1 2023年07月14日 10时31分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AccessPlatformReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;


    /** 设备编号列表 */
    @NotNull(message = "设备编号列表不能为空")
    private List<String> sbbhList;
    /** 接入地址 */
    @NotBlank(message = "接入地址不能为空")
    private String address;
    /** 端口 */
    @NotBlank(message = "端口不能为空")
    private String port;
    /** 是否发布 0：否，1：是 */
    @NotNull(message = "是否发布 0：否，1：是不能为空")
    private Integer sffb;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}