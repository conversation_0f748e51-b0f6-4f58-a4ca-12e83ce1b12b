/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.dto.bpgl;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 班牌远程控制-定时开关机dto
 * <AUTHOR>
 * @version $Id: CommandTimingBootDTO.java, v 0.1 2023年07月26日 10时49分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CommandTimingBootDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备编号列表 */
    @NotNull(message = "设备编号列表不能为空")
    private List<String> sbbhList;
    /** 有效开始日期 yyyy-MM-dd */
    @NotNull(message = "有效开始日期 yyyy-MM-dd 不能为空")
    private String yxksrq;
    /** 有效结束日期 yyyy-MM-dd */
    @NotNull(message = "有效结束日期 yyyy-MM-dd 不能为空")
    private String yxjsrq;
    /** 时间规则列表 */
    @NotNull(message = "时间规则列表不能为空")
    private List<SjgzItemDTO> sjgzList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
