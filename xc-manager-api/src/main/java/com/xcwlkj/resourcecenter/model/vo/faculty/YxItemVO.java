/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.faculty;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 院系-列表vo
 * <AUTHOR>
 * @version $Id: YxItemVO.java, v 0.1 2022年10月08日 10时19分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class YxItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 单位号 */
    private String dwh;
    /** 单位名称 */
    private String dwmc;
    /** 单位简称 */
    private String dwjc;
    /** 隶属单位号 */
    private String lsdwh;
    /** 隶属单位名称 */
    private String lsdwmc;
    /** 校区号 */
    private String xqh;
    /** 校区名 */
    private String xqmc;
    /** 单位有效标识             1 是 (有效)              0 否 (失效) */
    private String dwyxbs;
    /** 失效日期 */
    private String sxrq;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
