/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.bpgl;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 命令日志列表vo
 * <AUTHOR>
 * @version $Id: CommandLogItemVO.java, v 0.1 2024年12月04日 13时44分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CommandLogItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 消息id */
    private String msgId;
    /** 类型 */
    private String type;
    /** 设备序列号 */
    private String sbxlh;
    /** 状态 */
    private Integer status;
    /** 消息 */
    private String message;
    /** 备注 */
    private String note;
    /** 返回值 */
    private String reply;
    /** 发送时间 */
    private String sendTime;
    /** 结束时间 */
    private String finishTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
