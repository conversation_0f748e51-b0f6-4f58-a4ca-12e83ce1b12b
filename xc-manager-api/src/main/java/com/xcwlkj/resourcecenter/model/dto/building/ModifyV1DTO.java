/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.dto.building;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;


/**
 * 楼房-编辑dto
 * <AUTHOR>
 * @version $Id: ModifyV1DTO.java, v 0.1 2022年10月08日 13时56分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ModifyV1DTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 建筑物号 */
    @NotBlank(message = "建筑物号不能为空")
    private String jzwh;
    /** 建筑物名称 */
    @NotBlank(message = "建筑物名称不能为空")
    private String jzwmc;
    /** 房屋产权码 */
    private String fwcqm;
    /** 使用状况码 */
    @NotBlank(message = "使用状况码不能为空")
    private String syzkm;
    /** 校区号 */
    @NotBlank(message = "校区号不能为空")
    private String xqh;
    /** 单位号 */
    private String dwh;
    /** 建筑物分类码 */
    private String jzwflm;
    /** 建筑物结构码 */
    private String jzwjgm;
    /** 建筑物层数 */
    private Integer jzwcs;
    /** 建成年月 */
    private String jcny;
    /** 建筑物投资总额 */
    private BigDecimal jzwtzze;
    /** 总建筑面积 */
    private BigDecimal zjzmj;
    /** 总使用面积 */
    private BigDecimal zsymj;
    /** 抗震设防烈度码 */
    private String kzsfldm;
    /** 抗震设防标准码 */
    private String kzsfbzm;
    /** 建筑物地址 */
    private String jzwdz;
    /** 建筑物状况码 */
    private String jzwzkm;
    /** 权属证号 */
    private String qszh;
    /** 建筑物占地面积 */
    private Integer jzwzdmj;
    /** 设计使用年限 */
    private Integer sjsynx;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
