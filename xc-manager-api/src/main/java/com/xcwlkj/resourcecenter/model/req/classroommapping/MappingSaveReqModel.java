/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.classroommapping;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import com.xcwlkj.resourcecenter.model.dto.classroommapping.ClassroomMappingItemDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教室映射-保存请求
 * <AUTHOR>
 * @version $Id: MappingSaveReqModel.java, v 0.1 2024年09月26日 13时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MappingSaveReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 教室映射列表 */
    @NotNull(message = "教室映射列表不能为空")
    private List<ClassroomMappingItemDTO> classroomMappingList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}