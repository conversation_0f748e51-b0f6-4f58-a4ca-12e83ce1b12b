/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.dzbp;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;


/**
 * 电子班牌设备运维信息详情请求
 * <AUTHOR>
 * @version $Id: DevOpsDetailReqModel.java, v 0.1 2024年09月29日 11时23分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DevOpsDetailReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 设备信息编号 */
    @NotBlank(message = "设备信息编号不能为空")
    private String sbxxbh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}