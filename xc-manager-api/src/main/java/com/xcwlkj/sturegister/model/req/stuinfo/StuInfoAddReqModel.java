/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.req.stuinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 添加学生基础信息请求
 *
 * <AUTHOR>
 * @version $Id: StuInfoAddReqModel.java, v 0.1 2022年09月29日 16时08分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StuInfoAddReqModel extends RemoteReqBaseModel {
    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     * 学号
     */
    @NotBlank(message = "学号不能为空")
    private String xh;
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String xm;
    /**
     * 性别码
     */
    @NotBlank(message = "性别码不能为空")
    private String xbm;
    /**
     * 名族码
     */
    private String mzm;
    /**
     * 身份证件类型码
     */
    private String sfzjlxm;
    /**
     * 身份证件号
     */
    private String sfzjh;
    /**
     * 政治面貌码
     */
    private String zzmmm;
    /**
     * 照片
     */
    private String zp;
    /**
     * 英文姓名
     */
    private String ywxm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 曾用名
     */
    private String cym;
    /**
     * 出生日期
     */
    private String csrq;
    /**
     * 出生地码
     */
    private String csdm;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 国籍/地区码
     */
    private String gjdqm;
    /**
     * 婚姻状况码
     */
    private String hyzkm;
    /**
     * 港澳台侨外码
     */
    private String gatqwm;
    /**
     * 健康状况码
     */
    private String jkzkm;
    /**
     * 信仰宗教码
     */
    private String xyzjm;
    /**
     * 血型码
     */
    private String xxm;
    /**
     * 身份证件有效期
     */
    private String sfzjyxq;
    /**
     * 是否独生子女
     */
    private String sfdszn;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}