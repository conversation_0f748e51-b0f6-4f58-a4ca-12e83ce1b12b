/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.resp.schoolrollinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.sturegister.model.vo.schoolrollinfo.SchoolRollInfoItemVO;
import java.util.List;


/**
 * 学籍基本信息列表响应
 * <AUTHOR>
 * @version $Id: SchoolRollInfoListRespModel.java, v 0.1 2023年02月06日 09时29分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SchoolRollInfoListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /**  */
    private List<SchoolRollInfoItemVO> schoolRollInfoList;
    /**  */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}