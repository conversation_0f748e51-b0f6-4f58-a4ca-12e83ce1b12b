/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.req.electiveclass;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 详情选修请求
 *
 * <AUTHOR>
 * @version $Id: ElectiveDetailReqModel.java, v 0.1 2022年09月29日 13时30分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ElectiveDetailReqModel extends RemoteReqBaseModel {
    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     *
     */
    @NotBlank(message = "不能为空")
    private String bah;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}