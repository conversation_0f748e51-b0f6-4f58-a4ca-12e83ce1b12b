/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.resp.quarter;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.sturegister.model.vo.quarter.QuarterItemVO;

import java.util.List;


/**
 * 学年学期列表响应
 *
 * <AUTHOR>
 * @version $Id: QuarterListRespModel.java, v 0.1 2022年09月29日 09时47分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuarterListRespModel extends RemoteRespBaseModel {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     *
     */
    private List<QuarterItemVO> quarterList;
    /**
     *
     */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}