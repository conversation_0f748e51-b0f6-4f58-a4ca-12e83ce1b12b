/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.dto.quarter;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 修改学年学期dto
 *
 * <AUTHOR>
 * @version $Id: QuarterUpdateDTO.java, v 0.1 2022年09月29日 09时37分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuarterUpdateDTO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 年度
     */
    @NotBlank(message = "年度不能为空")
    private String nd;
    /**
     * 学年
     */
    @NotBlank(message = "学年不能为空")
    private String xn;
    /**
     * 学期码
     */
    @NotBlank(message = "学期码不能为空")
    private String xqm;
    /**
     * 开始日期
     */
    @NotBlank(message = "开始日期不能为空")
    private String ksrq;
    /**
     * 结束日期
     */
    @NotBlank(message = "结束日期不能为空")
    private String jsrq;
    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空")
    private String bh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
