/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.resp.stuinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 删除学生基础信息响应
 *
 * <AUTHOR>
 * @version $Id: StuInfoDeleteRespModel.java, v 0.1 2022年09月29日 16时09分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StuInfoDeleteRespModel extends RemoteRespBaseModel {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}