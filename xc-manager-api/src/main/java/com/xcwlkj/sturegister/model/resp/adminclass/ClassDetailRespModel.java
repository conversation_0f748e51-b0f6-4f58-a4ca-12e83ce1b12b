/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.resp.adminclass;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 详情班级响应
 * <AUTHOR>
 * @version $Id: ClassDetailRespModel.java, v 0.1 2023年02月10日 15时38分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClassDetailRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 班级名称 */
    private String bjmc;
    /** 班级编号 */
    private String bah;
    /** 专业代码 */
    private String zydm;
    /** 专业名称 */
    private String zymc;
    /** 专业方向代码 */
    private String zyfxdm;
    /** 学科门类码 */
    private String xkmlm;
    /** 建班年月 */
    private String jbny;
    /** 所属年级 */
    private String ssnj;
    /** 班主任工号 */
    private String bzrgh;
    /** 班长学号 */
    private String bzxh;
    /** 辅导员号 */
    private String fdyh;
    /** 是否订单班 */
    private String sfddb;
    /** 单位号 */
    private String dwh;
    /** 单位名称 */
    private String dwmc;
    /** 教室号 */
    private String jsh;
    /** 教室名称 */
    private String jsmc;
    /** 别名 */
    private String alias;
    /** 口号 */
    private String slogan;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}