/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.dto.electiveclass;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 添加选修班dto
 *
 * <AUTHOR>
 * @version $Id: ElectiveAddDTO.java, v 0.1 2022年09月29日 13时29分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ElectiveAddDTO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 班级名称
     */
    @NotBlank(message = "班级名称不能为空")
    private String bjmc;
    /**
     *
     */
    @NotBlank(message = "不能为空")
    private String zydm;
    /**
     * 专业方向代码
     */
    private String zyfxdm;
    /**
     * 建班年月
     */
    @NotBlank(message = "建班年月不能为空")
    private String jbny;
    /**
     * 所属年级
     */
    private String ssnj;
    /**
     * 班主任工号
     */
    private String bzrgh;
    /**
     * 班长学号
     */
    private String bzxh;
    /**
     * 辅导员号
     */
    private String fdyh;
    /**
     * 是否订单班
     */
    private String sfddb;
    /**
     * 单位号
     */
    @NotBlank(message = "单位号不能为空")
    private String dwh;
    /**
     * 选修班标记      0：行政班      1：选修班
     */
    @NotBlank(message = "选修班标记      0：行政班      1：选修班不能为空")
    private String xxbbj;
    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空")
    private String bah;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
