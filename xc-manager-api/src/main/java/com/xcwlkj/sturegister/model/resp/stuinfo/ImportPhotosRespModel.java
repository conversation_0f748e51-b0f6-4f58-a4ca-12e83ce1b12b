/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.resp.stuinfo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 学生-批量导入照片响应
 * <AUTHOR>
 * @version $Id: ImportPhotosRespModel.java, v 0.1 2025年06月05日 14时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImportPhotosRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}