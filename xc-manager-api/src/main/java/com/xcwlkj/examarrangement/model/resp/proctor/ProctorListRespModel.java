/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.resp.proctor;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.examarrangement.model.vo.proctor.ProctorItemVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;


/**
 * 监考人信息列表响应
 * <AUTHOR>
 * @version $Id: ProctorListRespModel.java, v 0.1 2024年08月02日 11时08分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProctorListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 监考人信息列表 */
    private List<ProctorItemVO> proctorList;
    /** 总数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}