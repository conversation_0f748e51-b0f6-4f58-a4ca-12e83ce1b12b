/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.req.proctorexamarrange;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;


/**
 * 监考人考试编排修改请求
 * <AUTHOR>
 * @version $Id: ProctorExamArrangeModifyReqModel.java, v 0.1 2024年08月02日 10时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProctorExamArrangeModifyReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 编号 */
    @NotBlank(message = "编号不能为空")
    private String bh;
    /** 考试计划 */
    @NotBlank(message = "考试计划不能为空")
    private String ksjh;
    /** 考试名称 */
    @NotBlank(message = "考试名称不能为空")
    private String ksmc;
    /** 考试课程      课程名称模糊搜索该考试计划下的考试课程计划，进行选择eg：大学英语(1000001)/第1批/2023-03-29 8:20-9:50 */
    @NotBlank(message = "考试课程      课程名称模糊搜索该考试计划下的考试课程计划，进行选择eg：大学英语(1000001)/第1批/2023-03-29 8:20-9:50不能为空")
    private String kskc;
    /** 考试教室号 */
    @NotBlank(message = "考试教室号不能为空")
    private String ksjsh;
    /** 教室     Eg: 东校区>东九楼>A栋302教室 */
    @NotBlank(message = "教室     Eg: 东校区>东九楼>A栋302教室不能为空")
    private String js;
    /** 考场号 */
    @NotBlank(message = "考场号不能为空")
    private String kch;
    /** 标准化考场编号 */
    @NotBlank(message = "标准化考场编号不能为空")
    private String bzhkcid;
    /** 监考人工号 */
    @NotBlank(message = "监考人工号不能为空")
    private String jkrgh;
    /** 监考人姓名 */
    @NotBlank(message = "监考人姓名不能为空")
    private String jkrxm;
    /** 状态 */
    @NotBlank(message = "状态不能为空")
    private String zt;
    /** 监考岗位职责 2001	监考 2002	场外监试	 2003	考点考务	 2004	考区考务	 2005	指挥舱预留 */
    @NotBlank(message = "监考岗位职责 2001	监考 2002	场外监试	 2003	考点考务	 2004	考区考务	 2005	指挥舱预留不能为空")
    private String jkgwzz;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}