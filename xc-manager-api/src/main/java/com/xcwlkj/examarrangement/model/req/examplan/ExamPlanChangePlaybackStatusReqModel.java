/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.req.examplan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 考试计划启用/禁用视频播放请求
 * <AUTHOR>
 * @version $Id: ExamPlanChangePlaybackStatusReqModel.java, v 0.1 2024年09月27日 15时22分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExamPlanChangePlaybackStatusReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 是否禁用视频播放 0-禁用 1-启用 */
    @NotNull(message = "是否禁用视频播放 0-禁用 1-启用不能为空")
    private Integer sfjy;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}