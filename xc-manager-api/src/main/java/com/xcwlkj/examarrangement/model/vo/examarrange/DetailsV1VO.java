/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.vo.examarrange;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 考试编排-详情vo
 * <AUTHOR>
 * @version $Id: DetailsV1VO.java, v 0.1 2022年10月10日 14时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DetailsV1VO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;

    /** 编排编号 */
    private String bh;
    /** 考试计划编号 */
    private String ksjhbh;
    /** 考试名称 */
    private String ksmc;
    /** 考试课程计划编号 */
    private String kskcjhbh;
    /** 课程号 */
    private String kch;
    /** 标准化考场编号 */
    private String bzhkcid;
    /** 课程名称 */
    private String kcmc;
    /** 考试时间 */
    private String kssj;
    /** 考场号 */
    private String kach;
    /** 考试教室号 */
    private String ksjsh;
    /** 教室名称 */
    private String jsmc;
    /** 座位号 */
    private String zwh;
    /** 学号 */
    private String xh;
    /** 考生号 */
    private String ksh;
    /** 学生姓名 */
    private String xsxm;
    /** 准考证号 */
    private String zkzh;
    /** 状态 */
    private String zt;
    /** 创建时间 */
    private String cjsj;
    /** 修改时间 */
    private String xgsj;
    /** 考试批次号 */
    private Integer kspch;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
