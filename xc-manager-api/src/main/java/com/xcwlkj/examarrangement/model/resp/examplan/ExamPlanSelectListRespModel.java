/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.resp.examplan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.examarrangement.model.vo.examplan.PlanItemVO;
import java.util.List;


/**
 * 考试计划下拉框（导入考生/监考人照片用）响应
 * <AUTHOR>
 * @version $Id: ExamPlanSelectListRespModel.java, v 0.1 2023年09月08日 13时43分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExamPlanSelectListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 考试计划下拉框列表 */
    private List<PlanItemVO> planList;
    /** 总数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}