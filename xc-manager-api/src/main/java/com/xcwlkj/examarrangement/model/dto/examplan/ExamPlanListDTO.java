/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.dto.examplan;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 考试计划列表dto
 *
 * <AUTHOR>
 * @version $Id: ExamPlanListDTO.java, v 0.1 2022年10月08日 09时44分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ExamPlanListDTO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 考试类型 1-校内考试  2-统一考试
     */
    private String kslx;
    /**
     * 考试名称
     */
    private String ksmc;
    /**
     * 学年
     */
    private String xn;
    /**
     * 学期
     */
    private String xq;
    /**
     * 状态 0-未启用  1-已启用  2-已结束
     */
    private List<String> zt;
    /**
     *
     */
    @NotNull(message = "不能为空")
    private Integer pageNum;
    /**
     *
     */
    @NotNull(message = "不能为空")
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
