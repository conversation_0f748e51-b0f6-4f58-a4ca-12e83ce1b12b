/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.console.model.resp.systemconfig;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.console.model.vo.systemconfig.SystemConfigDetailVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询system_config详情响应
 * <AUTHOR>
 * @version $Id: QuerySystemConfigDetailByIdRespModel.java, v 0.1 2019年08月25日 11时32分 XcDev Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuerySystemConfigDetailByIdRespModel extends RemoteRespBaseModel{

    /**  */
    private static final long serialVersionUID = -1L;

    private SystemConfigDetailVO systemConfigDetailVO;

}
