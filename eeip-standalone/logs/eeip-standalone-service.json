{"@timestamp":"2025-07-29T02:20:29.360Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:20:31.965Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T02:20:31.975Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T02:20:41.303Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T02:20:41.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T02:20:42.681Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T02:20:42.891Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T02:20:43.190Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-29T02:20:43.231Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-29T02:20:43.231Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-29T02:20:54.334Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T02:20:57.526Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 29.846 seconds (JVM running for 31.239)"}
{"@timestamp":"2025-07-29T02:20:58.581Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：213，摘要信息：KkwTjInfoVO[total=57,ysb=0,wsb=57,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T02:20:58.730Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T02:20:58.783Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"36300","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T02:22:57.411Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$17d0bfc6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:23:00.046Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T02:23:00.056Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T02:23:08.408Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T02:23:08.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T02:23:09.821Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T02:23:10.021Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T02:23:10.316Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-29T02:23:10.361Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-29T02:23:10.361Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-29T02:23:21.106Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T02:23:24.588Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 28.778 seconds (JVM running for 29.981)"}
{"@timestamp":"2025-07-29T02:23:25.676Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：213，摘要信息：KkwTjInfoVO[total=57,ysb=0,wsb=57,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T02:23:25.736Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T02:23:25.772Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T02:23:25.789Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T02:23:25.820Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T02:23:25.841Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4960","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T02:28:15.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$cff26a67] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T02:28:18.500Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T02:28:18.506Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"com.xcwlkj.manager.CxtjControllerTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T02:28:26.821Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T02:28:26.920Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T02:28:28.153Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T02:28:28.368Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T02:28:28.692Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-29T02:28:28.746Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-29T02:28:28.746Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-29T02:28:39.489Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T02:28:42.546Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"com.xcwlkj.manager.CxtjControllerTest","rest":"Started CxtjControllerTest in 28.11 seconds (JVM running for 29.603)"}
{"@timestamp":"2025-07-29T02:28:42.661Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@5454d35e] to prepare test instance [com.xcwlkj.manager.CxtjControllerTest@68caf7f4]"}
{"@timestamp":"2025-07-29T02:28:42.696Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@5454d35e] to prepare test instance [com.xcwlkj.manager.CxtjControllerTest@1d591f58]"}
{"@timestamp":"2025-07-29T02:28:42.716Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@5454d35e] to prepare test instance [com.xcwlkj.manager.CxtjControllerTest@58e6940]"}
{"@timestamp":"2025-07-29T02:28:42.739Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@5454d35e] to prepare test instance [com.xcwlkj.manager.CxtjControllerTest@1d430022]"}
{"@timestamp":"2025-07-29T02:28:42.775Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@5454d35e] to prepare test instance [com.xcwlkj.manager.CxtjControllerTest@5475de4]"}
{"@timestamp":"2025-07-29T02:28:42.800Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22164","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@5454d35e] to prepare test instance [com.xcwlkj.manager.CxtjControllerTest@60c972b9]"}
{"@timestamp":"2025-07-29T03:14:10.580Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:14:11.463Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:14:11.469Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:14:20.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:14:20.592Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:14:21.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:14:22.049Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:14:22.508Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:14:33.565Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:14:36.683Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 27.697 seconds (JVM running for 29.011)"}
{"@timestamp":"2025-07-29T03:14:37.790Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:14:37.792Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3561c410] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@4a84239a]"}
{"@timestamp":"2025-07-29T03:14:38.198Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:14:38.573Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:14:38.575Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:14:41.884Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:14:41.893Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:14:43.048Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:14:43.064Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:14:43.324Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:14:51.500Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:14:53.824Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 16.016 seconds (JVM running for 46.152)"}
{"@timestamp":"2025-07-29T03:14:54.878Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:14:54.879Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3561c410] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@50e984d7]"}
{"@timestamp":"2025-07-29T03:14:55.251Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:14:55.616Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:14:55.618Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:14:58.758Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:14:58.765Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:15:00.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:15:00.164Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:15:00.313Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:15:08.628Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:15:11.041Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 16.157 seconds (JVM running for 63.369)"}
{"@timestamp":"2025-07-29T03:15:12.080Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:15:12.081Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3561c410] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@777fb3b2]"}
{"@timestamp":"2025-07-29T03:15:12.467Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:15:12.840Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:15:12.842Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:15:16.015Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:15:16.023Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:15:17.417Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:15:17.435Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:15:17.617Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:15:25.935Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:15:28.161Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 16.075 seconds (JVM running for 80.489)"}
{"@timestamp":"2025-07-29T03:15:29.205Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:15:29.206Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3561c410] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@5d5cffd4]"}
{"@timestamp":"2025-07-29T03:15:29.591Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:15:29.935Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:15:29.937Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:15:32.904Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:15:32.913Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:15:34.233Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:15:34.252Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:15:34.374Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:15:42.782Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:15:45.054Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 15.843 seconds (JVM running for 97.383)"}
{"@timestamp":"2025-07-29T03:15:46.099Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:15:46.100Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3561c410] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@f97aab9]"}
{"@timestamp":"2025-07-29T03:15:46.456Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:15:46.809Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:15:46.811Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:15:49.749Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:15:49.757Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:15:51.100Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:15:51.117Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:15:51.268Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:15:58.925Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:16:01.116Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 15.012 seconds (JVM running for 113.444)"}
{"@timestamp":"2025-07-29T03:16:02.167Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:16:02.168Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27692","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3561c410] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@44b43556]"}
{"@timestamp":"2025-07-29T03:21:46.016Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e06471cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:21:48.641Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:21:48.647Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:21:57.158Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:21:57.251Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:21:58.461Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:21:58.646Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:21:59.057Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:22:09.760Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:22:12.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 28.207 seconds (JVM running for 29.43)"}
{"@timestamp":"2025-07-29T03:22:13.844Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:22:13.847Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3148f668] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@4ab2f15c]"}
{"@timestamp":"2025-07-29T03:22:14.250Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e06471cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:22:16.659Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:22:16.662Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:22:19.956Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:22:19.966Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:22:20.972Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:22:20.990Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:22:21.196Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"检查平台类型或初始化上级平台DFS客户端时发生异常"}
{"@timestamp":"2025-07-29T03:22:29.460Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:22:31.711Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 17.848 seconds (JVM running for 48.393)"}
{"@timestamp":"2025-07-29T03:22:32.762Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:22:32.764Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3148f668] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@a7b54d4]"}
{"@timestamp":"2025-07-29T03:22:33.133Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e06471cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:22:35.510Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:22:35.513Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22324","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:22:42.516Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:22:45.174Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:22:45.181Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:22:53.374Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:22:53.473Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:22:54.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:22:54.859Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:22:55.202Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:23:06.047Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:23:09.525Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 28.509 seconds (JVM running for 29.731)"}
{"@timestamp":"2025-07-29T03:23:10.720Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:23:10.724Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6e005dc9] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@41bb9bf6]"}
{"@timestamp":"2025-07-29T03:23:11.251Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:23:13.688Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:23:13.690Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:23:17.913Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:23:17.924Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:23:19.764Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:23:19.789Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:23:19.924Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:23:28.860Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:23:31.043Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 20.299 seconds (JVM running for 51.249)"}
{"@timestamp":"2025-07-29T03:23:32.128Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:23:32.130Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6e005dc9] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@359750c2]"}
{"@timestamp":"2025-07-29T03:23:32.520Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:23:34.898Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:23:34.900Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:23:38.327Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:23:38.334Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:23:40.130Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:23:40.147Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:23:40.275Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:23:50.331Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:23:52.428Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 20.293 seconds (JVM running for 72.635)"}
{"@timestamp":"2025-07-29T03:23:53.473Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:23:53.474Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6e005dc9] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@6d1f4ba2]"}
{"@timestamp":"2025-07-29T03:23:53.835Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:23:56.260Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:23:56.263Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:23:59.200Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:23:59.208Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:24:00.457Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:24:00.472Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:24:00.596Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:24:09.003Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:24:11.822Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 18.344 seconds (JVM running for 92.028)"}
{"@timestamp":"2025-07-29T03:24:12.915Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:24:12.915Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6e005dc9] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@73ad6a05]"}
{"@timestamp":"2025-07-29T03:24:13.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:24:15.872Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:24:15.872Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5360","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:24:20.670Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:24:23.275Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:24:23.282Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:24:31.742Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:24:31.839Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:24:33.050Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:24:33.235Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:24:33.654Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:24:44.409Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:24:47.287Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 28.072 seconds (JVM running for 29.3)"}
{"@timestamp":"2025-07-29T03:24:48.524Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:24:48.526Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6e005dc9] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@1461af21]"}
{"@timestamp":"2025-07-29T03:24:48.908Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:24:51.297Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:24:51.299Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:24:54.482Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:24:54.497Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:24:55.519Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:24:55.533Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:24:55.671Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:25:03.744Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:25:06.001Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 17.458 seconds (JVM running for 48.014)"}
{"@timestamp":"2025-07-29T03:25:07.070Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-29T03:25:07.071Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6e005dc9] to prepare test instance [com.xcwlkj.service.KsKkwMsgServiceTest@23942294]"}
{"@timestamp":"2025-07-29T03:25:07.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:25:09.848Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:25:09.848Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"23636","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:25:14.410Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:25:17.037Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:25:17.043Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:25:25.329Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:25:25.423Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:25:26.589Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:25:26.759Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:25:27.118Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:25:37.805Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:25:40.677Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 27.762 seconds (JVM running for 28.946)"}
{"@timestamp":"2025-07-29T03:25:41.009Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-29T03:25:41.070Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T03:25:41.114Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T03:25:41.140Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T03:25:41.179Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T03:25:41.208Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17588","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T03:32:44.095Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:32:46.682Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:32:46.690Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:32:56.703Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:32:56.826Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:32:58.494Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:32:58.773Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:32:59.213Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:33:13.328Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:33:17.306Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 34.772 seconds (JVM running for 35.971)"}
{"@timestamp":"2025-07-29T03:33:17.642Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-29T03:33:17.829Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T03:33:17.900Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33008","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T03:43:03.937Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T03:43:06.716Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T03:43:06.724Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T03:43:15.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T03:43:15.786Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T03:43:17.015Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T03:43:17.195Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T03:43:17.565Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T03:43:28.525Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T03:43:32.392Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 30.022 seconds (JVM running for 31.344)"}
{"@timestamp":"2025-07-29T03:43:32.739Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-29T03:43:32.831Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T03:43:32.883Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T03:43:32.898Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T03:43:32.945Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T03:43:32.983Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16620","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T05:32:45.226Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c224d153] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T05:32:47.938Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T05:32:47.944Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T05:32:56.847Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T05:32:56.950Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T05:32:58.319Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T05:32:58.529Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T05:32:58.895Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T05:33:10.727Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T05:33:13.826Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 30.265 seconds (JVM running for 31.564)"}
{"@timestamp":"2025-07-29T05:33:14.129Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-29T05:33:14.182Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T05:33:14.223Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T05:33:14.248Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T05:33:14.284Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：0，摘要信息：KkwTjInfoVO[total=0,ysb=0,wsb=0,reportRate=0.00%]"}
{"@timestamp":"2025-07-29T05:33:14.308Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1560","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
{"@timestamp":"2025-07-29T05:38:42.620Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b8df9598] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-29T05:38:43.254Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-29T05:38:43.262Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-29T05:38:51.837Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-29T05:38:52.141Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-29T05:38:53.471Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-29T05:38:53.661Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-29T05:38:54.240Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-29T05:39:05.522Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-29T05:39:08.578Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 27.531 seconds (JVM running for 28.631)"}
{"@timestamp":"2025-07-29T05:39:08.830Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-29T05:39:08.904Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：12"}
{"@timestamp":"2025-07-29T05:39:08.958Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：4，摘要信息：KkwTjInfoVO[total=5,ysb=4,wsb=1,reportRate=80.00%]"}
{"@timestamp":"2025-07-29T05:39:08.994Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：4"}
{"@timestamp":"2025-07-29T05:39:09.041Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：5，摘要信息：KkwTjInfoVO[total=5,ysb=4,wsb=1,reportRate=80.00%]"}
{"@timestamp":"2025-07-29T05:39:09.065Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16584","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：0"}
