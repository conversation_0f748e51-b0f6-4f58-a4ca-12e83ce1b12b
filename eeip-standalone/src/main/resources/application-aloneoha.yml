server:
  port: 8888
  tomcat:
    remote-ip-header: x-forward-for
    uri-encoding: UTF-8
    accept-count: 1000
    max-threads: 1000
    max-connections: 2000
    max-http-header-size: 8096
    #核心代码，设置tomcat的basedir
    basedir: ${user.home}/tomcat/tmp
  use-forward-headers: true
spring:
  application:
    name: eeip-standalone-service
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: ***********************************************************************************
    username: root
    password: BoojuxMysql
    driver-class-name: com.mysql.jdbc.Driver
  #redis配置
  redis:
    cluster:
      # redis 集群地址配置，和单机配置只要配置一个即可，不配置为空,集群配置优先
      #      nodes: redis1.xccloud.com:7001,redis1.xccloud.com:7004,redis2.xccloud.com:7002,redis2.xccloud.com:7005,redis3.xccloud.com:7003,redis3.xccloud.com:7006
      nodes:
      #redis 单机配置
      host: *************
      port: 6379
      passwd: xcwlkj2015
      timeOut: 5000
      #跨集群执行命令时要遵循的最大重定向数量
      max-redirects: 3
      #缓存注解(@Cacheable、@CacheEvict等)缓存的有效时间,默认180秒
      expire-time: 180
  servlet:
    multipart:
      max-file-size: 100MB #单个文件大小 Max file size默认1M
      max-request-size: 100MB #总上传的数据大小 Max request size默认10M
      enabled: true
  mail:
    host: smtp.126.com
    username: <EMAIL>
    password: windyac@123
    port: 465
    protocol: smtp
    test-connection: false
    default-encoding: UTF-8
    properties:
      mail:
        debug: true
        smtp:
          auth: true
          connectiontimeout: 10000
          timeout: 10000
          writetimeout: 10000
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465
          starttls:
            enable: true
            required: true
management:
  endpoints:
    web:
      exposure:
        include: '*'

#feign配置
feign:
  hystrix:
    enabled: true
  # feign 使用okhttp3 作为客户端连接
  httpclient:
    enabled: false
  okhttp:
    enabled: true
####超时配置####
hystrix:
  threadpool:
    default:
      coreSize: 50 #默认为10,基本得原则时保持线程池尽可能小，他主要是为了释放压力，防止资源被阻塞
      maxQueueSize: 1500 #最大排队长度。默认-1,不能动态调整
      #动态控制线程池队列的上限，即使maxQueueSize没有达到，��到queueSizeRejectionThreshold该值后，请求也会被拒绝，默认值5
      queueSizeRejectionThreshold: 1200
  command:
    default:
      execution:
        isolation:
          strategy: THREAD #HystrixCommand.run()的隔离策略，THREAD和SEMAPHORE
          thread:
            timeoutInMilliseconds: 230000
        timeout:
          enabled: true
ribbon:
  #请求处理的超时时间 下级服务响应最大时间,超出时间消费方（路由也是消费方）返回timeout,超时时间不可大于断路器的超时时间
  ReadTimeout: 230000
  #ribbon请求连接的超时时间- 限制5秒内必须请求到服务，并不限���服务处理的返回时间
  ConnectTimeout: 5000
  # 对所有的操作请求都进行重试，如果是get则可以，如果是post,put等操作没有实现幂等的情况下是很危险的，所以设置为false
  OkToRetryOnAllOperations: false
  #对当前实例的重试次数
  MaxAutoRetries: 1
  #切换实例的重试次数
  MaxAutoRetriesNextServer: 1
  # ribbon 使用okhttp3 作为客户端连接
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  ServerListRefreshInterval: 5000 #负载均衡器服务列表缓存
  eureka:
    enabled: true
###超时配置###
mybatis:
  type-aliases-package: com.xcwlkj.model.domain
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
mapper:
  #mappers: com.xcwlkj.base.mybatis.MyMapper
  mappers: tk.mybatis.mapper.common.Mapper
  not-empty: false
  identity: MYSQL

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

xc:
  enableSqlLogInterceptor: false
  zk:
    isOn: 1
    # zkAddressList: zk.xccloud.com:2181
    zkAddressList: **************:2181
  aliyun:
    key:
      accessKeyId: 'LTAI4GA1Zb6tDXqHeD4e38bu'
      accessKeySecret: '******************************'
    sms:
      signName: 骑客智能
      regionId: cn-hangzhou
      endpointName: cn-hangzhou
      product: 'Dysmsapi'
      domain: dysmsapi.aliyuncs.com
    oss:
      endpoint: http://oss-cn-hangzhou.aliyuncs.com
  local:
    oss:
      path: 'D:/Users/<USER>/Desktop/temp/'
      showUrl: 'D:/Users/<USER>/Desktop/temp/'
  hhjt: #黄河交通学院单点登录参数
    ptpath: http://*************:7000/dist/#/login #巡课系统地址
    caspath: https://cas-pass.zjtu.edu.cn/cas/ #新开普CAS地址
  xcDfs:
    prefixUrl: http://************:8811
    channel: ZJKSZHPT #渠道名
    serviceAddress: http://************:8811
    serviceInnerAddress: http://************:8811
    appId: e046e7c7e7fbf1f1a4183e00c76e0182
    appSecret: 6d0dc6d02af7bab1f1ed39a7baadbb55
    expiredMinute: 5
    fileObjectPrefix: ${xc.xcDfs.serviceAddress}/remote/dfs/
    filePathPrefix: ${xc.xcDfs.serviceAddress}/remote/file/
    fileObjectInnerPrefix: ${xc.xcDfs.serviceInnerAddress}/remote/dfs/
    filePathInnerPrefix: ${xc.xcDfs.serviceInnerAddress}/remote/file/
    distributeExpireMinute: 1440
    filePathReflectPrefix: http://************:8811/remote/file/
  yimei:
    key:
      appId: 'EUCP-EMY-SMS1-1QRXDxx'
      secretKey: '93D3C3FC87805350xx'
    ip:
      baseUrl: 'http://shmtn.b2m.cn:80'
      sendSingleSmsUrl: '/inter/sendSingleSMS'
  temp:
    path: '/Users/<USER>/Documents/bak/temp/'
    messageAddress: 'http://**************:8082/v2/sip/message/specification'
    webUrl: 'http://************:8000/upload/'
    generatePath: 'D:\Users\huangmengjian\Documents\zip'
  #rocketMQ 配置
  rocketMq:
    enable: true
    reliableMessageConsumer: false #消费者是否使用可靠消息, 默认不使用 @MqProducerStore
    reliableMessageProducer: true #生产者是否使用可靠消息, 默认不使用 @MqConsumerStore
    instanceName: pubc-service
    namesrvAddr: rocketmq.xccloud.com:9876 #服务地址
    consumerGroup: CID_BUSINESS #消费者组
    producerGroup: PID_BUSINESS #生产者组
  mock:
    isOpen: false
    url: http://*************:3000/mock/
  export:
    muban: D://muban
    tempPath: D://temp
  mqtt:
    url: tcp://*************:2883
    #url: tcp://**************:1883
    revTopics: /+/+/event/+
  wechat:
    wechatAppId: wxc2e047f38e8fa3f0
    wechatAppSecret: a1b5a0933f2fae4fcc6a5c1475147d6c
  tyjr:
    urlPrefix: http://*************:8844
    appId: RDbKcP5Z3mGwxeJB
    accessKey: SNTiZ8mHKKhWFhNFApFRPMtk
    accessJsonType: 2
  distribute:
    threadCounts: 8 # 下发任务工作线程数
  datax:
    urlPrefix: http://*************:19527/
    appId: jkpt
    appKey: 123456
    callback: http://x.x.x.x:9527/api/job/resultEcho # 回调地址
    logQuery:
      expire: 3600 # 任务日志查询超时时间
      frequency: 30 # 任务日志查询频率, 单位秒
    job-id-map:
      examJobIdMap:
        default:
          - 26
          - 27
          - 29
        bp:
          - 30
          - 32
        xs:
          - 28
        js:
          - 33
      eduJobIdMap:
        default:
        bp:
          - 34
        xs:
          - 35
        js:
          - 36
  rstj:
    cxsc: 20 #查询时长：查询当前时间前后x分钟时间段内检测数据；
  identityVerify:
    distributePkg:
      tokenNum: 10
      tokenSupplyIntervalSec: 10
      max_retry_count: 1
    #文件上传
    wjsc:
      fileName:
        ksbmxx: ks.db
        jkryjbxx: jky.db
        jkrybpxx: jkybp.db
        sjjkzp: jkryzp.db
        sjkszp: kszp.db
        sjpzxx: pzxx.db
    pkg:
      # 考生数据打包路径
      path: "f:/tmp"
      secret:
        enable: true
        key: gxhy@123
        algorithm: zip
      threadCounts: 16
    wgcl:
      mb:
        keys: wgclPrcl,wgclKzks
    #省平台身份验证信息
    hisomeProvicePlat:
      appid: gxsfhy
      secret: 8efc68adb41744d1810d2b6527ed32c5
      sm4Key: hssfhy@2025~$#@!

user:
  defaultPassword: jdpt12345
sso:
  appId: c52600c1a207496f8e553ccdcebb1c92
  appSecret: 64c6f8ceb95ec914e7d432ac587f6120
  url: https://**************
#xxl-job
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8180/xxl-job-admin
    executor:
      appname: executor-eeip-basic
      address:
      ip:
      port: 9998
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
    accessToken:

pj:
  jspm: #评价排名
    defaultScore: 75  #排名中的默认评分
    ddpfzb: 0.2 #督导评分占比
    jspfzb: 0.3 #同行教师评分占比
    xspfzb: 0.5 #学生评分占比

xk: #巡课
  sfzsyljt: 0  #是否展示预览截图

video:
  urlPrefix: http://*************:8844

file:
  tempPath: /Users/<USER>/Documents/bak/temp/

bq:
  muban: 'D:/bqMuban/' #标签导出pdf文件模板路径
  secret: false #标准化考点

HsFaceFeature:
  HsFaceFeatureCreatorPath: /mnt/data/eeip/HsFaceFeatureCreator
  HsFaceFeatureFilePath: /mnt/data/eeip/HsFaceFeatureFile