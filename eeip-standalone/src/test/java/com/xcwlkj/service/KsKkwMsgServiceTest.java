package com.xcwlkj.service;

import com.xcwlkj.config.MinimalDatabaseTestApplication;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwTjDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwXqDTO;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwTjRespModel;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwXqRespModel;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 空考位消息服务测试
 * <AUTHOR>
 * @version $Id: KsKkwMsgServiceTest.java, v 0.1 2025年07月29日 xcwlkj.com Exp $
 */
@SpringBootTest(classes = MinimalDatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class KsKkwMsgServiceTest {
    @Autowired
    private KsKkwMsgService ksKkwMsgService;

    /**
     * 测试空考位统计查询 - 正常情况
     */
    @Test
    public void testGetKkwTj_Normal() {
        // 准备测试数据
        KkwTjDTO dto = new KkwTjDTO();
        dto.setKsjhbh("TEST2025001");
        dto.setCcm("001");
        dto.setPageNum(1);
        dto.setPageSize(10);

        // 执行测试
        KkwTjRespModel result = ksKkwMsgService.getKkwTj(dto);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getPageInfo());
        assertNotNull(result.getSummary());
        assertTrue(result.getPageInfo().getPageNum() >= 1);
        assertTrue(result.getPageInfo().getPageSize() >= 1);
    }

    /**
     * 测试空考位统计查询 - 带上报状态筛选
     */
    @Test
    public void testGetKkwTj_WithReportStatus() {
        // 准备测试数据
        KkwTjDTO dto = new KkwTjDTO();
        dto.setKsjhbh("TEST2025001");
        dto.setCcm("001");
        dto.setSbzt("1"); // 已上报
        dto.setPageNum(1);
        dto.setPageSize(10);

        // 执行测试
        KkwTjRespModel result = ksKkwMsgService.getKkwTj(dto);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getPageInfo());
        assertNotNull(result.getSummary());
    }

    /**
     * 测试空考位详情查询 - 正常情况
     */
    @Test
    public void testGetKkwTjXq_Normal() {
        // 准备测试数据
        KkwXqDTO dto = new KkwXqDTO();
        dto.setKsjhbh("TEST2025001");
        dto.setCcm("001");
        dto.setPageNum(1);
        dto.setPageSize(10);

        // 执行测试
        KkwXqRespModel result = ksKkwMsgService.getKkwTjXq(dto);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getPageInfo());
        assertTrue(result.getPageInfo().getPageNum() >= 1);
        assertTrue(result.getPageInfo().getPageSize() >= 1);
    }

    /**
     * 测试空考位详情查询 - 带异常类型筛选
     */
    @Test
    public void testGetKkwTjXq_WithExceptionType() {
        // 准备测试数据
        KkwXqDTO dto = new KkwXqDTO();
        dto.setKsjhbh("TEST2025001");
        dto.setCcm("001");
        dto.setYclx("6"); // 缺考（空位）
        dto.setPageNum(1);
        dto.setPageSize(10);

        // 执行测试
        KkwXqRespModel result = ksKkwMsgService.getKkwTjXq(dto);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getPageInfo());
    }

    /**
     * 测试空考位详情查询 - 带准考证号筛选
     */
    @Test
    public void testGetKkwTjXq_WithZkzh() {
        // 准备测试数据
        KkwXqDTO dto = new KkwXqDTO();
        dto.setKsjhbh("TEST2025001");
        dto.setCcm("001");
        dto.setZkzh("202501001");
        dto.setPageNum(1);
        dto.setPageSize(10);

        // 执行测试
        KkwXqRespModel result = ksKkwMsgService.getKkwTjXq(dto);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getPageInfo());
    }

    /**
     * 测试参数校验 - 考试计划编号为空
     */
    @Test
    public void testGetKkwTj_EmptyKsjhbh() {
        // 准备测试数据
        KkwTjDTO dto = new KkwTjDTO();
        dto.setKsjhbh(""); // 空的考试计划编号
        dto.setPageNum(1);
        dto.setPageSize(10);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            ksKkwMsgService.getKkwTj(dto);
        });
    }
}
