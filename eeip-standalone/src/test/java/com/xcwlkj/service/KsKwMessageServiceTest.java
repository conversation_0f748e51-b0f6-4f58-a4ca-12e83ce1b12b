package com.xcwlkj.service;

import com.github.pagehelper.PageInfo;
import com.xcwlkj.config.MinimalDatabaseTestApplication;
import com.xcwlkj.identityverify.model.dto.cxtj.JjhjCxDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KwxxCxDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KwxxydxqDTO;
import com.xcwlkj.identityverify.model.vo.cxtj.JjhjCxVO;
import com.xcwlkj.identityverify.model.vo.cxtj.KwxxCxVO;
import com.xcwlkj.identityverify.model.vo.cxtj.KwxxydxqVO;
import com.xcwlkj.identityverify.service.KsKwMessageService;
import com.xcwlkj.standalone.StandaloneApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * 考务消息服务单元测试
 */
@SpringBootTest(classes = MinimalDatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class KsKwMessageServiceTest {

    @Resource
    private KsKwMessageService ksKwMessageService;

    @Test
    public void testKwxxcx_正常查询() {
        // 准备测试数据
        KwxxCxDTO kwxxCxDTO = new KwxxCxDTO();
        kwxxCxDTO.setKsjhbh("TEST001");
        kwxxCxDTO.setPageNum(1);
        kwxxCxDTO.setPageSize(10);
        
        // 执行测试
        PageInfo<KwxxCxVO> result = ksKwMessageService.kwxxcx(kwxxCxDTO);
        
        // 验证结果
        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());
        assertNotNull("总记录数不能为空", result.getTotal());
        assertTrue("页码应该大于0", result.getPageNum() > 0);
        assertTrue("每页大小应该大于0", result.getPageSize() > 0);
    }

    @Test
    public void testKwxxcx_分页参数校正() {
        // 测试分页参数自动校正
        KwxxCxDTO kwxxCxDTO = new KwxxCxDTO();
        kwxxCxDTO.setKsjhbh("TEST001");
        kwxxCxDTO.setPageNum(0); // 无效页码
        kwxxCxDTO.setPageSize(-1); // 无效页大小
        
        PageInfo<KwxxCxVO> result = ksKwMessageService.kwxxcx(kwxxCxDTO);
        
        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());
        // 验证分页参数被自动校正
        assertTrue("页码应该被校正为正数", result.getPageNum() >= 1);
        assertTrue("每页大小应该被校正为正数", result.getPageSize() >= 1);
    }

    @Test
    public void testKwxxcx_时间范围查询() {
        // 测试时间范围查询
        KwxxCxDTO kwxxCxDTO = new KwxxCxDTO();
        kwxxCxDTO.setKsjhbh("TEST001");
        kwxxCxDTO.setPageNum(1);
        kwxxCxDTO.setPageSize(10);
//        kwxxCxDTO.setFbksrq(new Date(System.currentTimeMillis() - 86400000)); // 昨天
//        kwxxCxDTO.setFbjsrq(new Date(System.currentTimeMillis() + 86400000)); // 明天
        
        PageInfo<KwxxCxVO> result = ksKwMessageService.kwxxcx(kwxxCxDTO);
        
        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());
        // 验证时间范围过滤效果
        for (KwxxCxVO vo : result.getList()) {
            assertNotNull("消息ID不能为空", vo.getMsgId());
            assertNotNull("发布人不能为空", vo.getFbr());
            assertNotNull("消息内容不能为空", vo.getXxnr());
        }
    }

    @Test
    public void testKwxxydxq_正常查询() {
        // 首先查询一个消息
        KwxxCxDTO kwxxCxDTO = new KwxxCxDTO();
        kwxxCxDTO.setKsjhbh("TEST001");
        kwxxCxDTO.setPageNum(1);
        kwxxCxDTO.setPageSize(1);
        
        PageInfo<KwxxCxVO> messageResult = ksKwMessageService.kwxxcx(kwxxCxDTO);
        
        if (messageResult.getList() != null && !messageResult.getList().isEmpty()) {
            String msgId = messageResult.getList().get(0).getMsgId();
            
            // 准备测试数据
            KwxxydxqDTO ydxqDTO = new KwxxydxqDTO();
            ydxqDTO.setMsgId(msgId);
            ydxqDTO.setPageNum(1);
            ydxqDTO.setPageSize(10);
            
            // 执行测试
            PageInfo<KwxxydxqVO> result = ksKwMessageService.kwxxydxq(ydxqDTO);
            
            // 验证结果
            assertNotNull("查询结果不能为空", result);
            assertNotNull("查询结果列表不能为空", result.getList());
            assertNotNull("总记录数不能为空", result.getTotal());
            
            // 验证结果数据
            for (KwxxydxqVO vo : result.getList()) {
                assertNotNull("考场号不能为空", vo.getKch());
                assertNotNull("状态不能为空", vo.getZt());
                assertNotNull("状态名称不能为空", vo.getZtmc());
                assertTrue("状态应该是0或1", "0".equals(vo.getZt()) || "1".equals(vo.getZt()));
                assertTrue("状态名称应该是未读或已读", "未读".equals(vo.getZtmc()) || "已读".equals(vo.getZtmc()));
            }
        }
    }

    @Test
    public void testKwxxydxq_状态过滤() {
        // 首先查询一个消息
        KwxxCxDTO kwxxCxDTO = new KwxxCxDTO();
        kwxxCxDTO.setKsjhbh("TEST001");
        kwxxCxDTO.setPageNum(1);
        kwxxCxDTO.setPageSize(1);
        
        PageInfo<KwxxCxVO> messageResult = ksKwMessageService.kwxxcx(kwxxCxDTO);
        
        if (messageResult.getList() != null && !messageResult.getList().isEmpty()) {
            String msgId = messageResult.getList().get(0).getMsgId();
            
            // 测试查询已读状态
            KwxxydxqDTO ydxqDTO = new KwxxydxqDTO();
            ydxqDTO.setMsgId(msgId);
            ydxqDTO.setZt("1"); // 查询已读
            ydxqDTO.setPageNum(1);
            ydxqDTO.setPageSize(10);
            
            PageInfo<KwxxydxqVO> result = ksKwMessageService.kwxxydxq(ydxqDTO);
            
            assertNotNull("查询结果不能为空", result);
            assertNotNull("查询结果列表不能为空", result.getList());
            
            // 验证状态过滤效果
            for (KwxxydxqVO vo : result.getList()) {
                assertEquals("状态应该是已读", "1", vo.getZt());
                assertEquals("状态名称应该是已读", "已读", vo.getZtmc());
            }
        }
    }

    @Test(expected = RuntimeException.class)
    public void testKwxxydxq_消息ID为空() {
        KwxxydxqDTO ydxqDTO = new KwxxydxqDTO();
        ydxqDTO.setMsgId(""); // 空消息ID
        ydxqDTO.setPageNum(1);
        ydxqDTO.setPageSize(10);
        
        ksKwMessageService.kwxxydxq(ydxqDTO);
    }

    @Test(expected = RuntimeException.class)
    public void testKwxxydxq_消息ID为null() {
        KwxxydxqDTO ydxqDTO = new KwxxydxqDTO();
        ydxqDTO.setMsgId(null); // null消息ID
        ydxqDTO.setPageNum(1);
        ydxqDTO.setPageSize(10);
        
        ksKwMessageService.kwxxydxq(ydxqDTO);
    }

    @Test(expected = RuntimeException.class)
    public void testKwxxydxq_消息不存在() {
        KwxxydxqDTO ydxqDTO = new KwxxydxqDTO();
        ydxqDTO.setMsgId("NONEXISTENT"); // 不存在的消息ID
        ydxqDTO.setPageNum(1);
        ydxqDTO.setPageSize(10);
        
        ksKwMessageService.kwxxydxq(ydxqDTO);
    }

    @Test
    public void testKwxxydxq_分页参数校正() {
        // 首先查询一个消息
        KwxxCxDTO kwxxCxDTO = new KwxxCxDTO();
        kwxxCxDTO.setKsjhbh("TEST001");
        kwxxCxDTO.setPageNum(1);
        kwxxCxDTO.setPageSize(1);
        
        PageInfo<KwxxCxVO> messageResult = ksKwMessageService.kwxxcx(kwxxCxDTO);
        
        if (messageResult.getList() != null && !messageResult.getList().isEmpty()) {
            String msgId = messageResult.getList().get(0).getMsgId();
            
            // 测试分页参数自动校正
            KwxxydxqDTO ydxqDTO = new KwxxydxqDTO();
            ydxqDTO.setMsgId(msgId);
            ydxqDTO.setPageNum(0); // 无效页码
            ydxqDTO.setPageSize(-1); // 无效页大小
            
            PageInfo<KwxxydxqVO> result = ksKwMessageService.kwxxydxq(ydxqDTO);
            
            assertNotNull("查询结果不能为空", result);
            assertNotNull("查询结果列表不能为空", result.getList());
            // 验证分页参数被自动校正
            assertTrue("页码应该被校正为正数", result.getPageNum() >= 1);
            assertTrue("每页大小应该被校正为正数", result.getPageSize() >= 1);
        }
    }

    @Test
    public void testQueryJjhjList_正常查询() {
        // 准备测试数据
        JjhjCxDTO jjhjCxDTO = new JjhjCxDTO();
        jjhjCxDTO.setKsjhbh("TEST001");
        jjhjCxDTO.setPageNum(1);
        jjhjCxDTO.setPageSize(10);

        // 执行测试
        PageInfo<JjhjCxVO> result = ksKwMessageService.queryJjhjList(jjhjCxDTO);

        // 验证结果
        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());
        assertNotNull("总记录数不能为空", result.getTotal());
        assertTrue("页码应该大于0", result.getPageNum() > 0);
        assertTrue("每页大小应该大于0", result.getPageSize() > 0);
    }

    @Test
    public void testQueryJjhjList_按考试计划查询() {
        // 准备测试数据
        JjhjCxDTO jjhjCxDTO = new JjhjCxDTO();
        jjhjCxDTO.setKsjhbh("TEST001");
        jjhjCxDTO.setPageNum(1);
        jjhjCxDTO.setPageSize(10);

        // 执行测试
        PageInfo<JjhjCxVO> result = ksKwMessageService.queryJjhjList(jjhjCxDTO);

        // 验证结果
        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());

        // 验证返回数据中ksjhbh匹配
        for (JjhjCxVO vo : result.getList()) {
            assertNotNull("消息ID不能为空", vo.getId());
            assertNotNull("发布时间不能为空", vo.getFbsj());
            assertNotNull("考场号不能为空", vo.getKcbh());
            assertNotNull("紧呼内容不能为空", vo.getJjhjnr());
            assertNotNull("处理状态不能为空", vo.getClzt());
            assertTrue("处理状态应该是0或1", "0".equals(vo.getClzt()) || "1".equals(vo.getClzt()));
        }
    }

    @Test
    public void testQueryJjhjList_按考场号查询() {
        // 准备测试数据
        JjhjCxDTO jjhjCxDTO = new JjhjCxDTO();
        jjhjCxDTO.setKsjhbh("TEST001");
        jjhjCxDTO.setKcbh("001");
        jjhjCxDTO.setPageNum(1);
        jjhjCxDTO.setPageSize(10);

        // 执行测试
        PageInfo<JjhjCxVO> result = ksKwMessageService.queryJjhjList(jjhjCxDTO);

        // 验证结果
        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());

        // 验证返回数据中kcbh包含查询条件
        for (JjhjCxVO vo : result.getList()) {
            assertTrue("考场号应该包含查询条件", vo.getKcbh().contains("001"));
        }
    }

    @Test
    public void testQueryJjhjList_按处理状态查询() {
        // 测试查询未处理状态
        JjhjCxDTO jjhjCxDTO = new JjhjCxDTO();
        jjhjCxDTO.setKsjhbh("TEST001");
        jjhjCxDTO.setClzt("0"); // 未处理
        jjhjCxDTO.setPageNum(1);
        jjhjCxDTO.setPageSize(10);

        // 执行测试
        PageInfo<JjhjCxVO> result = ksKwMessageService.queryJjhjList(jjhjCxDTO);

        // 验证结果
        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());

        // 验证返回数据中hfnr为空（未处理）
        for (JjhjCxVO vo : result.getList()) {
            assertEquals("处理状态应该是未处理", "0", vo.getClzt());
            assertTrue("回复内容应该为空", vo.getHfnr() == null || vo.getHfnr().isEmpty());
        }
    }

    @Test
    public void testQueryJjhjList_分页功能测试() {
        // 准备测试数据
        JjhjCxDTO jjhjCxDTO = new JjhjCxDTO();
        jjhjCxDTO.setKsjhbh("TEST001");
        jjhjCxDTO.setPageNum(1);
        jjhjCxDTO.setPageSize(10);

        // 执行测试
        PageInfo<JjhjCxVO> result = ksKwMessageService.queryJjhjList(jjhjCxDTO);

        // 验证分页信息正确
        assertNotNull("查询结果不能为空", result);
//        assertEquals("页码应该正确", Integer.valueOf(1), result.getPageNum());
//        assertEquals("页面大小应该正确", Integer.valueOf(10), result.getPageSize());
        assertTrue("总记录数应该大于等于0", result.getTotal() >= 0);
    }

    @Test
    public void testQueryJjhjList_分页参数校正() {
        // 测试分页参数自动校正
        JjhjCxDTO jjhjCxDTO = new JjhjCxDTO();
        jjhjCxDTO.setKsjhbh("TEST001");
        jjhjCxDTO.setPageNum(0); // 无效页码
        jjhjCxDTO.setPageSize(-1); // 无效页大小

        PageInfo<JjhjCxVO> result = ksKwMessageService.queryJjhjList(jjhjCxDTO);

        assertNotNull("查询结果不能为空", result);
        assertNotNull("查询结果列表不能为空", result.getList());
        // 验证分页参数被自动校正
        assertTrue("页码应该被校正为正数", result.getPageNum() >= 1);
        assertTrue("每页大小应该被校正为正数", result.getPageSize() >= 1);
    }
}