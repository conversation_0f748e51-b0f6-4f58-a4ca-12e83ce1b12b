package com.xcwlkj.junit;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * 事务回滚测试示例
 * 继承TransactionalDatabaseTest，测试方法会在事务中执行，测试完成后自动回滚
 */
public class TransactionalDatabaseTestExample extends TransactionalDatabaseTest {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 测试数据库事务回滚
     * 此测试会执行数据修改操作，但由于使用了@Transactional，测试完成后会自动回滚
     */
    @Test
    public void testTransactionalRollback() {
        logStep("开始测试事务回滚功能");
        
        // 查询当前数据
        String sql = "SELECT COUNT(*) FROM sys_user";
        int countBefore = jdbcTemplate.queryForObject(sql, Integer.class);
        logStep("当前用户数量: " + countBefore);
        
        // 执行插入操作
        logStep("执行插入操作");
        String insertSql = "INSERT INTO sys_user (username, password, name, email, phone, status) " +
                "VALUES ('test_user', 'password123', '测试用户', '<EMAIL>', '13800138000', 1)";
        
        try {
            jdbcTemplate.execute(insertSql);
            logStep("插入操作执行成功");
            
            // 再次查询数据
            int countAfter = jdbcTemplate.queryForObject(sql, Integer.class);
            logStep("插入后用户数量: " + countAfter);
            
            // 验证数据已插入
            if (countAfter != countBefore + 1) {
                throw new AssertionError("插入操作未生效，期望数量: " + (countBefore + 1) + ", 实际数量: " + countAfter);
            }
            
            logStep("数据已成功插入，测试完成后将自动回滚");
        } catch (Exception e) {
            logStep("执行SQL时发生错误: " + e.getMessage());
            // 如果是表不存在等错误，可以在这里处理
            logStep("请确保数据库中存在sys_user表，或修改测试代码使用已存在的表");
        }
    }
}
