package com.xcwlkj.junit;

import com.xcwlkj.config.MinimalDatabaseTestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

/**
 * Zookeeper排除测试
 * 验证Zookeeper相关组件是否被正确排除
 */
@SpringBootTest(classes = MinimalDatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class ZookeeperExclusionTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void testZookeeperExclusion() {
        System.out.println("=== 测试Zookeeper组件排除 ===");
        
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        
        // 检查是否有Zookeeper相关的Bean
        boolean hasZookeeperBeans = false;
        System.out.println("检查Zookeeper相关Bean:");
        
        for (String beanName : beanNames) {
            if (beanName.toLowerCase().contains("zookeeper") || 
                beanName.toLowerCase().contains("zk") ||
                beanName.contains("ZookeeperInitRunner")) {
                
                System.out.println("发现Zookeeper相关Bean: " + beanName);
                hasZookeeperBeans = true;
                
                try {
                    Object bean = applicationContext.getBean(beanName);
                    System.out.println("  类型: " + bean.getClass().getName());
                } catch (Exception e) {
                    System.out.println("  获取失败: " + e.getMessage());
                }
            }
        }
        
        if (!hasZookeeperBeans) {
            System.out.println("✓ 没有发现Zookeeper相关Bean，排除成功");
        } else {
            System.out.println("✗ 仍然存在Zookeeper相关Bean");
        }
    }

    @Test
    public void testCommandLineRunners() {
        System.out.println("=== 检查CommandLineRunner Bean ===");
        
        try {
            // 获取所有CommandLineRunner类型的Bean
            String[] runnerBeans = applicationContext.getBeanNamesForType(org.springframework.boot.CommandLineRunner.class);
            
            System.out.println("找到的CommandLineRunner Bean数量: " + runnerBeans.length);
            
            for (String beanName : runnerBeans) {
                try {
                    Object bean = applicationContext.getBean(beanName);
                    System.out.println("- " + beanName + ": " + bean.getClass().getName());
                    
                    // 检查是否是ZookeeperInitRunner
                    if (bean.getClass().getName().contains("ZookeeperInitRunner")) {
                        System.out.println("  ⚠️  发现ZookeeperInitRunner，但应该被Mock替代");
                    } else if (bean.getClass().getName().contains("MockZookeeperInitRunner")) {
                        System.out.println("  ✓ Mock的ZookeeperInitRunner，正常");
                    }
                    
                } catch (Exception e) {
                    System.out.println("- " + beanName + ": [获取失败] " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.err.println("获取CommandLineRunner失败: " + e.getMessage());
        }
    }

    @Test
    public void testZookeeperConfiguration() {
        System.out.println("=== 检查Zookeeper配置 ===");
        
        try {
            // 检查是否有xc.zk.isOn配置
            String zkIsOn = applicationContext.getEnvironment().getProperty("xc.zk.isOn");
            System.out.println("xc.zk.isOn 配置值: " + zkIsOn);
            
            if ("0".equals(zkIsOn)) {
                System.out.println("✓ Zookeeper已通过配置禁用");
            } else {
                System.out.println("⚠️  Zookeeper配置未禁用，值为: " + zkIsOn);
            }
            
        } catch (Exception e) {
            System.err.println("检查Zookeeper配置失败: " + e.getMessage());
        }
    }

}
