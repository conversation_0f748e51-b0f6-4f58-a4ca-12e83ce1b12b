package com.xcwlkj.test.example;

import com.google.gson.Gson;
import com.xcwlkj.config.MinimalDatabaseTestApplication;
import com.xcwlkj.identityverify.model.dto.ksgl.CxjkrylbDTO;
import com.xcwlkj.identityverify.model.vo.ksgl.CxjkrylbVO;
import com.xcwlkj.identityverify.service.KsJkryJbxxService;
import com.xcwlkj.test.BaseDatabaseTest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = MinimalDatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class KsJkryJbxxServiceTest {
    @Autowired
    private ApplicationContext applicationContext;
    private KsJkryJbxxService ksJkryJbxxService;
    Gson gson = new Gson();
    @Before
    public void startUp(){
        ksJkryJbxxService = applicationContext.getBean(KsJkryJbxxService.class);
    }
    @Test
    public void query_invigilator_list_with_pic(){
//        {
//    "ksjhbh": "23122911394301850704626798610432",
//    "pageNum": 1,
//    "pageSize": 78
//}
        CxjkrylbDTO cxjkrylbDTO = new CxjkrylbDTO();
        cxjkrylbDTO.setKsjhbh("23122911394301850704626798610432");
        cxjkrylbDTO.setPageNum(1);
        cxjkrylbDTO.setPageSize(78);
        CxjkrylbVO cxjkrylb = ksJkryJbxxService.cxjkrylb(cxjkrylbDTO);
        System.out.println(gson.toJson(cxjkrylb));
    }
}
