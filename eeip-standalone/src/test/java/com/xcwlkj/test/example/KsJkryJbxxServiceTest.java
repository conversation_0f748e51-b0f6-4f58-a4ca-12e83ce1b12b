package com.xcwlkj.test.example;

import com.google.gson.Gson;
import com.xcwlkj.config.MinimalDatabaseTestApplication;
import com.xcwlkj.identityverify.model.dto.ksgl.CxjkrylbDTO;
import com.xcwlkj.identityverify.model.vo.ksgl.CxjkrylbVO;
import com.xcwlkj.identityverify.service.KsJkryJbxxService;
import org.aspectj.lang.annotation.Before;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = MinimalDatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class KsJkryJbxxServiceTest {
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private KsJkryJbxxService ksJkryJbxxService;
    Gson gson = new Gson();
    @Test
    public void query_invigilator_list_with_pic(){
        CxjkrylbDTO cxjkrylbDTO = new CxjkrylbDTO();
        cxjkrylbDTO.setKsjhbh("2507211439231396864138952572928");
        cxjkrylbDTO.setPageNum(1);
        cxjkrylbDTO.setPageSize(78);
        CxjkrylbVO cxjkrylb = ksJkryJbxxService.cxjkrylb(cxjkrylbDTO);
        System.out.println(gson.toJson(cxjkrylb));
    }
}
