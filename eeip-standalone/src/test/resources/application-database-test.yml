# 数据库测试专用配置
# 只包含数据库测试必需的配置，其他功能全部禁用

spring:
  application:
    name: eeip-database-test

  # 数据库配置
  datasource:
    url: ***********************************************************************************
    username: root
    password: BoojuxMysql
    driver-class-name: com.mysql.jdbc.Driver
    # 连接池配置 - 测试环境使用较小的连接池
    hikari:
      maximum-pool-size: 5
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置 - 测试环境禁用真实Redis连接
  # 使用Mock的RedisUtil，不需要真实的Redis服务
  redis:
    enabled: false  # 禁用Redis自动配置

  # 允许bean定义覆盖
  main:
    allow-bean-definition-overriding: true
    # 禁用Web环境（如果不需要Web功能）
    web-application-type: none

  # 禁用所有不需要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration
      - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration
      - org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration
      - org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration
      - org.springframework.cloud.openfeign.FeignAutoConfiguration

  # 禁用Spring Cloud组件
  cloud:
    discovery:
      enabled: false
    config:
      enabled: false
    loadbalancer:
      enabled: false
    openfeign:
      enabled: false

  # 禁用定时任务
  task:
    scheduling:
      enabled: false

  # 禁用消息队列
  rabbitmq:
    enabled: false

# 禁用所有外部服务和组件
mqtt:
  enabled: false

feign:
  client:
    enabled: false

eureka:
  client:
    enabled: false
    register-with-eureka: false
    fetch-registry: false

# 禁用定时任务
xxl:
  job:
    admin:
      addresses: ""  # 空值，不加载XxlJobConfig
    accessToken: ""
# 基础配置
user:
  defaultPassword: jdpt12345

# 最小化的xc配置 - 只保留测试必需的部分
xc:
  # Mock的文件服务配置
  local:
    oss:
      path: /tmp/test-oss/
      showUrl: /tmp/test-oss/
  temp:
    path: '/Users/<USER>/Documents/bak/temp/'
    messageAddress: 'http://**************:8082/v2/sip/message/specification'
    webUrl: 'http://************:8000/upload/'
    generatePath: 'D:\Users\huangmengjian\Documents\zip'
  # 身份验证相关配置（测试需要）
  identityVerify:
    # 省平台身份验证信息
    hisomeProvicePlat:
      appid: gxsfhy
      secret: 8efc68adb41744d1810d2b6527ed32c5
      sm4Key: hssfhy@2025~$#@!
    pkg:
      # 考生数据打包路径
      path: "D:/tmp"
      secret:
        enable: true
        key: gxhy@123
        algorithm: zip
      threadCounts: 16
    distributePkg:
      tokenNum: 10
      tokenSupplyIntervalSec: 10
      max_retry_count: 1
    wgcl:
      mb:
        keys: wgclPrcl,wgclKzks
  xcDfs:
    prefixUrl: http://************:8811
    channel: ZJKSZHPT #渠道名
    serviceAddress: http://************:8811
    serviceInnerAddress: http://************:8811
    appId: e046e7c7e7fbf1f1a4183e00c76e0182
    appSecret: 6d0dc6d02af7bab1f1ed39a7baadbb55
    expiredMinute: 5
    fileObjectPrefix: ${xc.xcDfs.serviceAddress}/remote/dfs/
    filePathPrefix: ${xc.xcDfs.serviceAddress}/remote/file/
    fileObjectInnerPrefix: ${xc.xcDfs.serviceInnerAddress}/remote/dfs/
    filePathInnerPrefix: ${xc.xcDfs.serviceInnerAddress}/remote/file/
    distributeExpireMinute: 1440
    filePathReflectPrefix: http://************:8811/remote/file/
  zk:
    isOn: 0  # 禁用Zookeeper

# 日志配置 - 测试环境简化日志输出
logging:
  level:
    root: WARN
    com.xcwlkj: INFO
    org.springframework: WARN
    org.springframework.cloud: ERROR
    org.springframework.boot: WARN
    com.zaxxer.hikari: WARN
    org.mybatis: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# MyBatis配置
mybatis:
  mapper-locations: classpath*:mapper/*.xml
#  type-aliases-package: com.xcwlkj.*.model.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    lazy-loading-enabled: false
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000
    jdbc-type-for-null: "NULL"

# Mapper配置（tk.mybatis）
mapper:
  mappers: com.xcwlkj.identityverify.config.commonMapper.CustomMapper
  not-empty: false
  identity: MYSQL

# 禁用Zookeeper相关功能

# 测试环境的临时文件路径
file:
  tempPath: /tmp/test-temp/

HsFaceFeature:
  HsFaceFeatureCreatorPath: /mnt/data/eeip/HsFaceFeatureCreator
  HsFaceFeatureFilePath: /mnt/data/eeip/HsFaceFeatureFile