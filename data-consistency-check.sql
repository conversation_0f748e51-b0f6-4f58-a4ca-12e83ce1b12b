-- 数据一致性检查脚本
-- 用于验证测试数据中各表之间的关联关系是否正确

-- ========================================
-- 1. 检查空考位消息与考生入场信息的一致性
-- ========================================

-- 1.1 检查缺考（空位）类型的数据一致性
SELECT 
    '缺考空位一致性检查' as 检查项,
    msg.ks_zkzh as 准考证号,
    msg.ljkch as 逻辑考场号,
    msg.ks_bpzwh as 座位号,
    ksrc.ksxm as 考生姓名,
    ksrc.sfrc as 是否入场,
    CASE 
        WHEN ksrc.zkzh IS NULL THEN '考生信息不存在'
        WHEN ksrc.sfrc = '0' THEN '数据一致-未入场'
        WHEN ksrc.sfrc = '1' THEN '数据不一致-已入场但报缺考'
        ELSE '状态异常'
    END as 一致性状态
FROM ks_kkw_msg msg
LEFT JOIN ks_ksrcxx ksrc ON msg.ks_zkzh = ksrc.zkzh 
    AND msg.ksjhbh = ksrc.ksjhbh 
    AND ksrc.sczt = '0'
WHERE msg.ksjhbh = 'TEST2025001' 
    AND msg.rcbz = '6'  -- 缺考（空位）
    AND msg.sczt = '0'
ORDER BY msg.ljkch, msg.ks_bpzwh;

-- 1.2 检查考场信息的一致性
SELECT 
    '考场信息一致性检查' as 检查项,
    msg.ljkch as 空考位消息考场,
    msg.kch as 考场号,
    kc.ljkcbh as 考场表逻辑考场号,
    kc.kcbh as 考场表考场编号,
    CASE 
        WHEN kc.ljkcbh IS NULL THEN '考场信息不存在'
        WHEN msg.ljkch = kc.ljkcbh AND msg.kch = kc.kcbh THEN '数据一致'
        ELSE '数据不一致'
    END as 一致性状态
FROM ks_kkw_msg msg
LEFT JOIN ks_kcxx kc ON msg.ljkch = kc.ljkcbh 
    AND msg.ksjhbh = kc.ksjhbh 
    AND kc.sczt = '0'
WHERE msg.ksjhbh = 'TEST2025001' 
    AND msg.sczt = '0'
ORDER BY msg.ljkch;

-- ========================================
-- 2. 检查移动终端上报与考场信息的一致性
-- ========================================

-- 2.1 检查标准上报（BZ）的考场信息一致性
SELECT 
    '移动终端标准上报一致性检查' as 检查项,
    ydsb.ljkcbh as 上报逻辑考场号,
    ydsb.kcbh as 上报考场号,
    kc.ljkcbh as 考场表逻辑考场号,
    kc.kcbh as 考场表考场编号,
    ydsb.sbkszs as 上报考生数,
    CASE 
        WHEN kc.ljkcbh IS NULL THEN '考场信息不存在'
        WHEN ydsb.ljkcbh = kc.ljkcbh AND ydsb.kcbh = kc.kcbh THEN '数据一致'
        ELSE '数据不一致'
    END as 一致性状态
FROM ks_ydsb_sbkszs ydsb
LEFT JOIN ks_kcxx kc ON ydsb.ljkcbh = kc.ljkcbh 
    AND ydsb.ksjhbh = kc.ksjhbh 
    AND kc.sczt = '0'
WHERE ydsb.ksjhbh = 'TEST2025001' 
    AND ydsb.sblx = 'BZ'  -- 标准上报
ORDER BY ydsb.ljkcbh;

-- ========================================
-- 3. 检查考场与教室信息的一致性
-- ========================================

SELECT 
    '考场教室信息一致性检查' as 检查项,
    kc.kcbh as 考场编号,
    kc.bzhkcid as 考场标准化ID,
    js.bzhkcid as 教室标准化ID,
    kc.bzhkcmc as 考场名称,
    js.jsmc as 教室名称,
    CASE 
        WHEN js.bzhkcid IS NULL THEN '教室信息不存在'
        WHEN kc.bzhkcid = js.bzhkcid THEN '数据一致'
        ELSE '数据不一致'
    END as 一致性状态
FROM ks_kcxx kc
LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid 
    AND js.sczt = '0'
WHERE kc.ksjhbh = 'TEST2025001' 
    AND kc.sczt = '0'
ORDER BY kc.kcbh;

-- ========================================
-- 4. 统计各表数据量
-- ========================================

SELECT 
    '数据量统计' as 统计项,
    (SELECT COUNT(*) FROM ks_kcxx WHERE ksjhbh = 'TEST2025001' AND sczt = '0') as 考场数量,
    (SELECT COUNT(*) FROM cs_jsjbxx WHERE bzhkcid LIKE 'TEST%' AND sczt = '0') as 教室数量,
    (SELECT COUNT(*) FROM ks_ksrcxx WHERE ksjhbh = 'TEST2025001' AND sczt = '0') as 考生入场记录数,
    (SELECT COUNT(*) FROM ks_kkw_msg WHERE ksjhbh = 'TEST2025001' AND sczt = '0') as 空考位消息数,
    (SELECT COUNT(*) FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001') as 移动终端上报数;

-- ========================================
-- 5. 检查数据的业务逻辑一致性
-- ========================================

-- 5.1 检查未入场考生是否都有对应的空考位消息
SELECT 
    '未入场考生空考位消息检查' as 检查项,
    ksrc.zkzh as 准考证号,
    ksrc.ksxm as 考生姓名,
    ksrc.ljkcbh as 逻辑考场号,
    ksrc.zwh as 座位号,
    CASE 
        WHEN msg.ks_zkzh IS NOT NULL THEN '已有空考位消息'
        ELSE '缺少空考位消息'
    END as 消息状态
FROM ks_ksrcxx ksrc
LEFT JOIN ks_kkw_msg msg ON ksrc.zkzh = msg.ks_zkzh 
    AND ksrc.ksjhbh = msg.ksjhbh 
    AND msg.rcbz = '6'  -- 缺考（空位）
    AND msg.sczt = '0'
WHERE ksrc.ksjhbh = 'TEST2025001' 
    AND ksrc.sfrc = '0'  -- 未入场
    AND ksrc.sczt = '0'
ORDER BY ksrc.ljkcbh, ksrc.zwh;

-- 5.2 检查已入场考生是否有异常的空考位消息
SELECT 
    '已入场考生异常消息检查' as 检查项,
    ksrc.zkzh as 准考证号,
    ksrc.ksxm as 考生姓名,
    ksrc.ljkcbh as 逻辑考场号,
    ksrc.zwh as 座位号,
    msg.rcbz as 异常类型,
    CASE msg.rcbz
        WHEN '1' THEN '误识别'
        WHEN '2' THEN '坐错他人位置'
        WHEN '3' THEN '实际未参加考试'
        WHEN '4' THEN '他人坐错位置'
        WHEN '5' THEN '人工核验'
        WHEN '6' THEN '缺考（空位）-异常'
        WHEN '7' THEN '无编排'
        ELSE '未知类型'
    END as 异常描述
FROM ks_ksrcxx ksrc
INNER JOIN ks_kkw_msg msg ON ksrc.zkzh = msg.ks_zkzh 
    AND ksrc.ksjhbh = msg.ksjhbh 
    AND msg.sczt = '0'
WHERE ksrc.ksjhbh = 'TEST2025001' 
    AND ksrc.sfrc = '1'  -- 已入场
    AND ksrc.sczt = '0'
ORDER BY ksrc.ljkcbh, ksrc.zwh;

-- ========================================
-- 6. 检查时间逻辑的一致性
-- ========================================

-- 6.1 检查空考位消息时间与考生入场时间的逻辑性
SELECT 
    '时间逻辑一致性检查' as 检查项,
    msg.ks_zkzh as 准考证号,
    ksrc.ksxm as 考生姓名,
    ksrc.rcsj as 入场时间,
    msg.timestamp as 空考位消息时间,
    CASE 
        WHEN ksrc.rcsj IS NULL AND msg.rcbz = '6' THEN '逻辑正确-未入场产生缺考消息'
        WHEN ksrc.rcsj IS NOT NULL AND msg.timestamp > ksrc.rcsj THEN '逻辑正确-入场后产生异常消息'
        WHEN ksrc.rcsj IS NOT NULL AND msg.timestamp <= ksrc.rcsj THEN '时间逻辑异常'
        ELSE '需要进一步检查'
    END as 时间逻辑状态
FROM ks_kkw_msg msg
LEFT JOIN ks_ksrcxx ksrc ON msg.ks_zkzh = ksrc.zkzh 
    AND msg.ksjhbh = ksrc.ksjhbh 
    AND ksrc.sczt = '0'
WHERE msg.ksjhbh = 'TEST2025001' 
    AND msg.sczt = '0'
ORDER BY msg.ljkch, msg.ks_bpzwh;

-- ========================================
-- 7. 汇总检查结果
-- ========================================

SELECT 
    '数据一致性检查汇总' as 检查结果,
    '测试数据已修正，确保以下一致性：' as 说明1,
    '1. 缺考空位消息对应未入场考生' as 说明2,
    '2. 考场信息在各表中保持一致' as 说明3,
    '3. 教室与考场的标准化ID匹配' as 说明4,
    '4. 移动终端上报的考场信息正确' as 说明5,
    '5. 时间逻辑符合业务规则' as 说明6;
