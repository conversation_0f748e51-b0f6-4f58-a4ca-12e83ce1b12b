package com.xcwlkj.cache;

import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存（key-value）抽象
 *
 * <AUTHOR> shark
 * @date 2022/1/19 10:38
 */
@Slf4j
public abstract class AbstractRedisDataCache<K, V> implements ApplicationRunner {
    @Autowired
    private RedisUtil redisUtil;


    // 缓存自动刷新定期器，用于缓存定时刷新，不依赖前台访问
    private final static ScheduledExecutorService cacheAutoRefreshScheduler = Executors.newScheduledThreadPool(5);

    private static String cacheWriteKeys = "cacheWriteKeys";

    private static String cacheWriteTimeKey = "cacheWriteTime";

    private static String keySplit = "~|~";

    /**
     * 缓存写入键的Key
     *
     * @return
     */
    private String getCacheWriteKeys(String keyStr) {
        return getHashKey() + cacheWriteKeys + keySplit + keyStr;
    }

    /**
     * 缓存创建时间Key
     *
     * @param keyStr
     * @return
     */
    private String getCacheWriteTimeKey(String keyStr) {
        return getHashKey() + cacheWriteTimeKey + keySplit + keyStr;
    }

    /**
     * 保存缓存Key
     *
     * @param key
     */
    private void saveCacheKeys(K key) {
        updateCacheKeys(key, true);
    }

    /**
     * 移除缓存Key
     *
     * @param key
     */
    private void removeCacheKeys(K key) {
        updateCacheKeys(key, false);
    }

    /**
     * 更新缓存Key
     *
     * @param key
     * @param addOrElseRemove true：增加；false：移除
     */
    private void updateCacheKeys(K key, boolean addOrElseRemove) {
        String keyStr = JSONObject.toJSONString(key);
        if (addOrElseRemove) {
            if (isAutoRefresh()) {
                redisUtil.set(getCacheWriteKeys(keyStr), key.getClass().getTypeName());
            } else {
                redisUtil.set(getCacheWriteKeys(keyStr), key.getClass().getTypeName(), getDuration());
            }
        } else {
            redisUtil.del(getCacheWriteKeys(keyStr));
        }
    }

    /**
     * 读取缓存，没有缓存则存入缓存
     *
     * @param key
     * @return
     */
    public V get(K key) {
        try {
            //缓存key不存在时，实时查询，并存入缓存
            String keyStr = JSONObject.toJSONString(key);
            if (!redisUtil.exists(getRealKey(keyStr))) {
                V v = this.write(key);
                if (v == null) {
                    return null;
                }
                this.saveCache(key, v);
                return v;
            }
            Object value = redisUtil.get(getRealKey(keyStr));
            return value != null ? (V) value : null;
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            log.warn("get cache error, now through the cache.", e);
            return this.write(key);
        }
    }

    /**
     * 获取缓存，如果缓存存在，否则返回null
     *
     * @param key
     * @return
     */
    public V getIfPresent(K key) {
        String keyStr = JSONObject.toJSONString(key);
        Object value = redisUtil.get(getRealKey(keyStr));
        return value != null ? (V) value : null;
    }

    /**
     * 刷新缓存
     */
    public void refresh() {
        List<String> cacheKeys = redisUtil.redisKeys(getHashKey() + cacheWriteKeys + "*");
        for (String cacheKey : cacheKeys) {
            try {
                String keyStr = cacheKey.split(keySplit)[2];
                long writeTime = (long) redisUtil.get(getCacheWriteTimeKey(keyStr));
                long now = System.currentTimeMillis();
                if (now - writeTime < getDuration() * 800) {
                    // 考虑多节点部署，此处做简单处理：最近写入缓存时间距今小于约定刷新时间的4/5，将不刷新
                    log.debug("refresh cache for key {} not happen because duration too short.", getRealKey(keyStr));
                    continue;
                }
                log.debug("refresh cache for key {}", getRealKey(keyStr));
                Class keyClazz = Class.forName((String) redisUtil.get(cacheKey));
                this.refresh((K) JSONObject.parseObject(keyStr, keyClazz));
            } catch (Exception e) {
                log.error("refresh cache error for key {} : {}", cacheKey, e.getMessage());
            }

        }
    }

    /**
     * 根据key刷新缓存
     *
     * @param key
     */
    public void refresh(K key) {
        log.debug("[{}]refresh cache for key {}", getHashKey(), key);
        V v = this.write(key);
        if (v == null) {
            return;
        }
        this.saveCache(key, v);
    }

    /**
     * 保存到redis缓存
     *
     * @param key
     * @param v
     */
    private void saveCache(K key, V v) {
        long createTime = System.currentTimeMillis();
        String keyStr = JSONObject.toJSONString(key);
        if (isAutoRefresh()) {
            redisUtil.set(getRealKey(keyStr), v);
            redisUtil.set(getCacheWriteTimeKey(keyStr), createTime);
        } else {
            redisUtil.set(getRealKey(keyStr), v, getDuration());
            redisUtil.set(getCacheWriteTimeKey(keyStr), createTime, getDuration());
        }

        this.saveCacheKeys(key);
    }

    /**
     * 清空缓存
     */
    public void cleanup() {
        List<String> cacheKeys = redisUtil.redisKeys(getHashKey() + cacheWriteKeys + "*");
        for (String cacheKey : cacheKeys) {
            try {
                String keyStr = cacheKey.split(keySplit)[2];
                Class keyClazz = Class.forName((String) redisUtil.get(cacheKey));
                cleanup((K) JSONObject.parseObject(keyStr, keyClazz));
                log.debug("cleanup cache for key[{}] finished", cacheKey);
            } catch (Exception e) {
                log.error("cleanup cache error for key {} : {}", cacheKey, e.getMessage());
            }
        }
        log.debug("[{}]cleanup cache finished", getHashKey());
    }

    /**
     * 根据key清空缓存
     *
     * @param key
     */
    public void cleanup(K key) {
        String keyStr = JSONObject.toJSONString(key);
        redisUtil.del(getRealKey(keyStr), getCacheWriteTimeKey(keyStr));
        this.removeCacheKeys(key);
        log.debug("[{}]cleanup cache finished", getRealKey(keyStr));
    }

    /**
     * 初始化
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (isAutoRefresh()) {
            ScheduledFuture<?> future = cacheAutoRefreshScheduler.scheduleAtFixedRate(() -> {
                refresh();
            }, getDuration(), getDuration(), TimeUnit.SECONDS);
        }
    }


    /**
     * 实例类名，用于hash key和日志打印
     *
     * @return
     */
    private String getHashKey() {
        return this.getClass().getSimpleName();
    }

    /**
     * 实际的key
     * hashKey + itemKey
     * @return
     */
    private String getRealKey(String itemKey) {
        return getHashKey() + ":" + itemKey;
    }


    /**
     * 缓存刷新/超时间隔时间，单位s
     *
     * @return
     */
    protected abstract long getDuration();

    /**
     * 缓存是否定时刷新，为true时将不会超时失效
     *
     * @return
     */
    protected abstract boolean isAutoRefresh();

    /**
     * 写入缓存的具体方法实现
     *
     * @param key key对象需实现equals和toString方法
     * @return
     */
    protected abstract V write(K key);


}
