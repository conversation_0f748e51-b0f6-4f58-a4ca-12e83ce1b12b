{"移动终端上报考生总数测试用例": {"表名": "ks_ydsb_sbkszs", "描述": "移动终端设备上报考生总数的测试数据和验证用例", "测试数据概览": {"总记录数": 14, "标准上报记录": 12, "非标准上报记录": 2, "已上报记录": 8, "未上报记录": 6}, "数据验证查询": [{"查询名称": "按场次统计上报情况", "SQL": "SELECT ccm as 场次, sblx as 上报类型, COUNT(*) as 记录数, SUM(sbkszs) as 总考生数, COUNT(CASE WHEN report_flag = '1' THEN 1 END) as 已上报数 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' GROUP BY ccm, sblx ORDER BY ccm, sblx", "预期结果": [{"场次": "001", "上报类型": "BZ", "记录数": 6, "总考生数": 144, "已上报数": 5}, {"场次": "001", "上报类型": "FB", "记录数": 1, "总考生数": 150, "已上报数": 1}, {"场次": "002", "上报类型": "BZ", "记录数": 6, "总考生数": 74, "已上报数": 2}, {"场次": "002", "上报类型": "FB", "记录数": 1, "总考生数": 75, "已上报数": 1}]}, {"查询名称": "查询已上报的标准设备记录", "SQL": "SELECT sbxlh as 设备序列号, kcbh as 考场号, sbkszs as 考生数, DATE_FORMAT(sbsj, '%Y-%m-%d %H:%i:%s') as 上报时间 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' AND sblx = 'BZ' AND report_flag = '1' ORDER BY sbsj", "预期结果": [{"设备序列号": "SN001001", "考场号": "101", "考生数": 25, "上报时间": "2025-07-29 08:45:00"}, {"设备序列号": "SN001002", "考场号": "102", "考生数": 26, "上报时间": "2025-07-29 08:46:00"}]}, {"查询名称": "查询移动设备上报记录", "SQL": "SELECT sbxlh as 设备序列号, ccm as 场次, sbkszs as 考生总数, DATE_FORMAT(sbsj, '%Y-%m-%d %H:%i:%s') as 上报时间 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' AND sblx = 'FB' ORDER BY ccm", "预期结果": [{"设备序列号": "MOBILE001", "场次": "001", "考生总数": 150, "上报时间": "2025-07-29 09:20:00"}, {"设备序列号": "MOBILE002", "场次": "002", "考生总数": 75, "上报时间": "2025-07-29 11:20:00"}]}, {"查询名称": "查询异常情况记录（考生数为0）", "SQL": "SELECT sbxlh as 设备序列号, kcbh as 考场号, ljkcbh as 逻辑考场号, sbkszs as 考生数, report_flag as 上报标志 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' AND sbkszs = 0", "预期结果": [{"设备序列号": "SN001006", "考场号": "204", "逻辑考场号": "LJ204", "考生数": 0, "上报标志": "0"}, {"设备序列号": "SN002004", "考场号": "304", "逻辑考场号": "LJ304", "考生数": 0, "上报标志": "0"}]}, {"查询名称": "统计各考场上报率", "SQL": "SELECT kcbh as 考场号, COUNT(*) as 总上报次数, COUNT(CASE WHEN report_flag = '1' THEN 1 END) as 成功上报次数, ROUND(COUNT(CASE WHEN report_flag = '1' THEN 1 END) * 100.0 / COUNT(*), 2) as 上报成功率 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' AND sblx = 'BZ' AND kcbh IS NOT NULL GROUP BY kcbh ORDER BY kcbh", "预期结果": [{"考场号": "101", "总上报次数": 2, "成功上报次数": 2, "上报成功率": 100.0}, {"考场号": "102", "总上报次数": 2, "成功上报次数": 2, "上报成功率": 100.0}, {"考场号": "202", "总上报次数": 1, "成功上报次数": 0, "上报成功率": 0.0}]}], "业务场景测试": [{"场景名称": "考试开始前设备预检", "描述": "考试开始前，各考场设备上报初始考生数量", "涉及记录": ["TEST_YDSB011", "TEST_YDSB012"], "验证点": ["设备能正常上报考生数量", "上报时间在考试开始前", "考生数量符合预期范围"]}, {"场景名称": "考试进行中实时上报", "描述": "考试进行过程中，设备实时上报考生入场情况", "涉及记录": ["TEST_YDSB001", "TEST_YDSB002", "TEST_YDSB003", "TEST_YDSB004"], "验证点": ["上报时间在考试时间段内", "考生数量有合理变化", "上报成功率达标"]}, {"场景名称": "移动设备巡检上报", "描述": "监考人员使用移动设备进行全场次考生统计", "涉及记录": ["TEST_YDSB009", "TEST_YDSB010"], "验证点": ["移动设备能正常上报", "统计数据覆盖整个场次", "与固定设备数据形成对比"]}, {"场景名称": "设备故障异常处理", "描述": "设备出现故障时的异常数据处理", "涉及记录": ["TEST_YDSB013", "TEST_YDSB014"], "验证点": ["异常数据能被正确记录", "考生数为0的情况能被识别", "未上报状态正确标记"]}, {"场景名称": "数据上报重试机制", "描述": "网络异常时的数据上报重试", "涉及记录": ["TEST_YDSB005", "TEST_YDSB007", "TEST_YDSB008"], "验证点": ["未上报数据能被正确标识", "重试机制能正常工作", "数据完整性得到保证"]}], "数据质量检查": [{"检查项": "数据完整性", "检查SQL": "SELECT COUNT(*) as 总记录数, COUNT(CASE WHEN ksjhbh IS NULL THEN 1 END) as 缺失考试计划, COUNT(CASE WHEN sbxlh IS NULL THEN 1 END) as 缺失设备序列号 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001'", "预期结果": {"总记录数": 14, "缺失考试计划": 0, "缺失设备序列号": 0}}, {"检查项": "时间逻辑性", "检查SQL": "SELECT COUNT(*) as 异常时间记录数 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' AND (sbsj > report_time OR (report_flag = '1' AND report_time IS NULL))", "预期结果": {"异常时间记录数": 0}}, {"检查项": "上报类型分布", "检查SQL": "SELECT sblx as 上报类型, COUNT(*) as 记录数 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' GROUP BY sblx", "预期结果": [{"上报类型": "BZ", "记录数": 12}, {"上报类型": "FB", "记录数": 2}]}, {"检查项": "考生数量合理性", "检查SQL": "SELECT MIN(sbkszs) as 最小考生数, MAX(sbkszs) as 最大考生数, AVG(sbkszs) as 平均考生数 FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001' AND sbkszs > 0", "预期结果": {"最小考生数": 24, "最大考生数": 150, "平均考生数": "约50"}}], "性能测试建议": [{"测试项": "批量插入性能", "描述": "测试大量设备同时上报时的插入性能", "建议": "模拟100个设备同时上报，验证插入耗时"}, {"测试项": "查询性能", "描述": "测试按不同条件查询时的响应时间", "建议": "在大数据量下测试按场次、设备、时间范围查询的性能"}, {"测试项": "并发上报", "描述": "测试多个设备并发上报时的数据一致性", "建议": "使用多线程模拟并发上报，验证数据不会丢失或重复"}], "清理脚本": {"描述": "测试完成后清理测试数据", "SQL": "DELETE FROM ks_ydsb_sbkszs WHERE ksjhbh = 'TEST2025001';"}}}