/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.dto.lesson;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 查询今明课表dto
 * <AUTHOR>
 * @version $Id: TodayAndTomorrowDTO.java, v 0.1 2024年04月07日 15时29分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TodayAndTomorrowDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 当前时间 */
    @NotBlank(message = "当前时间不能为空")
    private String time;
    /** 教室号 */
    @NotBlank(message = "教室号不能为空")
    private String jsh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
