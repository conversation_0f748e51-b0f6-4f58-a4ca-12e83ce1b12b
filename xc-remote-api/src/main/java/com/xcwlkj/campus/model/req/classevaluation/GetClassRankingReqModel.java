/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.req.classevaluation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 班级评价-班级排名请求
 * <AUTHOR>
 * @version $Id: GetClassRankingReqModel.java, v 0.1 2025年05月21日 10时00分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetClassRankingReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 模板编号 */
    @NotBlank(message = "模板编号不能为空")
    private String mbbh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}