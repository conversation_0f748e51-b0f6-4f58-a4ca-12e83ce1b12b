/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.req.classhonor;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 学生荣誉列表请求
 * <AUTHOR>
 * @version $Id: XsrylistReqModel.java, v 0.1 2025年06月12日 14时31分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class XsrylistReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 班级教室号 */
    @NotBlank(message = "班级教室号不能为空")
    private String bjjsh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}