/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.dto.jtxxdz;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 1-根据设备编号查询截图数据dto
 * <AUTHOR>
 * @version $Id: GjsbbhcxjtsjDTO.java, v 0.1 2022年10月18日 14时19分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GjsbbhcxjtsjDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 信息编号列表 */
    @NotNull(message = "信息编号列表不能为空")
    private List<XxbhlbItemDTO> xxbhlb;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
